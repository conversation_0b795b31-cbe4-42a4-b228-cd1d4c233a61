export namespace IqcOrder {
  export interface Item {
    orderNo: string;
    produceDate: Date;
    materialName: string;
  }
  interface IQueryParams {
    statsType: number; //0=日,1=周,2=月,3=季度,4=年
    statsDateStart: string;
    statsDateEnd: string;
    plant: string;
  }
  interface PassRate {
    dateLabel: string;
    passRate: number;
  }
  interface Dppm {
    dateLabel: string;
    itemList: {
      materialType: string;
      defectCount: number;
      sampleQty: number;
      dppm: number;
      target: string;
    }[];
    itemList: {
      materialType: string;
      defectCount: number;
      sampleQty: number;
      dppm: number;
      target: string;
    }[];
  }
  interface SupplierDefect {
    supplierName: string;
    defectRate: number;
  }
  interface materialPassRate {
    dateLabel: string;
    itemList: {
      materialType: string;
      passRate: number;
    }[];
  }
  interface MaterialTimelyRate {
    dateLabel: string;
    itemList: {
      materialType: string;
      timelyRate: number;
    }[];
  }
  interface StatsInspection {
    onTimeBatches: number; //已检验批次
    lateBatches: number; //不及时检验批次
    totalBatches: number; //送检批次
  }
  interface listParam {
    startDate: Date;
    endDate: Date;
  }
  interface refreshTime {
    refreshValues: string;
    tabChangeValues: string;
  }
}
