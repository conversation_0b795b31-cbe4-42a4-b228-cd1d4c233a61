<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="{ selectedListIds }">
        <el-button v-auth="'stop-line-line-suspension:export'" type="primary" @click="downloadFile(selectedListIds as number[])">
          {{ $t("导出") }}
        </el-button>
        <el-button v-auth="'stop-line-line-suspension:add'" type="primary" @click="openLineSuspensionModal('新增')">{{
          $t("新增")
        }}</el-button>
        <el-button v-auth="'stop-line-line-suspension:edit'" type="primary" @click="openLineSuspensionModal('编辑')">{{
          $t("编辑")
        }}</el-button>
        <el-button v-auth="'stop-line-line-suspension:delete'" type="danger" @click="batchDelete(selectedListIds as number[])">{{
          $t("删除")
        }}</el-button>
      </template>
    </ProTable>
    <LineSuspensionModal ref="LineSuspensionModalRef" />
    <LineSuspensionViewModal ref="LineSuspensionViewModalRef" />
  </div>
</template>

<script setup lang="tsx" name="stop-line-line-suspension">
import {
  getLineSuspensionList,
  createLineSuspension,
  editLineSuspension,
  deleteLineSuspension,
  exportLineSuspension
} from "@/api/modules/stop-line/line_suspension";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import LineSuspensionModal from "./components/LineSuspensionModal.vue";
import LineSuspensionViewModal from "../line-suspension/components/LineSuspensionViewModal.vue";
import { download } from "@/api/modules/common";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAdminDict, useDict } from "@/hooks/useDict";
import { LineSuspension } from "@/typings/stop-line/line_suspension";
import { isEmpty, isEmptyObj } from "@/utils/is";
import DateRange from "../../components/DateRange.vue";
import { ref, reactive, computed, h } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { ApprovalStatus, UrgentDegree } from "@/enums/statusStopLine";
import LinkModal from "../../components/LinkModal.vue";
import useUserStore from "@/stores/modules/user";
import { SuperRoleId } from "@/enums/status";
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const LineSuspensionModalRef = ref<InstanceType<typeof LineSuspensionModal> | null>(null);
const LineSuspensionViewModalRef = ref<InstanceType<typeof LineSuspensionViewModal> | null>(null);

let queryParams = reactive<LineSuspension.IQueryParams>({} as LineSuspension.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");
const { sl_dept } = useDict("sl_dept");

// 表格配置项
const columns = reactive<ColumnProps<LineSuspension.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "orderNo",
    label: "停机单号",
    width: 160,
    search: { el: "input", order: 35 },
    render({ row }) {
      return <LinkModal name={row.orderNo} row={row} onOpenDialog={openPreview} />;
    }
  },
  {
    prop: "plant",
    label: "工厂",
    width: 120,
    enum: factory,
    fieldNames: { label: "label", value: "label" },
    search: { order: 20, el: "select", props: { filterable: true } }
  },
  {
    prop: "orderStatus",
    label: "状态",
    width: 120,
    tag: true,
    enum: ApprovalStatus,
    search: { el: "select", order: 80 },
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "applicationDept", label: "申请部门", width: 120, search: { el: "input", order: 30 } },
  {
    prop: "applicationDate",
    label: "申请日期",
    width: 120,
    search: {
      order: 10,
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  {
    prop: "urgentDegree",
    label: "紧急程度",
    width: 120,
    tag: true,
    enum: UrgentDegree,
    search: { el: "select", order: 90 },
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "carNo",
    label: "CAR编号",
    width: 120,
    render({ row }) {
      if (row.carNo) {
        const carNoUrl = import.meta.env.VITE_CAR_NO_URL;
        const fullUrl = `${carNoUrl}/problem?mode=view&no=${row.carNo}`;
        return h(
          "a",
          {
            href: fullUrl,
            target: "_blank",
            rel: "noopener noreferrer",
            style: "color: #409eff; text-decoration: underline;"
          },
          row.carNo
        );
      }
      return "";
    }
  },
  { prop: "cqeName", label: "CQE", width: 120 },
  { prop: "applicantNo", label: "申请人工号", width: 120 },
  { prop: "applicantName", label: "申请人", width: 120, search: { el: "input", order: 60 } },
  { prop: "productNo", label: "产品型号", width: 180, search: { el: "input", order: 50 } },
  { prop: "workCenter", label: "工作中心", width: 120, search: { el: "input", order: 40 } },
  { prop: "batchQty", label: "批量数量", width: 120 },
  { prop: "ngQty", label: "不良数量", width: 120 },
  {
    prop: "ngRatio",
    label: "不良率",
    width: 120,
    render({ row }) {
      if (parseFloat(row.ngRatio) >= 0) {
        return `${row.ngRatio}%`;
      }
      return "";
    }
  },
  { prop: "responsibleStaffName", label: "指定责任人", width: 120 },
  { prop: "createBy", label: "创建者", width: 120 },
  { prop: "createAt", label: "创建时间", width: 165 },
  { prop: "updateBy", label: "更新者", width: 120 },
  { prop: "updateAt", label: "更新时间", width: 165 }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }

  queryParams = reactive(condition);
  return getLineSuspensionList({
    condition,
    pageNum,
    pageSize
  });
};

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const batchDelete = async (id?: number | number[]) => {
  const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  const statusCheck =
    proTable.value?.selectedList?.some((item: { orderStatus: string }) => item.orderStatus !== "待提交") || false;
  if (statusCheck) {
    return ElMessage.error(t("非待提交状态的申请单无法删除"));
  }

  if (!userInfo.value.roleId.includes(SuperRoleId)) {
    const hasNonSelf =
      proTable.value?.selectedList?.some((item: { applicantNo: string }) => item.applicantNo !== userInfo.value.jobNum) || false;
    if (hasNonSelf) {
      return ElMessage.error(t("无法删除他人创建的申请"));
    }
  }

  await useHandleData(deleteLineSuspension, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 导出
const downloadFile = async (ids?: number[]) => {
  const params = { ...queryParams, ...(!isEmpty(ids) ? { ids } : {}) };
  ElMessageBox.confirm(t("确认导出数据?"), t("提示"), { type: "warning" }).then(() =>
    download(exportLineSuspension(params as any))
  );
};
const openPreview = (row: Partial<LineSuspension.Item> = {}) => {
  openLineSuspensionModal("查看", row);
};
const openLineSuspensionModal = (title: string, row: Partial<LineSuspension.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }

  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };
  if (title === "编辑") {
    if (!userInfo.value.roleId.includes(SuperRoleId) && form.applicantNo !== userInfo.value.jobNum) {
      return ElMessage.error(t("无权限编辑"));
    }
  }
  const params = {
    title,
    isApproval: false,
    isView: title === "查看",
    factory: factory.value,
    sl_dept: sl_dept.value,
    UrgentDegree,
    form,
    api: title === "新增" ? createLineSuspension : title === "编辑" ? editLineSuspension : undefined,
    updateApi: editLineSuspension,
    getTableList: proTable.value?.getTableList
  };
  LineSuspensionModalRef.value?.acceptParams(params);
};
</script>
