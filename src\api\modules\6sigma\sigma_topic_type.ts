import { ReqPage, ResPage } from "@/api/interface/index";
import { SigmaTopicType } from "@/typings/6sigma/sigma_topic_type";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/6sigmaTopicType`;

// 列表
export const getSigmaTopicTypeList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTopicType.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSigmaTopicTypeDetail = (id: number) => {
  return http.post<SigmaTopicType.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSigmaTopicType = (data: SigmaTopicType.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSigmaTopicType = (data: SigmaTopicType.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSigmaTopicType = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSigmaTopicType = (params?: SigmaTopicType.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSigmaTopicType = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSigmaTopicTypeTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

//通过课题类型查节点计划map
export const getSigmaTopicNodes = (params: { topicType: string }) => {
  return http.post<SigmaTopicType.Item>(`${baseUrl}/getList`, params);
};
