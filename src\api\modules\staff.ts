import { ReqPage, ResPage } from "@/api/interface/index";
import { Staff } from "@/typings/staff";
import http from "@/api";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/sys_staff`;
export const getStaffList = (params?: ReqPage) => {
  return http.get<ResPage<Staff.Item>>(`${baseUrl}/list`, params);
};

export const getStaffAll = () => {
  return http.get<Staff.Item[]>(`${baseUrl}/all`, {}, { loading: false });
};
export const getStaffFilterList = (params?: ReqPage) => {
  return http.get<ResPage<Staff.Item>>(`${baseUrl}/filter`, params);
};

export const getStaffFilterByQualityPersonList = (params?: ReqPage) => {
  return http.post<ResPage<Staff.qualityPersonItem>>(`${API_PREFIX}/qualityPersonOrder/list`, params);
};
export const getStaffDetail = (roleId: number) => {
  return http.get<Staff.Item>(`${baseUrl}/${roleId}`);
};

export const createStaff = (data: Staff.Item) => {
  return http.post(`${baseUrl}`, data);
};

export const editStaff = (data: Staff.Item) => {
  return http.put(`${baseUrl}`, data);
};

export const deleteStaff = (data: { ids: number[] }) => {
  return http.delete(`${baseUrl}`, data);
};

export const exportStaff = (ids?: number[]) => {
  return http.post(`${baseUrl}/export`, { ids });
};
export const auditStaff = (data: { status: "0" | "1" | "2"; rowId: number[]; reason?: string }) => {
  return http.put(`${baseUrl}/audit`, data);
};

export const editPassword = (data: { password: string; oldPassword: string }) => {
  return http.put(`${baseUrl}/password`, data);
};

export const updateStaffLangCode = (data: { code: string }) => {
  return http.put<Staff.Item>(`${baseUrl}/update_lang_code`, data);
};
