export namespace LineSuspension {
  export interface Item {
    id: number;
    plant: string;
    orderNo: string;
    applicationDept: string;
    applicationDate: string;
    applicantNo: string;
    applicantName: string;
    applicantEmail: string;
    productNo: string;
    workCenter: string;
    batchQty: string;
    ngQty: string;
    ngRatio: string;
    carNo: string;
    cqeNo: string;
    cqeName: string;
    cqeEmail: string;
    abnormalNo: string;
    responsibleDept: string;
    responsibleStaffNo: string;
    responsibleStaffName: string;
    responsibleStaffEmail: string;
    suspensionAt: string;
    suspensionReason: string;
    notifyDeptStr: string;
    orderStatus: string;
    rejectReason: string;
    isRecovery: string;
    recoveryAt: string;
    improvePolicy: string;
    urgentDegree: string;
    notRestartReason: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  interface IQueryParams {
    startDate: string;
    endDate: string;
  }

  export interface notifyDeptStr {
    deptName: string;
    index: number;
    orderId: number;
    responsibleStaffEmail: string;
    responsibleStaffName: string;
    responsibleStaffNo: string;
  }

  export interface Log {
    actionName: string;
    actionStatus: number;
    isReject: number;
    changeLog: string;
    createBy: string;
    createAt: string;
    startDate: string;
    endDate: string;
  }
}
