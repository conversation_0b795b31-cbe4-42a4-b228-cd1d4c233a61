import { ReqPage, ResPage } from "@/api/interface/index";
import { InternalAuditPlan } from "@/typings/internal-audit/internal_audit_plan";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/InternalAuditPlan`;
// 列表
export const getInternalAuditPlanList = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditPlan.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getInternalAuditPlanDetail = (id: number) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createInternalAuditPlan = (data: InternalAuditPlan.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editInternalAuditPlan = (data: InternalAuditPlan.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteInternalAuditPlan = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportInternalAuditPlan = (params?: InternalAuditPlan.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importInternalAuditPlan = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importInternalAuditPlanTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 取消
export const cancelAuditPlan = (params: InternalAuditPlan.Item) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/cancelRequest`, params);
};

// 退回
export const backAuditPlan = (params: { id: number; rejectReason: string }) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/reject`, params);
};

// 延期
export const postponeAuditPlan = (params: InternalAuditPlan.Item) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/postponeRequest`, params);
};

// 审批通过
export const auditAuditPlan = (id: number) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getAuditPlanApproveList = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditPlan.Item>>(`${baseUrl}/listToBeAudited`, params);
};
//延期列表
export const getListToBePostponed = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditPlan.Item>>(`${baseUrl}/listToBePostponed`, params);
};
//取消列表
export const getListToBeCanceled = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditPlan.Item>>(`${baseUrl}/listToBeCanceled`, params);
};

// 延期通过
export const postpone = (id: number) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/postpone/${id}`);
};

// 取消通过
export const cancel = (id: number) => {
  return http.post<InternalAuditPlan.Item>(`${baseUrl}/cancel/${id}`);
};
