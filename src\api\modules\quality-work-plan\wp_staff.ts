import { ReqPage, ResPage } from "@/api/interface/index";
import { WpStaff } from "@/typings/quality-work-plan/wp_staff";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/quality-work-plan/statistics";

const baseUrl = `${API_PREFIX}/wpStaff`;

// 列表
export const getWpStaffList = (params?: ReqPage) => {
  return http.post<ResPage<WpStaff.Item>>(`${baseUrl}/list`, params);
};

// 待分解列表
export const getListToBeDecomposed = (params?: ReqPage) => {
  return http.post<ResPage<WpStaff.Item>>(`${baseUrl}/listToBeDecomposed`, params);
};
export const listByDeptPlanId = (params?: ReqPage) => {
  return http.post<ResPage<WpStaff.Item>>(`${baseUrl}/listByDeptPlanId`, params);
};
// 详情
export const getWpStaffDetail = (id: number) => {
  return http.post<WpStaff.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createWpStaff = (data: WpStaff.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 克隆个人计划
export const cloneStaffPlan = (data: WpStaff.Item) => {
  return http.post(`${baseUrl}/cloneStaffPlan`, data);
};

//分解个人计划
export const decomposeTask = (data: WpStaff.Item) => {
  return http.post(`${baseUrl}/decomposeTask`, data);
};
// 修改
export const editWpStaff = (data: WpStaff.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

export const modifyDecomposeTask = (data: WpStaff.Item) => {
  return http.post(`${baseUrl}/modifyDecomposeTask`, data);
};

// 删除
export const deleteWpStaff = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 提交
export const submitWpStaff = (ids: number[]) => {
  return http.post(`${baseUrl}/submit`, { ids });
};
//分解集团计划-提交
export const submitDecomposeTask = (params: { ids: number[]; groupPlanId: number }) => {
  return http.post(`${baseUrl}/submitDecomposeTask`, params);
};

// 导出
export const exportWpStaff = (params?: WpStaff.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importWpStaff = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importWpStaffTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
//绩效汇总
export const statsStaffSummary = (params: Statistics.IQueryStaffParams) => {
  return http.post<Statistics.OverviewList>(`${API_PREFIX}/wpStatsStaff/statsStaffSummary`, params);
};
//绩效分布-左侧树状图
export const statsPerformanceTree = (params: Statistics.IQueryStaffParams) => {
  return http.post<Statistics.TreeList>(`${API_PREFIX}/wpStatsStaff/statsPerformanceTree`, params);
};
//绩效分布-右侧列表
export const statsPerformanceList = (params: Statistics.IQueryStaffParams) => {
  return http.post<Statistics.DataList>(`${API_PREFIX}/wpStatsStaff/statsPerformanceList`, params);
};
//绩效分布-导出数据
export const exportList = (params?: WpStaff.IQueryParams) => {
  return http.post(`${API_PREFIX}/wpStatsStaff/exportList`, params);
};
//绩效分布-个人任务达成率
export const statsStaffPassedRatio = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsGroupPassedRatio>(`${API_PREFIX}/wpStatsStaff/statsStaffPassedRatio`, params);
};
//绩效分布-个人任务状态分布
export const statsStaffReportStatus = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsGroupReportStatus>(`${API_PREFIX}/wpStatsStaff/statsStaffReportStatus`, params);
};
//绩效分布-个人绩效得分柱状图
export const statsStaffPerformanceBar = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsStaffPerformanceBar>(`${API_PREFIX}/wpStatsStaff/statsStaffPerformanceBar`, params);
};
//绩效分布-个人KPI绩效得分雷达图
export const statsStaffKpiRadar = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsStaffPerformanceBar>(`${API_PREFIX}/wpStatsStaff/statsStaffKpiRadar`, params);
};

export const statsHome = (params: Statistics.IQueryHomeParams) => {
  return http.post<Statistics.Home>(`${API_PREFIX}/wpStatsHome/statsHome`, params);
};

export const getStatsHomeList = (params?: ReqPage) => {
  return http.post<ResPage<Statistics.Home>>(`${API_PREFIX}/wpStatsHome/statsHomeList`, params);
};
