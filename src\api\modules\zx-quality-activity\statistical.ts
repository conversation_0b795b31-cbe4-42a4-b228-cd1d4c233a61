import { API_PREFIX, yqjBaseUrl, zxQualityActivityBaseUrl } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}${zxQualityActivityBaseUrl}/statistical`;

import http from "@/api";

export type Item = {
  group: string;
  data: { label: string; value: number }[];
  x: string[];
  y: number[];
};

export const getPlanCount = (data: any) => {
  return http.post<ZxQualityActivityDashboard.CardData[]>(`${baseUrl}/plan-count`, data);
};

export const getFactoryStatusCount = (data: any) => {
  return http.post<ZxQualityActivityDashboard.FactoryStatusCount[]>(`${baseUrl}/factory-status-count`, data);
};

export const getTaskCompleteRate = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/task-complete-rate`, data);
};

export const getOverBudgetRate = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/over-budget-rate`, data);
};
