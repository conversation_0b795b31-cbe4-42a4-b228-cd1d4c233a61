import { ref } from "vue";
import { pullCustomer } from "@/api/modules/customer-demand/customer_demand";
import { CustomerDemand } from "@/typings/customer-demand/customer_demand";

export const customerDemandList = ref<CustomerDemand.CustomerItem[]>([]);
export const getCustomerDemand = async () => {
  try {
    const { success, data } = await pullCustomer();
    if (success) {
      customerDemandList.value = data || [];
    }
  } catch (error) {
    console.error("Error fetching work center list:", error);
  }
};
