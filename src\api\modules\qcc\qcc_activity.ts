import { ReqPage, ResPage } from "@/api/interface/index";
import { QccActivity } from "@/typings/qcc/qcc_activity";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/activity`;

// 列表
export const getQccActivityList = (params?: ReqPage) => {
  return http.post<ResPage<QccActivity.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQccActivityDetail = (id: number) => {
  return http.post<QccActivity.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQccActivity = (data: QccActivity.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQccActivity = (data: QccActivity.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQccActivity = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQccActivity = (params?: QccActivity.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQccActivity = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQccActivityTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
