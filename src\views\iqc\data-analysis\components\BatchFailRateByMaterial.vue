<template>
  <div class="table-box bg-white py-2 flex-1 flex flex-col">
    <div class="w-full text-center text-1xl font-bold">不良物料大类(TOP5)</div>
    <!-- 明确设置了高度的Echarts容器 -->
    <div class="grid w-full gap-6 mb-[30px]" style="height: 350px">
      <ECharts :option="option" autoresize />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { DataAnalysis } from "@/typings/iqc/data_analysis";
import { ECOption } from "@/components/ECharts/config";

const colorMap = {
  barColor: "#5470c6",
  axisColor: "#639FD2"
};

type Props = {
  chartData: DataAnalysis.BatchFailRateByMaterial[];
  searchParams: DataAnalysis.IQueryParams;
};

const props = defineProps<Props>();

// 排序后的数据（从大到小）
const sortedData = computed(() => {
  if (!props.chartData || props.chartData.length === 0) return [];

  return [...props.chartData].sort((a, b) => b.defectRate - a.defectRate).slice(0, 5);
});

// 将API返回的数据转换为echarts需要的格式
const seriesData = computed(() => {
  if (sortedData.value.length === 0) return [];

  // 为了让最大值显示在顶部，需要反转数据
  const reversedData = [...sortedData.value].reverse();

  return [
    {
      name: "不良率",
      type: "bar",
      itemStyle: {
        color: colorMap.barColor
      },
      data: reversedData.map(item => item.defectRate),
      label: {
        show: true,
        position: "right",
        formatter: (params: any) => `${params.value}%`
      }
    }
  ];
});

// 提取物料类型作为Y轴（水平条形图）
// 为了让最大值显示在顶部，需要反转数组
const yData = computed(() => {
  if (sortedData.value.length === 0) return [];

  return [...sortedData.value].reverse().map(item => item.materialType);
});

const option = computed<ECOption>(() => {
  return {
    backgroundColor: "#fff",
    tooltip: {
      showContent: true,
      trigger: "axis",
      formatter: (params: any) => {
        const data = params[0];
        const materialType = data.name;
        const defectRate = data.value;

        // 找到对应的原始数据
        const originalData = sortedData.value.find(item => item.materialType === materialType);

        return `
          ${materialType}<br/>
          不良率: ${defectRate}%<br/>
          总数量: ${originalData?.totalCount || 0}<br/>
          不良数量: ${originalData?.defectCount || 0}
        `;
      }
    },
    textStyle: {
      color: "#c0c3cd",
      fontSize: 14
    },
    grid: {
      left: "20%",
      right: "10%",
      top: "10%",
      bottom: "10%"
    },
    xAxis: {
      type: "value",
      axisLabel: {
        formatter: (value: number) => `${value}%`,
        textStyle: {
          color: colorMap.axisColor
        }
      }
    },
    yAxis: {
      type: "category",
      data: yData.value,
      axisLabel: {
        textStyle: {
          color: colorMap.axisColor
        }
      }
    },
    series: seriesData.value
  } as ECOption;
});
</script>
