<template>
  <el-dialog
    v-model="visible"
    width="900px"
    draggable
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="`${t(title)}`"
    modal-class="draggable-modal"
  >
    <el-form ref="formRef" label-position="top" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('月份')" prop="monthly">
            <el-date-picker
              v-model="form.monthly"
              type="month"
              :placeholder="$t('请选择月份')"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('工厂')" prop="plant">
            <el-input v-model="form.plant" :placeholder="$t('请选择工厂')" readonly @click="openSelectionDialog" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('物料类别')" prop="materialType">
            <el-input v-model="form.materialType" :placeholder="$t('请选择物料类别')" readonly @click="openSelectionDialog" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplierName">
            <el-input v-model="form.supplierName" :placeholder="$t('请选择供应商')" readonly @click="openSelectionDialog" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('IQC检验批次数')" prop="iqcInspectionBatchCount">
            <el-input-number v-model="form.iqcInspectionBatchCount" :min="0" :placeholder="$t('请填写')" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('IQC合格批次数')" prop="iqcQualifiedBatchCount">
            <el-input-number v-model="form.iqcQualifiedBatchCount" :min="0" :placeholder="$t('请填写')" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('IQC LAR')" prop="lar">
            <div style="display: flex; align-items: center; width: 100%">
              <el-input-number v-model="form.lar" :min="0" :placeholder="$t('自动计算')" style="flex: 1" :disabled="true" />
              <span style="margin-left: 8px">%</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('制程物料下线率(DPPM)')" prop="dppm">
            <el-input-number v-model="form.dppm" :min="0" :placeholder="$t('请填写')" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('制程重大品质问题发生次数(不良率≥2%)')" prop="majorQualityIssue">
            <el-input-number v-model="form.majorQualityIssue" :min="0" :placeholder="$t('请填写')" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('制程品质问题发生次数(不良率<2%)')" prop="minorQualityIssue">
            <el-input-number v-model="form.minorQualityIssue" :min="0" :placeholder="$t('请填写')" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('客户反馈或投诉次数')" prop="clientComplaint">
            <el-input-number v-model="form.clientComplaint" :min="0" :placeholder="$t('请填写')" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('品质改善配合度')" prop="qualityImprovementCooperation">
            <el-input v-model="form.qualityImprovementCooperation" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('总分')" prop="totalScore">
            <el-input-number v-model="form.totalScore" :min="0" :max="100" :placeholder="$t('请填写')" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('供应商现场安排')" prop="supplierOnsiteArrangement">
            <el-input v-model="form.supplierOnsiteArrangement" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>

  <!-- 供应商选择组件 -->
  <SupplierSelector ref="supplierSelectorRef" @select="handleSupplierSelect" @cancel="handleSupplierCancel" />
</template>

<script setup lang="tsx" name="MonthlyDataModal">
import { MonthlyData } from "@/typings/supplier-quality-summary/monthly_data";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, watch } from "vue";
import { Dict } from "@/typings/dict";
import { useI18n } from "vue-i18n";
import { isEmpty } from "@/utils/is";
import SupplierSelector from "../../components/SupplierSelector/index.vue";

const { t } = useI18n();

interface IState {
  title: string;
  isView: boolean;
  factory: Dict.IDataItem[];
  form: Partial<MonthlyData.Item>;
  api?: (params: any) => Promise<any>;
  updateApi?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  monthly: [{ required: true, message: "请选择月份", trigger: "blur" }],
  plant: [{ required: true, message: "请选择工厂", trigger: "blur" }],
  supplierName: [{ required: true, message: "请选择供应商", trigger: "blur" }],
  materialType: [{ required: true, message: "请填写物料类别", trigger: "blur" }],
  iqcInspectionBatchCount: [{ required: true, message: "请填写IQC检验批次数", trigger: "blur" }],
  iqcQualifiedBatchCount: [{ required: true, message: "请填写IQC合格批次数", trigger: "blur" }],
  majorQualityIssue: [{ required: true, message: "请填写制程重大品质问题发生次数", trigger: "blur" }],
  minorQualityIssue: [{ required: true, message: "请填写制程品质问题发生次数", trigger: "blur" }],
  dppm: [{ required: true, message: "请填写制程下线率DPPM", trigger: "blur" }],
  clientComplaint: [{ required: true, message: "请填写客户投诉次数", trigger: "blur" }],
  totalScore: [{ required: true, message: "请填写总分", trigger: "blur" }]
};

const visible = ref(false);
const formRef = ref<FormInstance>();
const supplierSelectorRef = ref<InstanceType<typeof SupplierSelector> | null>(null);

const state = reactive<IState>({
  isView: false,
  factory: [],
  title: "",
  form: {}
});
const { form, title, isView } = toRefs(state);

// Watch for changes in IQC fields and calculate LAR
watch(
  () => [form.value.iqcQualifiedBatchCount, form.value.iqcInspectionBatchCount],
  values => {
    const [qualified, inspection] = values;
    if (qualified !== undefined && inspection !== undefined && qualified > 0 && inspection > 0) {
      form.value.lar = Math.round((qualified * 100) / inspection);
    }
  },
  { immediate: true }
);

const setVisible = (val: boolean) => {
  visible.value = val;
};

// 打开选择弹窗
const openSelectionDialog = () => {
  supplierSelectorRef.value?.open();
};

// 供应商选择处理
const handleSupplierSelect = (row: SupplierInformation.Item) => {
  // 选择一行后，同时填充工厂、物料类别、供应商三个字段
  form.value.plant = row.plant;
  form.value.materialType = row.materialType;
  form.value.supplierName = row.supplierName;
};

// 供应商选择取消处理
const handleSupplierCancel = () => {
  // 取消选择，不做任何操作
};

const acceptParams = (params: IState) => {
  Object.assign(state, params);
  // 设置默认值
  if (isEmpty(params.form.iqcInspectionBatchCount)) {
    params.form.iqcInspectionBatchCount = 0;
  }
  if (isEmpty(params.form.iqcQualifiedBatchCount)) {
    params.form.iqcQualifiedBatchCount = 0;
  }
  if (isEmpty(params.form.majorQualityIssue)) {
    params.form.majorQualityIssue = 0;
  }
  if (isEmpty(params.form.minorQualityIssue)) {
    params.form.minorQualityIssue = 0;
  }
  if (isEmpty(params.form.dppm)) {
    params.form.dppm = 0;
  }
  if (isEmpty(params.form.clientComplaint)) {
    params.form.clientComplaint = 0;
  }
  if (isEmpty(params.form.totalScore)) {
    params.form.totalScore = 0;
  }
  if (isEmpty(params.form.lar)) {
    params.form.lar = 0;
  }
  setVisible(true);
};

const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const { success, data } = await state.api!(form.value);
      if (!success) {
        ElMessage.error({ message: t(`保存失败`) });
        return;
      }
      form.value = data;
      if (!isEmpty(form.value.id) && form.value.id! > 0) {
        state.api = state.updateApi;
      }
      ElMessage.success({ message: t(`保存成功`) });
      state.getTableList!();
      // setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
