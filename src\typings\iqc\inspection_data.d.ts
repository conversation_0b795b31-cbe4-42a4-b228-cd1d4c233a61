export namespace InspectionData {
  export interface Item {
    id: number;
    plant: string;
    pkey: string;
    orderNo: string;
    materialType: string;
    partNo: string;
    materialName: string;
    produceDate: string;
    supplierNo: string;
    supplierName: string;
    unit: string;
    receivedQty: number;
    checkResult: string;
    inboundOrderNo: string;
    description: string;
    yearWeek: string;
    checkMethod: string;
    checkStatus: string;
    responseBelong: string;
    urgency: string;
    receivedAt: string;
    checkAt: string;
    mrb: string | null;
    ngType: string | null;
    criVal: number | null;
    majVal: number | null;
    minVal: number | null;
    sqe: string | null;
    plmOrderNo: string | null;
    documentNo: string | null;
    sampleQty: number;
    editTime: string;
    diffValue: number;
    dayNum: number;
    deleted: number;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string | null;
    startDate: string | null;
    endDate: string | null;
  }
  export interface IQueryParams {
    startDate: string;
    endDate: string;
    checkStatus: string;
    materialType: string;
    orderNo: string;
    checkResult: string;
    materialName: string;
    partNo: string;
    supplierName: string;
    checkMethod: string;
    urgency: string;
  }
}
