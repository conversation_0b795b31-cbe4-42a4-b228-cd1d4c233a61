<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">LAR分析</h3>
      <el-button type="primary" size="small" @click="handleViewDetail">查看明细数据</el-button>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="LarChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Statistics } from "@/typings/supplier-quality-summary/statistics_monthly";

interface Props {
  chartData: Statistics.LarMonthlyData[];
  searchParams: Statistics.IQueryParams;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  viewDetail: [chartType: string, data: Statistics.LarMonthlyData[]];
}>();

// 检查是否有数据
const hasData = computed(() => props.chartData && props.chartData.length > 0);

// 图表配置
const chartOption = computed<ECOption>(() => {
  const suppliers = props.chartData.map(item => item.supplier);
  const larValues = props.chartData.map(item => item.lar);
  const targets = props.chartData.map(item => item.target);

  return {
    title: {
      text: "LAR数据分析",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold"
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross"
      },
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}${param.seriesName.includes("LAR") ? "%" : ""}<br/>`;
        });
        return result;
      }
    },
    legend: {
      data: ["IQC LAR", "目标"],
      bottom: 10
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: suppliers,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: "value",
      name: "LAR (%)",
      nameTextStyle: {
        fontSize: 12
      },
      min: 0,
      max: 100
    },
    series: [
      {
        name: "IQC LAR",
        type: "bar",
        data: larValues,
        itemStyle: {
          color: "#5470c6"
        },
        label: {
          show: true,
          position: "top",
          formatter: "{c}%"
        },
        barWidth: "40%"
      },
      {
        name: "目标",
        type: "line",
        // 使用主Y轴
        data: targets,
        itemStyle: {
          color: "#fc8452"
        },
        lineStyle: {
          color: "#fc8452"
        },
        symbol: "circle",
        symbolSize: 6,
        label: {
          show: true,
          position: "top",
          formatter: "{c}%"
        }
      }
    ]
  };
});

// 查看明细数据
const handleViewDetail = () => {
  emit("viewDetail", "lar", props.chartData);
};
</script>

<style lang="scss" scoped>
.echarts-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.echarts-content {
  flex: 1;
  width: 100%;
  min-height: 400px;
}

.no-data {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
