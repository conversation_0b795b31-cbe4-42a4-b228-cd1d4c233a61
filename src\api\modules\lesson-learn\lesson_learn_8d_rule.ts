import { ReqPage, ResPage } from "@/api/interface/index";
import { LessonLearn8dRule } from "@/typings/lesson-learn/lesson_learn_8d_rule";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/lessonLearn8dRule`;

// 列表
export const getLessonLearn8dRuleList = (params?: ReqPage) => {
  return http.post<ResPage<LessonLearn8dRule.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getLessonLearn8dRuleDetail = (id: number) => {
  return http.post<LessonLearn8dRule.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createLessonLearn8dRule = (data: LessonLearn8dRule.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editLessonLearn8dRule = (data: LessonLearn8dRule.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteLessonLearn8dRule = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportLessonLearn8dRule = (params?: LessonLearn8dRule.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importLessonLearn8dRule = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importLessonLearn8dRuleTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
