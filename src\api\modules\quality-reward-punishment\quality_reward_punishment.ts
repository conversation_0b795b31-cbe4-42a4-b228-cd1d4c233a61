import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityRewardPunishment } from "@/typings/quality-reward-punishment/quality_reward_punishment";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/quality-reward-punishment/statistics";

const baseUrl = `${API_PREFIX}/QualityRewardPunishment`;

// 列表
export const getQualityRewardPunishmentList = (params?: ReqPage) => {
  return http.post<ResPage<QualityRewardPunishment.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityRewardPunishmentDetail = (id: number) => {
  return http.post<QualityRewardPunishment.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityRewardPunishment = (data: QualityRewardPunishment.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityRewardPunishment = (data: QualityRewardPunishment.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityRewardPunishment = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQualityRewardPunishment = (params?: QualityRewardPunishment.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQualityRewardPunishment = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQualityRewardPunishmentTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
// 审批
export const submitToAudit = (id: number) => {
  return http.post<QualityRewardPunishment.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backAudit = (params: { id: number; rejectReason: string }) => {
  return http.post<QualityRewardPunishment.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const audit = (id: number) => {
  return http.post<QualityRewardPunishment.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getApproveList = (params?: ReqPage) => {
  return http.post<ResPage<QualityRewardPunishment.Item>>(`${baseUrl}/listToBeAudited`, params);
};

//质量奖惩 个人分析查基础数据
export const personSummary = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.summary[]>(`${baseUrl}/statsQuallityRPByPersonBasic`, params);
};
//质量奖惩 个人分析查奖惩趋势
export const statsQualityRPByPersonTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsTrend>(`${baseUrl}/statsQuallityRPByPersonTrend`, params);
};
//质量奖惩 个人分析查奖惩一级评价维度趋势
export const statsQualityRPByPersonFirstTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByPersonFirstTrend`, params);
};
//质量奖惩 个人分析查奖惩二级评价维度趋势
export const statsQualityRPByPersonSecondTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByPersonSecondTrend`, params);
};
//质量奖惩 个人分析查奖惩KPI趋势
export const statsQualityRPByPersonKPITrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByPersonKPITrend`, params);
};
//质量奖惩 个人分析查奖惩列表
export const statsQualityRPByPersonList = (params: Statistics.IQueryParams) => {
  return http.post<QualityRewardPunishment.Item[]>(`${baseUrl}/statsQuallityRPByPersonList`, params);
};

export const statsQualityRPByPersonTotalList = (params: Statistics.IQueryParams) => {
  return http.post<QualityRewardPunishment.Item[]>(`${baseUrl}/statsQuallityRPByPersonTotalList`, params);
};

//绩效分布-左侧树状图
export const statsPerformanceTree = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.TreeList>(`${baseUrl}/statsPerformanceTree`, params);
};
//质量奖惩 个人分析查奖惩列表导出
export const exportPersonListData = (params?: QualityRewardPunishment.IQueryParams) => {
  return http.post(`${baseUrl}/exportPersonListData`, params);
};
//质量奖惩 部门分析查基础数据
export const deptSummary = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.summary[]>(`${baseUrl}/statsQuallityRPByDeptBasic`, params);
};
//质量奖惩 部门分析查奖惩趋势
export const statsQualityRPByDeptTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsTrend>(`${baseUrl}/statsQuallityRPByDeptTrend`, params);
};
//质量奖惩 部门分析查奖惩一级评价维度趋势
export const statsQualityRPByDeptFirstTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByDeptFirstTrend`, params);
};
//质量奖惩 部门分析查奖惩二级评价维度趋势
export const statsQualityRPByDeptSecondTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByDeptSecondTrend`, params);
};
//质量奖惩 部门分析查奖惩KPI趋势
export const statsQualityRPByDeptKPITrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByDeptKPITrend`, params);
};
//质量奖惩 部门分析查奖惩列表
export const statsQualityRPByDeptList = (params: Statistics.IQueryParams) => {
  return http.post<ResPage<QualityRewardPunishment.Item>>(`${baseUrl}/statsQuallityRPByDeptList`, params);
};

//质量奖惩 集团分析查基础数据
export const groupSummary = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.summary[]>(`${baseUrl}/statsQuallityRPByGroupBasic`, params);
};
//质量奖惩 集团分析查奖惩趋势
export const statsQualityRPByGroupTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsTrend>(`${baseUrl}/statsQuallityRPByGroupTrend`, params);
};
//质量奖惩 集团分析查奖惩一级评价维度趋势
export const statsQualityRPByGroupFirstTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByGroupFirstTrend`, params);
};
//质量奖惩 集团分析查奖惩二级评价维度趋势
export const statsQualityRPByGroupSecondTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.statsList>(`${baseUrl}/statsQuallityRPByGroupSecondTrend`, params);
};
//质量奖惩 集团分析查奖惩KPI趋势
export const statsQualityRPByGroupPlantTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.PlantTrend[]>(`${baseUrl}/statsQuallityRPByGroupPlantTrend`, params);
};
//质量奖惩 集团分析查奖惩列表
export const statsQualityRPByGroupList = (params: Statistics.IQueryParams) => {
  return http.post<ResPage<QualityRewardPunishment.Item>>(`${baseUrl}/statsQuallityRPByGroupList`, params);
};
