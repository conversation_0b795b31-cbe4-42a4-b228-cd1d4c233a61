<template>
  <div class="echarts-container">
    <div class="echarts">
      <ECharts :option="option" />
    </div>
  </div>
</template>

<script setup lang="ts" name="line">
import { ECOption } from "@/components/ECharts/config";
import ECharts from "@/components/ECharts/index.vue";
import { defineProps } from "vue";

const props = defineProps({
  xData: {
    type: Array as () => string[],
    required: true
  },
  seriesData: {
    type: Array as () => Array<{
      name: string;
      data: number[];
    }>,
    required: true
  }
});

// 新增颜色配置
const colors = [
  "#00cc66", // 绿色
  "#0066cc", // 蓝色
  "#ff0000" // 红色
];

const option: ECOption = {
  legend: {
    data: props.seriesData.map(item => item.name), // 动态生成图例
    bottom: 0,
    type: "scroll",
    orient: "horizontal",
    textStyle: {
      color: "#fff",
      fontSize: 12
    },
    itemGap: 20
  },
  xAxis: {
    type: "category",
    data: props.xData,
    axisLabel: { color: "#fff" }
  },
  yAxis: {
    type: "value",
    axisLabel: { color: "#fff" }
  },
  series: props.seriesData.map((series, index) => ({
    type: "line",
    name: series.name,
    data: series.data,
    smooth: true,
    symbol: "circle",
    symbolSize: 8,
    itemStyle: {
      color: colors[index % colors.length] // 按系列索引分配颜色
    }
  }))
};
</script>

<style lang="scss" scoped>
.echarts-container {
  display: flex;
  width: 100%;
  height: 100%;
}
.echarts {
  flex: 1;
  height: 100%;
}
</style>
