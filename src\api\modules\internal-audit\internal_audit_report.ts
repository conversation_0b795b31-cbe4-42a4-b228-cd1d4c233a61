import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { InternalAuditReport } from "@/typings/internal-audit/internal_audit_report";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/internal-audit/statistics";

const baseUrl = `${API_PREFIX}/InternalAuditReport`;
// 列表
export const getInternalAuditReportList = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditReport.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getInternalAuditReportDetail = (id: number) => {
  return http.post<InternalAuditReport.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createInternalAuditReport = (data: InternalAuditReport.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editInternalAuditReport = (data: InternalAuditReport.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteInternalAuditReport = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportInternalAuditReport = (params?: InternalAuditReport.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importInternalAuditReport = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importInternalAuditReportTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
// 看板
export const statsPanel = (params?: StatisticsReport) => {
  return http.post<Statistics.StatsScoreLineBar>(`${baseUrl}/statsPanel`, params);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<InternalAuditReport.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backAuditReport = (params: { id: number; rejectReason: string }) => {
  return http.post<InternalAuditReport.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const auditAuditReport = (id: number) => {
  return http.post<InternalAuditReport.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getAuditReportApproveList = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditReport.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 审核通过率
export const statsEAPass = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsEAPass`, params);
};

// 审核问题点及时关闭率
export const statsEAQuestionClose = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsEAQuestionClose`, params);
};

// 审核计划按期实施率
export const statsPlanOnScheduleImplementationRate = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsPlanOnScheduleImplementationRate`, params);
};
