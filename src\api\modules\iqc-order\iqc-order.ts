import http from "@/api";
import { IqcOrder } from "@/typings/iqc-order/iqc-order";
import { ReqPage, ResPage } from "@/api/interface";
import { API_PREFIX } from "@/api/config/servicePort";
const baseUrl = `${API_PREFIX}/iqcOrder`;
// const token =
//   "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImYzMTUwNDBiLTljODktNDIwYy1iZjllLTQwNzQwMzUwMjY0ZiJ9.sV3tF8ZwX67AL_g8eR0eXK1rbcepxeRJAfpShguCtQW5DJlMd_IV1HMvDwIU-7GAUvUMwFRPXvGWXhFJKfWsBg";

// const headers = {
//   Authorization: token
// };
//检验批次合格率(整体)
export const getStatsByAllBatchQualifyRate = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.PassRate[]>(`${baseUrl}/statsByAllBatchQualifyRate`, params);
};

//物料大类DPPM
export const getStatsDppmRate = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.Dppm[]>(`${baseUrl}/statsDppmRate`, params);
};
//物料大类DPPM
export const getStatsByFailNumByType = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.Dppm[]>(`${baseUrl}/statsByFailNumByType`, params);
};
//供应商检验批次不合格率
export const getStatsBatchFailBySupplierRate = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.SupplierDefect[]>(`${baseUrl}/statsBatchFailBySupplierRate`, params);
};
//供应商检验批次不合格率
export const getStatsBatchQualifyRate = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.materialPassRate[]>(`${baseUrl}/statsBatchQualifyRate`, params);
};
//校验及时率
export const getStatsTimelinessRate = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.MaterialTimelyRate[]>(`${baseUrl}/statsTimelinessRate`, params);
};
//所有物料统计及时检测批次
export const getStatsInspectionByAll = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.StatsInspection>(`${baseUrl}/statsInspectionByAll`, params);
};
//所有物料统计及时检测批次
export const getStatsInspectionTimeliness = (params: IqcOrder.IQueryParams) => {
  return http.post<IqcOrder.StatsInspection>(`${baseUrl}/statsInspectionTimeliness`, params);
};
//实时待检信息
export const listIqcOrder = (params?: Partial<IqcOrder.listParam & ReqPage>) => {
  return http.post<ResPage<IqcOrder.Item>>(`${baseUrl}/list`, params);
};
//获取刷新时间间隔和tab切换间隔
export const getRefreshTime = () => {
  return http.post<IqcOrder.refreshTime>(`${baseUrl}/refreshTime`);
};
