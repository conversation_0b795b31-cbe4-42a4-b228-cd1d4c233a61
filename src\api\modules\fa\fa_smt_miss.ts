import { ReqPage, ResPage } from "@/api/interface/index";
import { FaSmtMiss } from "@/typings/fa/fa_smt_miss";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/faSMTMiss`;

// 列表
export const getFaSmtMissList = (params?: ReqPage) => {
  return http.post<ResPage<FaSmtMiss.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getFaSmtMissDetail = (id: number) => {
  return http.post<FaSmtMiss.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createFaSmtMiss = (data: FaSmtMiss.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editFaSmtMiss = (data: FaSmtMiss.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteFaSmtMiss = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportFaSmtMiss = (params?: FaSmtMiss.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importFaSmtMiss = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importFaSmtMissTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
