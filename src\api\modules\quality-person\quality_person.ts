import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityPerson } from "@/typings/quality-person/quality_person";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qualityPersonOrder`;

// 列表
export const getQualityPersonList = (params?: ReqPage) => {
  return http.post<ResPage<QualityPerson.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityPersonDetail = (id: number) => {
  return http.post<QualityPerson.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityPerson = (data: QualityPerson.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityPerson = (data: QualityPerson.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityPerson = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQualityPerson = (params?: QualityPerson.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQualityPerson = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadDataset`, formData);
};

//下载模板
export const importQualityPersonTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
