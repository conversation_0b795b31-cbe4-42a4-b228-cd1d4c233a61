import { ReqPage, ResPage } from "@/api/interface/index";
import { IpqcSmtInspection } from "@/typings/ipqc-smt/ipqc_smt_inspection";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/ipqc-smt/statistics";

const baseUrl = `${API_PREFIX}/ipqcSMTInspection`;

// 列表
export const getIpqcSmtInspectionList = (params?: ReqPage) => {
  return http.post<ResPage<IpqcSmtInspection.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getIpqcSmtInspectionDetail = (id: number) => {
  return http.post<IpqcSmtInspection.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createIpqcSmtInspection = (data: IpqcSmtInspection.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editIpqcSmtInspection = (data: IpqcSmtInspection.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteIpqcSmtInspection = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportIpqcSmtInspection = (params?: IpqcSmtInspection.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importIpqcSmtInspection = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importIpqcSmtInspectionTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

//各楼层巡检问题点统计
export const statsBarByFloor = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsBarByFloor`, params);
};

//问题点分类BY类型
export const statsBarByCategory = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsBarByCategory`, params);
};

// 问题点分布BY责任人
export const statsPieByDept = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsPieByDept`, params);
};

//关闭率趋势
export const statsClosedRatioTrend = (params?: Statistics.IQueryParamsReportStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsClosedRatioTrend`, params);
};

//问题点趋势图
export const statsFindingTrend = (params?: Statistics.IQueryParamsReportStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsFindingTrend`, params);
};

//问题点分布
export const statsFindingOverview = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.statsFindingOverview>(`${baseUrl}/statsFindingOverview`, params);
};
