<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">供应商总得分分析</h3>
      <el-button type="primary" size="small" @click="handleViewDetail">查看明细数据</el-button>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="TotalScoreChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Statistics } from "@/typings/supplier-quality-summary/statistics_monthly";

interface Props {
  chartData: Statistics.TotalScoreData[];
  searchParams: Statistics.IQueryParams;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  viewDetail: [chartType: string, data: Statistics.TotalScoreData[]];
  init: [chartInstance: any];
}>();

// 检查是否有数据
const hasData = computed(() => props.chartData && props.chartData.length > 0);

// 图表配置
const chartOption = computed<ECOption>(() => {
  const supplierNames = props.chartData.map(item => item.supplier);
  const scores = props.chartData.map(item => item.score);

  console.log("供应商名称:", supplierNames);
  console.log("得分数据:", scores);

  return {
    title: {
      text: "供应商总得分分析",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold"
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>总得分: ${data.value}`;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: supplierNames,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: "value",
      name: "总得分",
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: "总得分",
        type: "bar",
        data: scores,
        itemStyle: {
          color: "#5470c6"
        },
        label: {
          show: true,
          position: "top",
          formatter: "{c}"
        },
        barWidth: "60%"
      }
    ]
  };
});

// 查看明细数据
const handleViewDetail = () => {
  emit("viewDetail", "totalScore", props.chartData);
};

const handleChartInit = (chartInstance: any) => {
  console.log("ECharts实例初始化完成:", chartInstance);
  if (!chartInstance) {
    console.error("ECharts实例初始化失败");
  }
};
</script>

<style lang="scss" scoped>
.echarts-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.echarts-content {
  flex: 1;
  width: 100%;
  min-height: 400px;
}

.no-data {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
