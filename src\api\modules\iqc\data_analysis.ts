import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { DataAnalysis } from "@/typings/iqc/data_analysis";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/iqcOrder`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;

// 1. 检验批次合格率By物料大类
export const statsBatchQualifyRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.BatchQualifyRateByMaterialGroup[]>(`${baseUrl}/statsBatchQualifyRate`, params);
};

// 2. 检验批次合格率By供应商
export const statsBySupplierBatchQualifyRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.BatchQualifyRateBySupplierGroup[]>(`${baseUrl}/statsBySupplierBatchQualifyRate`, params);
};

// 3. DPPM By物料类别
export const statsDppmRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.DppmByMaterialGroup[]>(`${baseUrl}/statsDppmRate`, params);
};

// 4. DPPM By供应商
export const statsDppmBySupplierRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.DppmBySupplierGroup[]>(`${baseUrl}/statsDppmBySupplierRate`, params);
};

// 5. 检验及时率By物料类别
export const statsTimelinessRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.TimelinessRateByMaterialGroup[]>(`${baseUrl}/statsTimelinessRate`, params);
};

// 6. 检验及时率By供应商
export const statsTimelinessBySupplierRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.TimelinessRateBySupplierGroup[]>(`${baseUrl}/statsTimelinessBySupplierRate`, params);
};

// 7. 不良物料类别(TOP5)
export const statsBatchFailRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.BatchFailRateByMaterial[]>(`${baseUrl}/statsBatchFailRate`, params);
};

// 8. 不良供应商(TOP5)
export const statsBatchFailBySupplierRate = (params: DataAnalysis.IQueryParams) => {
  return http.post<DataAnalysis.BatchFailRateBySupplier[]>(`${baseUrl}/statsBatchFailBySupplierRate`, params);
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
