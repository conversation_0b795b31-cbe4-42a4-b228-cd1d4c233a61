export namespace JqeData {
  export interface Item {
    id: number;
    monthly: string;
    plant: string;
    supplierName: string;
    materialType: string;
    isJqeAssigned: number;
    wasBlacklistedLastQuarter: number;
    oqcInspectionDefectRateList: InspectionDefectRateDetail[];
    reinspectionDefectRateList: ReInspectionDefectRateDetail[];
    deleted: number;
    creatEmail: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }

  export interface IQueryParams {
    startDate: string;
    endDate: string;
    plant: string;
    supplierName: string;
    materialType: string;
  }

  export interface NewParams {
    monthly: string;
    plant: string;
    supplierName: string;
    materialType: string;
    isJqeAssigned: number;
    wasBlacklistedLastQuarter: number;
    oqcInspectionDefectRateList: InspectionDefectRateDetail[];
    reinspectionDefectRateList: ReInspectionDefectRateDetail[];
  }

  export interface EditParams {
    id: number;
    monthly: string;
    plant: string;
    supplierName: string;
    materialType: string;
    isJqeAssigned: number;
    wasBlacklistedLastQuarter: number;
    oqcInspectionDefectRateList: InspectionDefectRateDetail[];
    reinspectionDefectRateList: ReInspectionDefectRateDetail[];
  }

  export interface InspectionDefectRateDetail {
    inspectionDefectRate: string;
    week: string;
  }
  export interface ReInspectionDefectRateDetail {
    reinspectionDefectRate: string;
    week: string;
  }
}
