import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Role } from "@/typings/role";
import { ADMIN_API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/sys_role`;

export const getRoleList = (params?: ReqPage) => {
  return http.get<ResPage<Role.Item>>(`${baseUrl}/list`, params);
};

export const getRoleDetail = (roleId: number) => {
  return http.get<Role.Item>(`${baseUrl}/${roleId}`);
};

export const createRole = (data: Role.Item) => {
  return http.post(`${baseUrl}`, data);
};

export const editRole = (data: Role.Item) => {
  return http.put(`${baseUrl}`, data);
};

export const deleteRole = (data: { ids: number[] }) => {
  return http.delete(`${baseUrl}`, data);
};
