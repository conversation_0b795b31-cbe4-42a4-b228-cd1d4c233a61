import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { ProposalList } from "@/typings/gold-idea/gold_idea_proposal";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/proposal`;

// 获取提案评审列表
export const getMyReviewList = (params?: ReqPage) => {
  return http.post<ResPage<ProposalList.Item>>(`${baseUrl}/reviewList`, params);
};

// 提交评审意见
export const submitReview = (data: Partial<ProposalList.reviewDetail>) => {
  return http.post(`${baseUrl}/submitReview`, data);
};

// 获取所有的评审数据
export const getReviewData = (id: number) => {
  return http.get<ProposalList.reviewDetail>(`${baseUrl}/reviewData/${id}`);
};
