<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :search-col="{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }"
      :init-param="initParam"
      @reset="resetForm()"
    >
      <template #tableHeader="{ selectedListIds }">
        <el-button v-auth="'supplier-quality-summary-monthly-data:add'" type="primary" @click="openMonthlyDataModal('新增')">{{
          $t("新增")
        }}</el-button>
        <el-button v-auth="'supplier-quality-summary-monthly-data:edit'" type="primary" @click="openMonthlyDataModal('编辑')">{{
          $t("编辑")
        }}</el-button>
        <el-button
          v-auth="'supplier-quality-summary-monthly-data:delete'"
          type="danger"
          @click="batchDelete(selectedListIds as number[])"
          >{{ $t("删除") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-monthly-data:export'"
          type="primary"
          @click="downloadFile(selectedListIds as number[])"
          >{{ $t("导出") }}
        </el-button>
      </template>
    </ProTable>
    <MonthlyDataModal ref="MonthlyDataModalRef" />
  </div>
</template>

<script setup lang="tsx" name="supplier-quality-summary-monthly-data">
import {
  getMonthlyData,
  createMonthData,
  editMonthData,
  deleteMonthlyData,
  exportMonthlyData,
  getSupplierData
} from "@/api/modules/supplier-quality-summary/supplier_quality_summary_monthly_data";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import MonthlyDataModal from "./components/MonthlyDataModal.vue";
import { useAdminDict } from "@/hooks/useDict";
import { MonthlyData } from "@/typings/supplier-quality-summary/monthly_data";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { download } from "@/api/modules/common";
import { ref, reactive, computed, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import DateRange from "@/views/components/DateRange.vue";

const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

const MonthlyDataModalRef = ref<InstanceType<typeof MonthlyDataModal> | null>(null);
let queryParams = reactive<MonthlyData.IQueryParams>({} as MonthlyData.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

const columns = reactive<ColumnProps<MonthlyData.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "monthly",
    label: "月份",
    width: 120,
    search: {
      order: 1,
      render: () => {
        return <DateRange type="month" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  {
    prop: "plant",
    label: "工厂",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 2 },
    enum: factory,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "supplierName", label: "供应商", width: 150, search: { el: "input", order: 4 } },
  { prop: "materialType", label: "物料类别", width: 120, search: { el: "input", order: 3 } },
  { prop: "lar", label: "IQC LAR", width: 100 },
  { prop: "majorQualityIssue", label: "制程重大品质问题发生次数(不良率≥2%)", width: 120 },
  { prop: "minorQualityIssue", label: "制程品质问题发生次数(不良率<2%)", width: 120 },
  { prop: "dppm", label: "制程物料下线率(DPPM)", width: 100 },
  { prop: "clientComplaint", label: "客户反馈或投诉次数", width: 100 },
  { prop: "qualityImprovementCooperation", label: "品质改善配合度", width: 150 },
  { prop: "totalScore", label: "总分", width: 100 },
  { prop: "supplierOnsiteArrangement", label: "供应商现场安排", width: 150 },
  { prop: "createBy", label: "创建人", width: 120 },
  { prop: "createAt", label: "创建时间", width: 165 },
  { prop: "updateBy", label: "修改人", width: 120 },
  { prop: "updateAt", label: "修改时间", width: 165 }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  // 处理日期区间参数
  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }

  queryParams = reactive(condition);
  return getMonthlyData({
    condition,
    pageNum,
    pageSize
  });
};

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
  queryParams = reactive<MonthlyData.IQueryParams>({} as MonthlyData.IQueryParams);
};

const batchDelete = async (id?: number | number[]) => {
  const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(deleteMonthlyData, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 导出
const downloadFile = async (ids?: number[]) => {
  if (!isEmpty(ids)) {
    // 选中数据导出
    ElMessageBox.confirm(t("确认导出数据?"), t("提示"), { type: "warning" }).then(() => download(exportMonthlyData(ids!)));
  } else {
    // 全部数据导出 - 需要先获取所有数据的IDs
    ElMessage.warning(t("请选择要导出的数据"));
  }
};

const openMonthlyDataModal = (title: string, row: Partial<MonthlyData.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    factory,
    form,
    api: title === "新增" ? createMonthData : title === "编辑" ? editMonthData : undefined,
    updateApi: editMonthData,
    getTableList: proTable.value?.getTableList
  };
  MonthlyDataModalRef.value?.acceptParams(params as any);
};
</script>
