import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Dept } from "@/typings/dept";
import { ADMIN_API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/sys_dept`;

export const getDeptList = (params?: ReqPage) => {
  return http.get<Dept.Item[]>(`${baseUrl}/list`, params);
};

export const getDeptAll = () => {
  return http.get<Dept.Item[]>(`${baseUrl}/all`, {}, { loading: false });
};

export const getDeptDetail = (deptId: number) => {
  return http.get<Dept.Item>(`${baseUrl}/${deptId}`);
};

export const createDept = (data: Dept.Item) => {
  return http.post(`${baseUrl}`, data);
};

export const editDept = (data: Dept.Item) => {
  return http.put(`${baseUrl}`, data);
};

export const deleteDept = (id: number) => {
  return http.delete(`${baseUrl}/${id}`);
};
