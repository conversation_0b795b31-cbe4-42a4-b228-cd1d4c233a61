export namespace TargetData {
  export interface Item {
    id: number;
    plant: string;
    supplierName: string;
    materialType: string;
    quarter: string;
    majorQualityIssueTarget: number;
    minorQualityIssueTarget: number;
    clientComplaintTarget: number;
    larTarge: number;
    deleted: number;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface IQueryParams {
    plant: string;
    supplierName: string;
    materialType: string;
    quarter: string;
  }
  export interface NewParams {
    plant: string;
    supplierName: string;
    materialType: string;
    quarter: string;
    majorQualityIssueTarget: number;
    minorQualityIssueTarget: number;
    clientComplaintTarget: number;
    larTarge: number;
  }
  export interface EditParams {
    id: number;
    plant: string;
    supplierName: string;
    materialType: string;
    quarter: string;
    majorQualityIssueTarget: number;
    minorQualityIssueTarget: number;
    clientComplaintTarget: number;
    larTarge: number;
  }
}
