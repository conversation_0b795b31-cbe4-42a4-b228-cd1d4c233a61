import { ReqPage, ResPage } from "@/api/interface/index";
import { IpqcDipAbnormal } from "@/typings/ipqc-dip-abnormal/ipqc_dip_abnormal";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ipqcDIPAbnormal`;

// 列表
export const getIpqcDipAbnormalList = (params?: ReqPage) => {
  return http.post<ResPage<IpqcDipAbnormal.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getIpqcDipAbnormalDetail = (id: number) => {
  return http.post<IpqcDipAbnormal.Item>(`${baseUrl}/get/${id}`);
};

// 提醒
export const remind = (id: number) => {
  return http.post<IpqcDipAbnormal.Item>(`${baseUrl}/remind/${id}`);
};

// 我的任务
export const listByCQE = (params?: ReqPage) => {
  return http.post<ResPage<IpqcDipAbnormal.Item>>(`${baseUrl}/listByCQE`, params);
};

// 填写改善措施
export const modifyByCQE = (data: IpqcDipAbnormal.Item) => {
  return http.post(`${baseUrl}/modifyByCQE`, data);
};

// 新增
export const createIpqcDipAbnormal = (data: IpqcDipAbnormal.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editIpqcDipAbnormal = (data: IpqcDipAbnormal.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 提交
export const submit = (data: IpqcDipAbnormal.Item) => {
  return http.post(`${baseUrl}/submit`, data);
};

// 删除
export const deleteIpqcDipAbnormal = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportIpqcDipAbnormal = (params?: IpqcDipAbnormal.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importIpqcDipAbnormal = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importIpqcDipAbnormalTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
