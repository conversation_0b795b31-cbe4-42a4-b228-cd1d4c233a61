<template>
  <div class="echarts">
    <ECharts :option="option" />
  </div>
</template>

<script setup lang="ts" name="bar">
import { ECOption } from "@/components/ECharts/config";
import ECharts from "@/components/ECharts/index.vue";
import { defineProps } from "vue";
// import * as echarts from "echarts";

// 移除targetData属性
const props = defineProps({
  xData: {
    type: Array as () => string[],
    required: true
  },
  seriesData: {
    type: Array as () => Array<{
      name: string;
      data: number[];
    }>,
    required: true
  },
  // 新增标签长度限制配置
  labelMaxLength: {
    type: Number,
    default: 0 // 0表示不限制长度
  }
});

const option: ECOption = {
  title: {
    // text: "检验批次合格率",
    // subtext: "各供应商数据统计",
    left: "center",
    textStyle: {
      fontSize: 18,
      color: "#fff"
    },
    subtextStyle: {
      fontSize: 15,
      color: "#fff"
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  grid: {
    left: "0%",
    right: "0%",
    bottom: "10%", // 为x轴标签留出空间
    borderColor: "rgba(255,255,255,0.1)", // 网格线颜色
    containLabel: true // 包含坐标轴标签在内
  },
  xAxis: {
    type: "category",
    data: props.xData, // 使用传入的x轴数据
    axisLabel: {
      color: "#fff",
      interval: 0,
      // 修改formatter根据配置截断
      formatter:
        props.labelMaxLength > 0
          ? (value: string) => (value.length > props.labelMaxLength ? value.substring(0, props.labelMaxLength) + "..." : value)
          : undefined
    }
  },
  yAxis: {
    type: "value",
    axisLabel: {
      color: "#fff",
      formatter: "{value}"
    }
  },
  legend: {
    data: props.seriesData.map(item => item.name), // 动态生成图例
    bottom: 0,
    type: "scroll",
    textStyle: {
      color: "#fff",
      fontSize: 12
    },
    itemGap: 20
  },
  series: [
    ...props.seriesData.map(series => ({
      type: "bar" as const,
      name: series.name,
      data: series.data,
      label: {
        show: true, // 显示数值标签
        position: "top", // 标签位置在柱子上方
        color: "#fff", // 标签颜色
        fontSize: 12, // 字体大小
        formatter: "{c}" // 显示原始数值
      }
    }))
    // 删除目标值系列配置
  ]
};
</script>

<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
</style>
