import { ReqPage, ResPage } from "@/api/interface/index";
import { SupplierAuditStaff } from "@/typings/supplier-annual-audit/supplier_audit_staff";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/supplierAuditStaff`;

// 列表
export const getSupplierAuditStaffList = (params?: ReqPage) => {
  return http.post<ResPage<SupplierAuditStaff.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSupplierAuditStaffDetail = (id: number) => {
  return http.post<SupplierAuditStaff.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSupplierAuditStaff = (data: SupplierAuditStaff.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSupplierAuditStaff = (data: SupplierAuditStaff.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSupplierAuditStaff = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSupplierAuditStaff = (params?: SupplierAuditStaff.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSupplierAuditStaff = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSupplierAuditStaffTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
