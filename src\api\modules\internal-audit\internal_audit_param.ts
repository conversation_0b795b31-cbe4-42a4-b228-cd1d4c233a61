import { ReqPage, ResPage } from "@/api/interface/index";
import { InternalAuditParam } from "@/typings/internal-audit/internal_audit_param";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/InternalAuditParam`;

// 列表
export const getInternalAuditParamList = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditParam.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getInternalAuditParamDetail = (id: number) => {
  return http.post<InternalAuditParam.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createInternalAuditParam = (data: InternalAuditParam.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editInternalAuditParam = (data: InternalAuditParam.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteInternalAuditParam = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportInternalAuditParam = (params?: InternalAuditParam.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importInternalAuditParam = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importInternalAuditParamTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
