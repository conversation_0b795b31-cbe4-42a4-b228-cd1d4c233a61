<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :search-col="{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }"
      :init-param="initParam"
      @reset="resetForm()"
    >
      <template #tableHeader="{ selectedListIds }">
        <el-button v-auth="'supplier-quality-summary-qcc-data:add'" type="primary" @click="openQccDataModal('新增')">{{
          $t("新增")
        }}</el-button>
        <el-button v-auth="'supplier-quality-summary-qcc-data:edit'" type="primary" @click="openQccDataModal('编辑')">{{
          $t("编辑")
        }}</el-button>
        <el-button
          v-auth="'supplier-quality-summary-qcc-data:delete'"
          type="danger"
          @click="batchDelete(selectedListIds as number[])"
          >{{ $t("删除") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-qcc-data:export'"
          type="primary"
          plain
          @click="downloadFile(selectedListIds as number[])"
          >{{ $t("导出") }}
        </el-button>
      </template>
    </ProTable>
    <QccDataModal ref="QccDataModalRef" />
  </div>
</template>

<script setup lang="tsx" name="supplier-quality-summary-qcc-data">
import {
  getQccData,
  createQccData,
  editQccData,
  deleteQccData,
  exportQccData,
  getSupplierData
} from "@/api/modules/supplier-quality-summary/supplier_quality_summary_qcc_data";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import QccDataModal from "@/views/supplier-quality-summary/qcc-data/components/QccDataModal.vue";
import { useAdminDict } from "@/hooks/useDict";
import { QccData } from "@/typings/supplier-quality-summary/qcc_data";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { download } from "@/api/modules/common";
import LinkFile from "@/views/components/LinkFile.vue";
import { ref, reactive, computed, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import DateRange from "@/views/components/DateRange.vue";

const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

const QccDataModalRef = ref<InstanceType<typeof QccDataModal> | null>(null);
let queryParams = reactive<QccData.IQueryParams>({} as QccData.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

// 表格配置项
const columns = reactive<ColumnProps<QccData.Item>[]>([
  { type: "selection", fixed: "left", width: 50 },
  { prop: "caseNo", label: "案例编号", search: { el: "input" } },
  {
    prop: "plant",
    label: "工厂",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 2 },
    enum: factory,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "supplierName", label: "供应商", search: { el: "input", order: 4 } },
  { prop: "materialType", label: "物料类别", search: { el: "input", order: 3 } },
  { prop: "projectContent", label: "专案内容", width: 300 },
  {
    prop: "improvementReportUrl",
    label: "改善报告",
    width: 200,
    render: ({ row }) => {
      return <LinkFile url={row.improvementReportUrl} name={row.improvementReportName} />;
    }
  },
  { prop: "status", label: "状态" },
  { prop: "planCompleteDate", label: "计划完成日期" },
  { prop: "createBy", label: "创建人" },
  {
    prop: "createAt",
    label: "创建时间",
    search: {
      order: 1,
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  { prop: "updateBy", label: "修改人" },
  { prop: "updateAt", label: "修改时间" }
]);

// 获取表格数据
const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  // 处理日期区间参数
  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }

  queryParams = reactive(condition);
  return getQccData({
    condition,
    pageNum,
    pageSize
  });
};

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};

// 重置搜索
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
  queryParams = reactive<QccData.IQueryParams>({} as QccData.IQueryParams);
};

// 批量删除
const batchDelete = async (id?: number | number[]) => {
  const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(deleteQccData, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 下载文件
const downloadFile = async (ids: number[]) => {
  if (!isEmpty(ids)) {
    // 选中数据导出
    ElMessageBox.confirm(t("确认导出数据?"), t("提示"), { type: "warning" }).then(() => download(exportQccData(ids!)));
  } else {
    // 全部数据导出 - 需要先获取所有数据的IDs
    ElMessage.warning(t("请选择要导出的数据"));
  }
};

const openQccDataModal = (title: string, row: Partial<QccData.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    factory,
    form,
    api: title === "新增" ? createQccData : title === "编辑" ? editQccData : undefined,
    updateApi: editQccData,
    getTableList: proTable.value?.getTableList
  };
  QccDataModalRef.value?.acceptParams(params as any);
};
</script>

<style scoped lang="scss">
.table-box {
  width: 100%;
  height: 100%;
}
</style>
