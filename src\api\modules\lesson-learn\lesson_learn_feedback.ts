import { ReqPage, ResPage } from "@/api/interface/index";
import { LessonLearnFeedback } from "@/typings/lesson-learn/lesson_learn_feedback";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/lessonLearnFeedback`;

// 列表
export const getLessonLearnFeedbackList = (params?: ReqPage) => {
  return http.post<ResPage<LessonLearnFeedback.Item>>(`${baseUrl}/listMyFeedbacks`, params);
};

export const getAllLessonLearnFeedbackList = (params?: ReqPage) => {
  return http.post<ResPage<LessonLearnFeedback.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getLessonLearnFeedbackDetail = (id: number) => {
  return http.post<LessonLearnFeedback.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createLessonLearnFeedback = (data: LessonLearnFeedback.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editLessonLearnFeedback = (data: LessonLearnFeedback.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteLessonLearnFeedback = (id: number) => {
  return http.post(`${baseUrl}/del/${id}`);
};

// 导出
export const exportLessonLearnFeedback = (params?: LessonLearnFeedback.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importLessonLearnFeedback = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importLessonLearnFeedbackTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 提交
export const finish = (data: LessonLearnFeedback.Item) => {
  return http.post(`${baseUrl}/finish`, data);
};
