import { ReqPage, ResPage } from "@/api/interface/index";
import { FaDipMiss } from "@/typings/fa/fa_dip_miss";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/faDIPMiss`;

// 列表
export const getFaDipMissList = (params?: ReqPage) => {
  return http.post<ResPage<FaDipMiss.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getFaDipMissDetail = (id: number) => {
  return http.post<FaDipMiss.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createFaDipMiss = (data: FaDipMiss.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editFaDipMiss = (data: FaDipMiss.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteFaDipMiss = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportFaDipMiss = (params?: FaDipMiss.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importFaDipMiss = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importFaDipMissTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
