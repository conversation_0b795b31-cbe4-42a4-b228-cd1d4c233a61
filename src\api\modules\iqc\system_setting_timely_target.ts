import { ReqPage, ResPage } from "@/api/interface/index";
import { Target } from "@/typings/target";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
const targetMenu = `${API_PREFIX}/processTargetMenu`;
const target = `${API_PREFIX}/iqcTimelinessTarget`;

export const menuTreeSelect = () => {
  return http.post<Target.menu[]>(`${targetMenu}/treeselect`);
};

export const menuCreate = (data: Target.menuCreate) => {
  return http.post(`${targetMenu}/create`, data);
};

export const menuDel = (id: number) => {
  return http.post(`${targetMenu}/del/{id}`);
};

export const targetCreate = (data: Target.target) => {
  return http.post(`${target}/create`, data);
};

export const targetModify = (data: Target.target) => {
  return http.post(`${target}/modify`, data);
};

export const targetList = (params?: ReqPage) => {
  return http.post<ResPage<Target.target>>(`${target}/list`, params);
};

export const targetDel = (ids: number[]) => {
  return http.post(`${target}/del`, { ids });
};
