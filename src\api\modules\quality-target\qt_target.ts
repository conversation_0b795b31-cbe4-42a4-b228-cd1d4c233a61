import { ReqPage, ResPage } from "@/api/interface/index";
import { QtTarget } from "@/typings/quality-target/qt_target";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qtTarget`;

// 列表
export const getQtTargetList = (params?: ReqPage) => {
  return http.post<ResPage<QtTarget.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQtTargetDetail = (id: number) => {
  return http.post<QtTarget.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQtTarget = (data: QtTarget.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQtTarget = (data: QtTarget.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQtTarget = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQtTarget = (params?: QtTarget.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQtTarget = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQtTargetTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
