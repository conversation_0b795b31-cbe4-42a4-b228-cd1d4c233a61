import { API_PREFIX, zxQualityActivityBaseUrl } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}${zxQualityActivityBaseUrl}/plan`;

import http from "@/api";
import { ReqPage, ResPage } from "@/api/interface";

export const getList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/list`, params);
};
export const getLatestNumber = (params?: { prefix?: string }) => {
  return http.post<string>(`${baseUrl}/latest-number`, params);
};

export const create = (data: any) => {
  return http.post(`${baseUrl}/submit`, data);
};

export const update = (data: any) => {
  return http.post(`${baseUrl}/edit`, data);
};

export const submit = (data: any) => {
  return http.post(`${baseUrl}/submit`, data);
};
export const save = (data: any) => {
  return http.post(`${baseUrl}/save`, data);
};

export const remove = (id: number | number[]) => {
  const ids = Array.isArray(id) ? id.join(",") : id;
  return http.post(`${baseUrl}/del/${ids}`);
};

export const getTaskList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/task-list`, params);
};
