import { API_PREFIX, zxQualityActivityBaseUrl } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}${zxQualityActivityBaseUrl}/activity`;

import http from "@/api";
import { ReqPage, ResPage } from "@/api/interface";

export const getList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/list`, params);
};
export const getListByPlanId = (planId: number) => {
  return http.post<ZxQualityActivityActivity.Item[]>(`${baseUrl}/list/${planId}`);
};

export const getLatestNumber = (params?: { prefix?: string }) => {
  return http.post<string>(`${baseUrl}/latest-number`, params);
};

export const create = (data: any) => {
  return http.post(`${baseUrl}/add`, data);
};

export const update = (data: any) => {
  return http.post(`${baseUrl}/edit`, data);
};

export const submit = (data: any) => {
  return http.post(`${baseUrl}/submit`, data);
};
export const save = (data: any) => {
  return http.post(`${baseUrl}/save`, data);
};

export const remove = (id: number | number[]) => {
  const ids = Array.isArray(id) ? id.join(",") : id;
  return http.post(`${baseUrl}/del/${ids}`);
};

export const getTaskList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/task-list`, params);
};

export const assignTask = (data: ZxQualityActivityActivity.Item) => {
  return http.post(`${baseUrl}/assign-task`, {
    activityId: data.id,
    taskList: data.activityTaskList,
    budgetList: data.activityCostBudgetList
  });
};

export const updateCostSummary = (data: ZxQualityActivityActivity.Item) => {
  return http.post(`${API_PREFIX}${zxQualityActivityBaseUrl}/cost-budget/update-price`, {
    activityId: data.id,
    budgetList: data.activityCostBudgetList.map(item => ({
      id: item.id,
      actualCost: item.actualCost
    }))
  });
};
