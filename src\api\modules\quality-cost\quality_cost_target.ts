import { ReqPage, ResPage } from "@/api/interface/index";
import { Target } from "@/typings/target";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
const qualityFailedTargetMenu = `${API_PREFIX}/qualityFailedTargetMenu`;
const qualityFailedTarget = `${API_PREFIX}/qualityFailedTarget`;

export const menuTreeSelect = () => {
  return http.post<Target.menu[]>(`${qualityFailedTargetMenu}/treeselect`);
};

export const menuCreate = (data: Target.menuCreate) => {
  return http.post(`${qualityFailedTargetMenu}/create`, data);
};

export const menuDel = (id: number) => {
  return http.post(`${qualityFailedTargetMenu}/del/{id}`);
};

export const targetCreate = (data: Target.target) => {
  return http.post(`${qualityFailedTarget}/create`, data);
};

export const targetModify = (data: Target.target) => {
  return http.post(`${qualityFailedTarget}/modify`, data);
};

export const targetList = (params?: ReqPage) => {
  return http.post<ResPage<Target.target>>(`${qualityFailedTarget}/list`, params);
};

export const targetDel = (ids: number[]) => {
  return http.post(`${qualityFailedTarget}/del`, { ids });
};
