export namespace QuarterlyData {
  export interface Item {
    id: number;
    plant: string;
    supplierName: string;
    materialType: string;
    iqcInspectionBatchCount: number;
    iqcQualifiedBatchCount: number;
    quarter: string;
    majorQualityIssue: number;
    minorQualityIssue: number;
    dppm: number;
    totalScore: number;
    clientComplaint: number;
    supplierOnsiteArrangement: string;
    qualityImprovementCooperation: string;
    qualityGrade: string;
    redBlackList: string;
    lar: number;
    deleted: number;
    creatEmail: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface IQueryParams {
    startDate: string;
    endDate: string;
    plant: string;
    supplierName: string;
    materialType: string;
  }
  export interface EditParams {
    id: number;
    dppm: number;
    totalScore: number;
    qualityImprovementCooperation: string;
    qualityGrade: string;
    redBlackList: string;
  }
}
