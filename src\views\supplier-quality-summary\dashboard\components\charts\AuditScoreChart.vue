<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">供应商年度审核得分分布</h3>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="AuditScoreChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";

interface Props {
  chartData: Dashboard.TotalScoreData[];
}

const props = defineProps<Props>();

// 检查是否有数据
const hasData = computed(() => {
  return props.chartData && props.chartData.length > 0;
});

// 图表配置
const chartOption = computed<ECOption>(() => {
  if (!hasData.value) return {};

  const suppliers = props.chartData.map(item => item.supplier);
  const scores = props.chartData.map(item => item.score);

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: suppliers,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: "value",
      name: "审核得分",
      min: 0,
      max: 100
    },
    series: [
      {
        name: "审核得分",
        type: "bar",
        data: scores.map(score => ({
          value: score,
          itemStyle: {
            color: getScoreColor(score)
          }
        })),
        label: {
          show: true,
          position: "top"
        }
      }
    ]
  };
});

// 根据得分获取颜色
const getScoreColor = (score: number) => {
  if (score >= 90) return "#91cc75"; // 绿色 - 优秀
  if (score >= 80) return "#5470c6"; // 蓝色 - 良好
  if (score >= 70) return "#fac858"; // 黄色 - 一般
  if (score >= 60) return "#fc8452"; // 橙色 - 及格
  return "#ee6666"; // 红色 - 不及格
};
</script>

<style scoped>
.echarts-container {
  width: 100%;
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.echarts-content {
  height: 350px;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
