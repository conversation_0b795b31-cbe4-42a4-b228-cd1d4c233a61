<template>
  <div class="table-box flex flex-col">
    <!-- 搜索栏 -->
    <div class="search-box">
      <el-form :model="searchForm" inline>
        <el-form-item label="月度">
          <el-date-picker
            v-model="searchForm.monthly"
            type="month"
            placeholder="请选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="工厂/部门">
          <el-select v-model="searchForm.plant" placeholder="请选择工厂/部门" style="width: 200px" @change="onPlantChange">
            <el-option v-for="item in factoryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料类别">
          <el-select
            v-model="searchForm.materialType"
            placeholder="请选择物料类别"
            style="width: 200px"
            @change="onMaterialTypeChange"
          >
            <el-option v-for="item in materialTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select
            v-model="searchForm.supplierName"
            placeholder="请选择供应商"
            multiple
            style="width: 300px"
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-container">
      <!-- 供应商总得分分析 -->
      <div class="chart-card">
        <TotalScoreChart :chart-data="totalScoreData" :search-params="searchParams" @view-detail="handleViewDetail" />
      </div>

      <!-- 客户端投诉数据分析 -->
      <div class="chart-card">
        <ClientComplaintChart :chart-data="clientComplaintData" :search-params="searchParams" @view-detail="handleViewDetail" />
      </div>

      <!-- LAR数据分析 -->
      <div class="chart-card">
        <LarChart :chart-data="larData" :search-params="searchParams" @view-detail="handleViewDetail" />
      </div>

      <!-- 重大制程品质问题分析 -->
      <div class="chart-card">
        <MajorQualityIssueChart
          :chart-data="majorQualityIssueData"
          :search-params="searchParams"
          @view-detail="handleViewDetail"
        />
      </div>
    </div>

    <!-- 明细数据弹窗 -->
    <DetailModal ref="detailModalRef" />
  </div>
</template>

<script setup lang="tsx" name="supplier-quality-summary-monthly-analysis">
import { ref, reactive, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import {
  statsTotalScoreData,
  statsClientComplaintData,
  statsLarData,
  statsMajorQualityIssueData,
  getSupplierData
} from "@/api/modules/supplier-quality-summary/supplier_quality_summary_statistics_monthly";
import { Statistics } from "@/typings/supplier-quality-summary/statistics_monthly";
import { useAdminDict } from "@/hooks/useDict";
import TotalScoreChart from "./components/TotalScoreChart.vue";
import ClientComplaintChart from "./components/ClientComplaintChart.vue";
import LarChart from "./components/LarChart.vue";
import MajorQualityIssueChart from "./components/MajorQualityIssueChart.vue";
import DetailModal from "./components/DetailModal.vue";

const { t } = useI18n();
const { factory } = useAdminDict("factory");

// 搜索表单
const searchForm = reactive({
  monthly: dayjs().format("YYYY-MM"), // 默认当前月份
  plant: "深圳", // 默认深圳
  materialType: "",
  supplierName: [] as string[]
});

// 搜索参数（用于传递给图表组件）
const searchParams = ref<Statistics.IQueryParams>({} as Statistics.IQueryParams);

// 下拉选项
const factoryOptions = computed(() => factory.value.map(item => ({ label: item.label, value: item.value })));
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const supplierOptions = ref<{ label: string; value: string }[]>([]);

// 图表数据
const totalScoreData = ref<Statistics.TotalScoreData[]>([]);
const clientComplaintData = ref<Statistics.ClientComplaintData[]>([]);
const larData = ref<Statistics.LarMonthlyData[]>([]);
const majorQualityIssueData = ref<Statistics.MajorQualityIssueData[]>([]);

// 明细弹窗引用
const detailModalRef = ref<InstanceType<typeof DetailModal> | null>(null);

// 工厂变更处理
const onPlantChange = async () => {
  if (!searchForm.plant) return;

  try {
    const { data } = await getSupplierData({
      condition: { plant: searchForm.plant },
      pageNum: 1,
      pageSize: 1000
    });

    // 更新物料类别选项
    const materialTypes = [...new Set(data.list.map(item => item.materialType))];
    materialTypeOptions.value = materialTypes.map(type => ({ label: type, value: type }));

    // 默认选择第一个物料类别
    if (materialTypes.length > 0) {
      searchForm.materialType = materialTypes[0];
      await onMaterialTypeChange();
    } else {
      searchForm.materialType = "";
      supplierOptions.value = [];
      searchForm.supplierName = [];
    }
  } catch (error) {
    console.error("获取物料类别失败:", error);
    ElMessage.error("获取物料类别失败");
  }
};

// 物料类别变更处理
const onMaterialTypeChange = async () => {
  if (!searchForm.plant || !searchForm.materialType) return;

  try {
    const { data } = await getSupplierData({
      condition: {
        plant: searchForm.plant,
        materialType: searchForm.materialType
      },
      pageNum: 1,
      pageSize: 1000
    });

    // 更新供应商选项
    const suppliers = [...new Set(data.list.map(item => item.supplierName))];
    supplierOptions.value = suppliers.map(supplier => ({ label: supplier, value: supplier }));

    // 默认选中所有供应商
    searchForm.supplierName = suppliers;
  } catch (error) {
    console.error("获取供应商失败:", error);
    ElMessage.error("获取供应商失败");
  }
};

// 查询处理
const handleSearch = async () => {
  if (!searchForm.monthly || !searchForm.plant || !searchForm.materialType || searchForm.supplierName.length === 0) {
    ElMessage.warning("请完善搜索条件");
    return;
  }

  searchParams.value = {
    monthly: searchForm.monthly,
    plant: searchForm.plant,
    materialType: searchForm.materialType,
    supplierName: searchForm.supplierName
  };

  await loadChartData();
};

// 重置处理
const handleReset = () => {
  searchForm.monthly = dayjs().format("YYYY-MM");
  searchForm.plant = "深圳";
  searchForm.materialType = "";
  searchForm.supplierName = [];
  materialTypeOptions.value = [];
  supplierOptions.value = [];

  // 清空图表数据
  totalScoreData.value = [];
  clientComplaintData.value = [];
  larData.value = [];
  majorQualityIssueData.value = [];
  searchParams.value = {} as Statistics.IQueryParams;
};

// 加载图表数据
const loadChartData = async () => {
  try {
    const [totalScore, clientComplaint, lar, majorQualityIssue] = await Promise.all([
      statsTotalScoreData(searchParams.value),
      statsClientComplaintData(searchParams.value),
      statsLarData(searchParams.value),
      statsMajorQualityIssueData(searchParams.value)
    ]);

    console.log("总得分数据:", totalScore.data);
    totalScoreData.value = totalScore.data || [];
    clientComplaintData.value = clientComplaint.data || [];
    larData.value = lar.data || [];
    majorQualityIssueData.value = majorQualityIssue.data || [];
  } catch (error) {
    console.error("加载图表数据失败:", error);
    ElMessage.error("加载图表数据失败");
  }
};

// 查看明细数据
const handleViewDetail = (chartType: string, data: any) => {
  detailModalRef.value?.open(chartType, data, searchParams.value);
};

// 初始化
onMounted(async () => {
  // 初始化工厂选择
  await onPlantChange();
});
</script>

<style lang="scss" scoped>
.search-box {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
</style>
