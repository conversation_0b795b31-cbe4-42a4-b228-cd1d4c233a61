import { ReqPage, ResPage } from "@/api/interface/index";
import { ObaSmtExceeded } from "@/typings/oba-smt/oba_smt_exceeded";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/obaSMTExceeded`;

// 列表
export const getObaSmtExceededList = (params?: ReqPage) => {
  return http.post<ResPage<ObaSmtExceeded.Item>>(`${baseUrl}/list`, params);
};

//操作记录
export const getChangeLogList = (params?: ReqPage) => {
  return http.post<ResPage<ObaSmtExceeded.Log>>(`${baseUrl}/getChangeLogList`, params);
};

// 详情
export const getObaSmtExceededDetail = (id: number) => {
  return http.post<ObaSmtExceeded.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createObaSmtExceeded = (data: ObaSmtExceeded.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editObaSmtExceeded = (data: ObaSmtExceeded.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteObaSmtExceeded = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportObaSmtExceeded = (params?: ObaSmtExceeded.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importObaSmtExceeded = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importObaSmtExceededTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
