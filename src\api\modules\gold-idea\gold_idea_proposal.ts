import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { ProposalList } from "@/typings/gold-idea/gold_idea_proposal";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/proposal`;

// 我的提案列表
export const getMyProposalList = (params?: ReqPage) => {
  return http.post<ResPage<ProposalList.Item>>(`${baseUrl}/myList`, params);
};

// 查询所有提案
export const getAllProposalList = (params?: ReqPage) => {
  return http.post<ResPage<ProposalList.Item>>(`${baseUrl}/list`, params);
};

// 获取提案详情
export const getProposalDetail = (id: number) => {
  return http.get<Partial<ProposalList.Item>>(`${baseUrl}/get/${id}`);
};

// 修改提案
export const editProposal = (data: ProposalList.Item) => {
  return http.post(`${baseUrl}/editPending`, data);
};

// 提交
export const submitProposal = (data: ProposalList.Item) => {
  return http.post(`${baseUrl}/submitPending`, data);
};

// 删除
export const deleteProposal = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportProposalList = (params?: ProposalList.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};
