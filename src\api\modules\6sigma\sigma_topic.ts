import { ReqPage, ResPage } from "@/api/interface/index";
import { SigmaTopic } from "@/typings/6sigma/sigma_topic";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/6sigmaTopic`;

// 列表
export const getSigmaTopicList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTopic.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSigmaTopicDetail = (id: number) => {
  return http.post<SigmaTopic.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSigmaTopic = (data: SigmaTopic.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSigmaTopic = (data: SigmaTopic.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

//评分
export const scoreSigmaTopic = (data: SigmaTopic.Item) => {
  return http.post(`${baseUrl}/score`, data);
};

// 删除
export const deleteSigmaTopic = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSigmaTopic = (params?: SigmaTopic.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSigmaTopic = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

// 根据期数查询课题
export const listToPeriodNum = () => {
  return http.post<string[]>(`${baseUrl}/listToPeriodNum`);
};
//下载模板
export const importSigmaTopicTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 提交
export const submitSigmaTopic = (id: number) => {
  return http.post(`${baseUrl}/submit/${id}`);
};

// 流程信息
export const getFlow = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTopic.Detail>>(`${baseUrl}/processInf`, params);
};

// 新增月度收益
export const createSigmaTopicMonthlyBenefits = (data: { topicId: number }) => {
  return http.post<SigmaTopic.MonthlyBenefits>(`${baseUrl}/monthlyBenefits/create`, data);
};

// 修改月度收益
export const editSigmaTopicMonthlyBenefits = (data: SigmaTopic.MonthlyBenefits) => {
  return http.post<SigmaTopic.MonthlyBenefits>(`${baseUrl}/monthlyBenefits/update`, data);
};

// 删除
export const deleteSigmaTopicMonthlyBenefits = (id: number) => {
  return http.post(`${baseUrl}/monthlyBenefits/delete/${id}`);
};

// 月度收益列表
export const getSigmaTopicMonthlyBenefitsList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTopic.MonthlyBenefits>>(`${baseUrl}/listMb`, params);
};

// 退回
export const backSigmaTopic = (params: { id: number; rejectReason: string }) => {
  return http.post<SigmaTopic.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const auditSigmaTopic = (id: number) => {
  return http.post<SigmaTopic.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getSigmaTopicApproveList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTopic.Item>>(`${baseUrl}/listToBeAudited`, params);
};
//审批详情
export const getSigmaTopicApproveDetail = (id: number) => {
  return http.post<SigmaTopic.Item>(`${baseUrl}/listToBeAudited/get/${id}`);
};
// 我的任务列表
export const getSigmaTopicMyList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTopic.Item>>(`${baseUrl}/listToMyQuest`, params);
};

// 我的任务详情
export const getSigmaTopicMyListDetail = (id: number) => {
  return http.post<SigmaTopic.Item>(`${baseUrl}/listToMyQuest/get/${id}`);
};

export const submitDetailToAudit = (id: number) => {
  return http.post<SigmaTopic.Detail>(`${baseUrl}/submitDetailToAudit/${id}`);
};
// 我的任务详情
export const getSigmaTopicDetailList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTopic.Detail>>(`${baseUrl}/listDetail`, params);
};
// 修改
export const modifyDetail = (data: SigmaTopic.Detail) => {
  return http.post<SigmaTopic.Detail>(`${baseUrl}/modifyDetail`, data);
};

// 首页看板
export const getSigmaTopicBoard = () => {
  return http.post<SigmaTopic.Board>(`${baseUrl}/board`);
};

//根据部门统计课题数
export const statsByDeptTopic = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsByDeptTopic[]>(`${baseUrl}/statsByDeptTopic`, params);
};
//根据期数统计课题数
export const statsByPeriodTopic = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsByPeriodTopic[]>(`${baseUrl}/statsByPeriodTopic`, params);
};
//按部门统计节约成本
export const statsCostSavingByDept = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsCostSavingByDept[]>(`${baseUrl}/statsCostSavingByDept`, params);
};
//按期数统计节约成本
export const statsCostSavingByPeriod = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsCostSavingByPeriod[]>(`${baseUrl}/statsCostSavingByPeriod`, params);
};
//按部门统计状态
export const statsByDeptStatus = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsByDeptStatus[]>(`${baseUrl}/statsByDeptStatus`, params);
};
//按期数统计状态
export const statsByPeriodStatus = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsByPeriodStatus[]>(`${baseUrl}/statsByPeriodStatus`, params);
};
//按部门统计参与人员
export const statsByDeptMember = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsByDeptMember[]>(`${baseUrl}/statsByDeptMember`, params);
};

//按期数统计参与人员
export const statsByPeriodMember = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsByPeriodMember[]>(`${baseUrl}/statsByPeriodMember`, params);
};
//按期数统计节约金额
export const statsByPeriodSavingTotal = (params?: SigmaTopic.StatsQueryParams) => {
  return http.post<SigmaTopic.statsByPeriodSavingTotal>(`${baseUrl}/statsByPeriodSavingTotal`, params);
};
