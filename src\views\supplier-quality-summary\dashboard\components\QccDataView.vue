<template>
  <div class="qcc-data-view">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="日期">
          <el-date-picker
            v-model="searchForm.statsDateStart"
            type="date"
            placeholder="开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 150px"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="—">
          <el-date-picker
            v-model="searchForm.statsDateEnd"
            type="date"
            placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 150px"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-container">
      <!-- QCC专案数量分布 -->
      <div class="chart-card">
        <QccProjectChart :chart-data="chartData.qccProjectCount" />
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="table-section">
      <div class="table-header">
        <h3>QCC专案详情</h3>
        <el-button type="primary" @click="exportData">导出</el-button>
      </div>
      <el-table :data="tableData" border stripe max-height="400" style="width: 100%">
        <el-table-column prop="caseNo" label="案例编号" width="150" />
        <el-table-column prop="plant" label="工厂" width="120" />
        <el-table-column prop="supplier" label="供应商" width="150" />
        <el-table-column prop="materialType" label="物料类别" width="120" />
        <el-table-column prop="projectContent" label="专案内容" width="200" show-overflow-tooltip />
        <el-table-column prop="revenueSummary" label="收益汇总" width="150" />
        <el-table-column prop="status" label="状态" width="100" />
        <el-table-column prop="planCompleteDate" label="计划完成日期" width="120" />
        <el-table-column prop="reportName" label="报告名称" width="150" show-overflow-tooltip />
        <el-table-column label="报告" width="100">
          <template #default="{ row }">
            <el-button v-if="row.reportUrl" type="primary" size="small" @click="downloadReport(row.reportUrl, row.reportName)">
              下载
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="tsx" name="QccDataView">
import { ref, reactive, onMounted, watch } from "vue";

import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { statsQccData } from "@/api/modules/supplier-quality-summary/supplier_quality_summary_dashboard";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";
import QccProjectChart from "./charts/QccProjectChart.vue";

interface Props {
  filterForm: {
    plant: string;
    materialType: string;
  };
}

const props = defineProps<Props>();

// 搜索表单 - 默认最近三个月
const searchForm = reactive({
  statsDateStart: dayjs().subtract(3, "month").format("YYYY-MM-DD"),
  statsDateEnd: dayjs().format("YYYY-MM-DD")
});

// 图表数据
const chartData = ref<Dashboard.QccData>({
  qccProjectCount: [],
  list: []
});

// 表格数据
const tableData = ref<Dashboard.QccData["list"]>([]);

// 日期变更处理
const handleDateChange = () => {
  if (props.filterForm.materialType) {
    loadData();
  }
};

// 加载数据
const loadData = async () => {
  // 确保materialType不为空
  if (!props.filterForm.materialType) {
    console.warn("materialType为空，跳过加载QCC数据");
    return;
  }

  try {
    const params: Dashboard.IQueryParamsQccData = {
      statsDateStart: searchForm.statsDateStart,
      statsDateEnd: searchForm.statsDateEnd,
      plant: props.filterForm.plant,
      materialType: props.filterForm.materialType
    };

    const response = await statsQccData(params);

    if (response.data) {
      chartData.value = response.data;
      tableData.value = response.data.list || [];
    }
  } catch (error) {
    console.error("加载QCC数据失败:", error);
    ElMessage.error("加载QCC数据失败");
  }
};

// 下载报告
const downloadReport = (url: string, fileName: string) => {
  if (!url) {
    ElMessage.warning("报告文件不存在");
    return;
  }

  const link = document.createElement("a");
  link.href = url;
  link.download = fileName || "QCC报告";
  link.target = "_blank";
  link.click();
};

// 导出数据
const exportData = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning("暂无数据可导出");
    return;
  }

  const headers = ["案例编号", "工厂", "供应商", "物料类别", "专案内容", "收益汇总", "状态", "计划完成日期", "报告名称"];
  const csvContent = [
    headers.join(","),
    ...tableData.value.map(row =>
      [
        row.caseNo,
        row.plant,
        row.supplier,
        row.materialType,
        `"${row.projectContent}"`, // 用引号包围可能包含逗号的内容
        row.revenueSummary,
        row.status,
        row.planCompleteDate,
        `"${row.reportName}"`
      ].join(",")
    )
  ].join("\n");

  const blob = new Blob(["\uFEFF" + csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = `QCC专案数据_${dayjs().format("YYYY-MM-DD")}.csv`;
  link.click();
};

// 监听筛选条件变化
watch(
  () => [props.filterForm.plant, props.filterForm.materialType],
  () => {
    // 只有当materialType不为空时才加载数据
    if (props.filterForm.materialType) {
      loadData();
    }
  },
  { deep: true }
);

// 初始化
onMounted(() => {
  // 只有当materialType不为空时才加载数据
  if (props.filterForm.materialType) {
    loadData();
  }
});
</script>

<style scoped>
.qcc-data-view {
  padding: 20px 0;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #333;
}
</style>
