<template>
  <div id="supplier-audit-staff-add" class="no-min-height-table">
    <el-table :data="dataList" style="width: 100%">
      <el-table-column :label="$t('序号')" type="index" width="80"></el-table-column>
      <el-table-column :label="$t('姓名')" prop="staffName"></el-table-column>
      <el-table-column :label="$t('工号')" prop="staffNo"></el-table-column>
      <el-table-column :label="$t('邮箱')" prop="staffEmail"></el-table-column>
      <el-table-column :label="$t('顺序')" prop="sort"></el-table-column>
      <el-table-column v-if="!isView" :label="$t('操作')" width="140">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.$index, scope.row)">{{ $t("编辑") }}</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.$index)">{{ $t("删除") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button v-if="!isView" type="primary" @click="handleAdd" style="margin-top: 10px">{{ $t("新增") }}</el-button>

    <el-dialog v-model="dialogVisible" width="400px" :title="dialogTitle">
      <el-form ref="formRef" :model="form" label-width="80px" :rules="rules">
        <el-row>
          <el-col :span="20">
            <el-form-item :label="$t('姓名')" prop="staffName" style="margin-bottom: 18px">
              <RemoteSearch :job-num="form.staffNo" v-model="form.staffName" @extra="staffExtra" />
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item :label="$t('顺序')" prop="sort" style="margin-bottom: 18px">
              <el-input v-model="form.sort" :placeholder="$t('请填写')" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">{{ $t("取消") }}</el-button>
        <el-button type="primary" @click="handleSubmit">{{ $t("保存") }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import RemoteSearch from "@/views/components/RemoteSearch.vue";
import { Staff } from "@/typings/staff";

const props = defineProps({
  data: { type: Array, default: () => [] },
  isView: { type: Boolean, default: false }
});
const emit = defineEmits(["updateData"]);
const { t } = useI18n();

const dataList = ref<any[]>([...(props.data ?? [])]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref<FormInstance>();
const form = reactive<any>({
  index: -1,
  staffName: "",
  staffNo: "",
  staffEmail: "",
  sort: ""
});

const rules = {
  staffName: [{ required: true, message: t("请填写姓名"), trigger: "blur" }],
  staffNo: [{ required: true, message: t("请填写工号"), trigger: "blur" }],
  staffEmail: [{ required: true, message: t("请填写邮箱"), trigger: "blur" }],
  sort: [{ required: true, message: t("请填写顺序"), trigger: "blur" }]
};

const staffExtra = (staff: Staff.Extra) => {
  Object.assign(form, {
    staffEmail: staff.email,
    staffNo: staff.jobNum,
    staffName: staff.name
  });
};

const handleSubmit = () => {
  formRef.value?.validate(valid => {
    if (!valid) return;
    const updatedData = {
      staffName: form.staffName,
      staffNo: form.staffNo,
      staffEmail: form.staffEmail,
      sort: form.sort
    };
    if (form.index === -1) {
      dataList.value.push(updatedData);
    } else {
      dataList.value[form.index] = updatedData;
    }
    emit("updateData", dataList.value);
    dialogVisible.value = false;
  });
};

const handleAdd = () => {
  dialogTitle.value = t("新增");
  form.index = -1;
  Object.assign(form, {
    staffName: "",
    staffNo: "",
    staffEmail: "",
    sort: ""
  });
  dialogVisible.value = true;
};

const handleEdit = (index: number, row: any) => {
  dialogTitle.value = t("编辑");
  Object.assign(form, {
    index,
    staffName: row.staffName,
    staffNo: row.staffNo,
    staffEmail: row.staffEmail,
    sort: row.sort
  });
  dialogVisible.value = true;
};

const handleDelete = (index: number) => {
  dataList.value.splice(index, 1);
  emit("updateData", dataList.value);
};
</script>

<style scoped>
#supplier-audit-staff-add .el-table .el-table__body-wrapper {
  min-height: auto;
}
</style>
