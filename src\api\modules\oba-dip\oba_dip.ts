import { ReqPage, ResPage } from "@/api/interface/index";
import { ObaDip } from "@/typings/oba-dip/oba_dip";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/obaDIP`;

// 列表
export const getObaDipList = (params?: ReqPage) => {
  return http.post<ResPage<ObaDip.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getObaDipDetail = (id: number) => {
  return http.post<ObaDip.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createObaDip = (data: ObaDip.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editObaDip = (data: ObaDip.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteObaDip = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportObaDip = (params?: ObaDip.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importObaDip = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importObaDipTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
