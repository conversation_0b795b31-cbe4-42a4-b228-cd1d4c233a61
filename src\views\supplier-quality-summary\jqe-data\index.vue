<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :search-col="{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }"
      :init-param="initParam"
      @reset="resetForm()"
    >
      <template #tableHeader="{ selectedListIds }">
        <el-button v-auth="'supplier-quality-summary-jqe-data:add'" type="primary" @click="openJqeDataModal('新增')">{{
          $t("新增")
        }}</el-button>
        <el-button v-auth="'supplier-quality-summary-jqe-data:edit'" type="primary" @click="openJqeDataModal('编辑')">{{
          $t("编辑")
        }}</el-button>
        <el-button
          v-auth="'supplier-quality-summary-jqe-data:delete'"
          type="danger"
          @click="batchDelete(selectedListIds as number[])"
          >{{ $t("删除") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-jqe-data:export'"
          type="primary"
          @click="downloadFile(selectedListIds as number[])"
          >{{ $t("导出") }}
        </el-button>
      </template>
    </ProTable>
    <JqeDataModal ref="JqeDataModalRef" />
  </div>
</template>

<script setup lang="tsx" name="supplier-quality-summary-jqe-data">
import {
  getJqeData,
  createJqeData,
  editJqeData,
  deleteJqeData,
  exportJqeData,
  getSupplierData
} from "@/api/modules/supplier-quality-summary/supplier_quality_summary_jqe_data";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import JqeDataModal from "@/views/supplier-quality-summary/jqe-data/components/JqeDataModal.vue";
import { useAdminDict } from "@/hooks/useDict";
import { JqeData } from "@/typings/supplier-quality-summary/jqe_data";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { download } from "@/api/modules/common";
import { ref, reactive } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import DateRange from "@/views/components/DateRange.vue";

const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

const JqeDataModalRef = ref<InstanceType<typeof JqeDataModal> | null>(null);
let queryParams = reactive<JqeData.IQueryParams>({} as JqeData.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

// 日期筛选
const filterDate = ref<string[]>([]);
const resetCounter = ref(0);

const handleSelectedDates = (dates: string[]) => {
  filterDate.value = dates;
};

const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
  queryParams = reactive<JqeData.IQueryParams>({} as JqeData.IQueryParams);
};

// 表格配置项
const columns = reactive<ColumnProps<JqeData.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  { prop: "monthly", label: "月份", width: 120 },
  {
    prop: "plant",
    label: "工厂",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 2 },
    enum: factory,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "supplierName", label: "供应商", search: { el: "input", order: 4 } },
  { prop: "materialType", label: "物料类别", search: { el: "input", order: 3 } },
  {
    prop: "isJqeAssigned",
    label: "是否安排JQE",
    render: ({ row }) => {
      return row.isJqeAssigned ? "是" : "否";
    }
  },
  {
    prop: "wasBlacklistedLastQuarter",
    label: "上季度是否黑榜供应商",
    render: ({ row }) => {
      return row.wasBlacklistedLastQuarter ? "是" : "否";
    }
  },
  { prop: "createBy", label: "创建人" },
  {
    prop: "createAt",
    label: "创建时间",
    search: {
      order: 1,
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  { prop: "updateBy", label: "修改人" },
  { prop: "updateAt", label: "修改时间" }
]);

// 获取表格数据
const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  // 处理日期区间参数
  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }

  queryParams = reactive(condition);
  return getJqeData({
    condition,
    pageNum,
    pageSize
  });
};

// 批量删除
const batchDelete = async (id?: number | number[]) => {
  const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(deleteJqeData, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 下载文件
const downloadFile = async (ids: number[]) => {
  if (!isEmpty(ids)) {
    // 选中数据导出
    ElMessageBox.confirm(t("确认导出数据?"), t("提示"), { type: "warning" }).then(() => download(exportJqeData(ids!)));
  } else {
    // 全部数据导出 - 需要先获取所有数据的IDs
    ElMessage.warning(t("请选择要导出的数据"));
  }
};

const openJqeDataModal = (title: string, row: Partial<JqeData.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : currentRow.value ? { ...currentRow.value } : {};

  const params = {
    title,
    isView: title === "查看",
    factory,
    form,
    api: title === "新增" ? createJqeData : title === "编辑" ? editJqeData : undefined,
    updateApi: editJqeData,
    getTableList: proTable.value?.getTableList
  };
  JqeDataModalRef.value?.acceptParams(params as any);
};
</script>

<style scoped lang="scss">
.table-box {
  width: 100%;
  height: 100%;
}
</style>
