import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityPersonCertificate } from "@/typings/quality-person/quality_person_certificate";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qualityPersonCertificate`;

// 列表
export const getQualityPersonCertificateList = (params?: ReqPage) => {
  return http.post<ResPage<QualityPersonCertificate.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityPersonCertificateDetail = (id: number) => {
  return http.post<QualityPersonCertificate.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityPersonCertificate = (data: QualityPersonCertificate.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityPersonCertificate = (data: QualityPersonCertificate.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityPersonCertificate = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQualityPersonCertificate = (params?: QualityPersonCertificate.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQualityPersonCertificate = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadDataset`, formData);
};

//下载模板
export const importQualityPersonCertificateTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
