<template>
  <div class="echarts-container">
    <!-- 新增数据展示区块 -->
    <div class="data-list">
      <div v-for="(item, index) in props.data" :key="index" class="data-item" :style="{ backgroundColor: getColor(index) }">
        <span class="label">{{ item.name }}</span>
        <span class="value">{{ item.value }}</span>
      </div>
    </div>
    <!-- 原有图表容器 -->
    <div class="echarts">
      <ECharts :option="option" />
    </div>
  </div>
</template>

<script setup lang="ts" name="pie">
import { ECOption } from "@/components/ECharts/config";
import ECharts from "@/components/ECharts/index.vue";
import { defineProps } from "vue";

const props = defineProps({
  data: {
    type: Array as () => Array<{
      name: string;
      value: number;
    }>,
    required: true
  }
});

// 修改图例和标签样式
const option: ECOption = {
  title: {
    // text: "Gitee / GitHub",
    // subtext: "访问占比",
    left: "56%",
    top: "45%",
    textAlign: "center",
    textStyle: {
      fontSize: 18,
      color: "#767676"
    },
    subtextStyle: {
      fontSize: 15,
      color: "#a1a1a1"
    }
  },
  tooltip: {
    trigger: "item"
  },
  legend: {
    bottom: 0,
    orient: "horizontal",
    type: "scroll", // 新增滚动容器
    itemGap: 10, // 缩小间距
    itemWidth: 16,
    itemHeight: 10,
    padding: [10, 20], // 减少左右边距
    pageIconSize: 10, // 翻页按钮大小
    textStyle: {
      color: "#fff" // 新增文字颜色设置
    },
    pageTextStyle: {
      color: "#fff" // 修改翻页文字颜色
    }
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "60%"], // 调整饼图半径
      center: ["50%", "35%"], // 上移饼图位置
      silent: true,
      clockwise: true,
      startAngle: 150,
      data: props.data, // 使用传入的数据
      label: {
        position: "outside",
        show: true,
        formatter: (params: any) => `${params.value}`, // 改为显示实际数值
        fontWeight: 400,
        fontSize: 14, // 减小字号
        color: "#fff",
        distance: 15, // 调整标签距离
        padding: [5, 10] // 增加内边距
      },
      labelLine: {
        length: 10, // 缩短引导线
        length2: 20,
        lineStyle: {
          width: 1
        }
      },
      color: [
        {
          // 绿色渐变
          type: "linear",
          x: 0,
          y: 0,
          x2: 0.5,
          y2: 1,
          colorStops: [
            { offset: 0, color: "#00cc66" },
            { offset: 1, color: "#00994d" }
          ]
        },
        {
          // 蓝色渐变
          type: "linear",
          x: 0,
          y: 0,
          x2: 0.5,
          y2: 1,
          colorStops: [
            { offset: 0, color: "#0066cc" },
            { offset: 1, color: "#004499" }
          ]
        },
        {
          // 红色渐变
          type: "linear",
          x: 0,
          y: 0,
          x2: 0.5,
          y2: 1,
          colorStops: [
            { offset: 0, color: "#ff0000" },
            { offset: 1, color: "#cc0000" }
          ]
        }
      ]
    }
  ]
};

// 新增颜色匹配逻辑
const colors = [
  "#00cc66", // 绿色
  "#0066cc", // 蓝色
  "#ff0000" // 红色
];

const getColor = (index: number) => colors[index % colors.length];
</script>

<style lang="scss" scoped>
.echarts-container {
  display: flex;
  width: 100%;
  height: 100%;
}
.data-list {
  display: flex; // 新增flex布局
  flex: 0 0 25%;
  flex-direction: column; // 垂直排列
  justify-content: center; // 垂直居中
  height: 100%; // 确保高度充满容器
  padding-right: 10px;

  // margin: 0 5px;
}
.data-item {
  display: flex;
  flex-direction: column; // 改为垂直布局
  align-items: center; // 新增水平居中
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 12px;
  .label {
    margin-bottom: 5px; // 增加下边距分隔
    font-size: 14px;
    color: white; // 确保该属性存在
  }
  .value {
    font-size: 16px; // 加大字号
    font-weight: bold;
    color: white; // 确保该属性存在
  }
}
.echarts {
  flex: 1;
  height: 100%;
  margin-top: 10px;
}
</style>
