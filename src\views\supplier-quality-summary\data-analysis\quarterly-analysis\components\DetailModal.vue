<template>
  <el-dialog v-model="visible" :title="modalTitle" width="800px" :destroy-on-close="true" :close-on-click-modal="false">
    <div class="detail-content">
      <!-- 导出按钮 -->
      <div class="action-bar">
        <el-button type="primary" @click="handleExport">
          <el-icon><Upload /></el-icon>
          导出
        </el-button>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" border stripe style="width: 100%">
        <el-table-column prop="quarter" label="季度" width="120" />
        <el-table-column prop="plant" label="工厂" width="120" />
        <el-table-column prop="supplierName" label="供应商" width="150" />
        <el-table-column prop="materialType" label="物料类别" width="120" />
        <el-table-column v-if="showScoreColumn" prop="score" label="总得分" width="100" />
        <el-table-column v-if="showGradeColumn" prop="name" label="品质等级" width="120" />
        <el-table-column v-if="showGradeColumn" prop="value" label="数量" width="100" />
        <el-table-column v-if="showComplaintColumn" prop="clientComplaint" label="客户投诉次数" width="120" />
        <el-table-column v-if="showLarColumn" prop="lar" label="LAR (%)" width="100" />
        <el-table-column v-if="showQualityIssueColumn" prop="actual" label="实际值 (%)" width="100" />
        <el-table-column v-if="showTargetColumn" prop="target" label="目标值" width="100" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="tsx" name="DetailModal">
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { Upload } from "@element-plus/icons-vue";
import { Statistics } from "@/typings/supplier-quality-summary/statistics_quarterly";

interface DetailData {
  quarter: string;
  plant: string;
  supplierName: string;
  materialType: string;
  score?: number;
  name?: string;
  value?: number;
  clientComplaint?: number;
  lar?: number;
  actual?: number;
  target?: number;
}

const visible = ref(false);
const chartType = ref("");
const searchParams = ref<Statistics.IQueryParams>({} as Statistics.IQueryParams);
const rawData = ref<any[]>([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 模态框标题
const modalTitle = computed(() => {
  const titleMap: Record<string, string> = {
    totalScore: "供应商季度总得分明细数据",
    gradeDistribution: "品质等级分布明细数据",
    clientComplaint: "客户端投诉明细数据",
    lar: "LAR明细数据",
    majorQualityIssue: "重大制程品质问题明细数据"
  };
  return titleMap[chartType.value] || "明细数据";
});

// 显示列控制
const showScoreColumn = computed(() => chartType.value === "totalScore");
const showGradeColumn = computed(() => chartType.value === "gradeDistribution");
const showComplaintColumn = computed(() => chartType.value === "clientComplaint");
const showLarColumn = computed(() => chartType.value === "lar");
const showQualityIssueColumn = computed(() => chartType.value === "majorQualityIssue");
const showTargetColumn = computed(() => ["clientComplaint", "lar", "majorQualityIssue"].includes(chartType.value));

// 表格数据
const tableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return processedData.value.slice(start, end);
});

// 处理后的数据
const processedData = computed(() => {
  if (!rawData.value.length) return [];

  const result: DetailData[] = [];

  // 根据图表类型生成明细数据
  rawData.value.forEach(item => {
    const baseData: DetailData = {
      quarter: searchParams.value.quarter,
      plant: searchParams.value.plant,
      supplierName: chartType.value === "gradeDistribution" ? "全部供应商" : item.supplier,
      materialType: searchParams.value.materialType
    };

    switch (chartType.value) {
      case "totalScore":
        result.push({
          ...baseData,
          score: item.score
        });
        break;
      case "gradeDistribution":
        result.push({
          ...baseData,
          name: item.name,
          value: item.value
        });
        break;
      case "clientComplaint":
        result.push({
          ...baseData,
          clientComplaint: item.clientComplaint,
          target: item.target
        });
        break;
      case "lar":
        result.push({
          ...baseData,
          lar: item.lar,
          target: item.target
        });
        break;
      case "majorQualityIssue":
        result.push({
          ...baseData,
          actual: item.actual,
          target: item.target
        });
        break;
    }
  });

  return result;
});

// 打开弹窗
const open = (type: string, data: any[], params: Statistics.IQueryParams) => {
  chartType.value = type;
  rawData.value = data;
  searchParams.value = params;
  total.value = data.length;
  currentPage.value = 1;
  visible.value = true;
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

// 导出处理
const handleExport = () => {
  if (!processedData.value.length) {
    ElMessage.warning("暂无数据可导出");
    return;
  }

  try {
    // 创建CSV内容
    const headers = ["季度", "工厂", "供应商", "物料类别"];

    if (showScoreColumn.value) headers.push("总得分");
    if (showGradeColumn.value) headers.push("品质等级", "数量");
    if (showComplaintColumn.value) headers.push("客户投诉次数");
    if (showLarColumn.value) headers.push("LAR (%)");
    if (showQualityIssueColumn.value) headers.push("实际值");
    if (showTargetColumn.value) headers.push("目标值");

    const csvContent = [
      headers.join(","),
      ...processedData.value.map(row => {
        const values = [row.quarter, row.plant, row.supplierName, row.materialType];

        if (showScoreColumn.value) values.push(String(row.score || ""));
        if (showGradeColumn.value) values.push(String(row.name || ""), String(row.value || ""));
        if (showComplaintColumn.value) values.push(String(row.clientComplaint || ""));
        if (showLarColumn.value) values.push(String(row.lar || ""));
        if (showQualityIssueColumn.value) values.push(String(row.actual || ""));
        if (showTargetColumn.value) values.push(String(row.target || ""));

        return values.join(",");
      })
    ].join("\n");

    // 创建下载链接
    const blob = new Blob(["\ufeff" + csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `${modalTitle.value}_${new Date().getTime()}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success("导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败");
  }
};

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.detail-content {
  .action-bar {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
