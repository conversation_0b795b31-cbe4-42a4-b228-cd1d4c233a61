<!-- eslint-disable vue/no-unused-vars -->
<template>
  <div class="progress-update-table">
    <div class="table-header">
      <el-button v-if="!isView" type="primary" @click="addNewRow">{{ $t("添加行") }}</el-button>
    </div>

    <el-table :data="displayList" border style="width: 100%">
      <el-table-column type="index" label="序号" width="60" align="center" />

      <el-table-column :prop="fieldName" :label="fieldLabel" min-width="300">
        <template #default="{ row }">
          <el-input v-if="row.isEditing" v-model="row[fieldName]" :placeholder="$t('请输入') + fieldLabel" />
          <span v-else>{{ row[fieldName] || "-" }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="week" label="周" width="150">
        <template #default="{ row }">
          <el-input v-if="row.isEditing" v-model="row.week" :placeholder="$t('请输入周')" />
          <span v-else>{{ row.week || "-" }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" align="center">
        <template #default="{ row, $index }">
          <div v-if="!isView">
            <template v-if="row.isEditing">
              <el-button type="primary" size="small" @click="saveRow(row)">
                {{ $t("保存") }}
              </el-button>
              <el-button type="danger" size="small" @click="deleteRow($index)">
                {{ $t("删除") }}
              </el-button>
            </template>
            <template v-else>
              <el-button type="primary" size="small" @click="editRow(row)">
                {{ $t("编辑") }}
              </el-button>
              <el-button type="danger" size="small" @click="deleteRow($index)">
                {{ $t("删除") }}
              </el-button>
            </template>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts" name="ProgressUpdateTable">
import { ref, computed, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";

const { t } = useI18n();

interface DefectRateDetail {
  [key: string]: any;
  week: string;
  isEditing?: boolean;
}

interface Props {
  progressList?: DefectRateDetail[];
  isView?: boolean;
  fieldName?: string;
  fieldLabel?: string;
}

interface Emits {
  (e: "update:progress-list", value: DefectRateDetail[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  progressList: () => [],
  isView: false,
  fieldName: "inspectionDefectRate",
  fieldLabel: "不良率(DPPM)"
});

const emit = defineEmits<Emits>();

// 本地数据副本
const localProgressList = ref<DefectRateDetail[]>([]);

// 显示列表
const displayList = computed(() => {
  return localProgressList.value;
});

// 监听props变化
watch(
  () => props.progressList,
  newList => {
    if (Array.isArray(newList)) {
      localProgressList.value = newList.map(item => ({ ...item, isEditing: false }));
    } else {
      localProgressList.value = [];
    }
  },
  { deep: true, immediate: true }
);

// 当用户保存或删除时更新父组件
function emitUpdate() {
  const cleanData = localProgressList.value.map(item => {
    const result: any = { week: item.week };
    result[props.fieldName] = item[props.fieldName];
    return result;
  });
  emit("update:progress-list", cleanData);
}

// 添加新行
const addNewRow = () => {
  const newRow: DefectRateDetail = {
    week: "",
    isEditing: true
  };
  newRow[props.fieldName] = "";

  // 在列表顶部添加新行
  localProgressList.value.unshift(newRow);
};

// 编辑行
const editRow = (row: DefectRateDetail) => {
  row.isEditing = true;
};

// 保存行
const saveRow = (row: DefectRateDetail) => {
  if (!row[props.fieldName] || !row[props.fieldName].trim()) {
    ElMessage.warning(t("请输入") + props.fieldLabel);
    return;
  }

  if (!row.week.trim()) {
    ElMessage.warning(t("请输入周"));
    return;
  }

  row.isEditing = false;
  emitUpdate();
  ElMessage.success(t("保存成功"));
};

// 删除行
const deleteRow = (index: number) => {
  ElMessageBox.confirm(t("确定要删除这条记录吗？"), t("提示"), {
    confirmButtonText: t("确定"),
    cancelButtonText: t("取消"),
    type: "warning"
  })
    .then(() => {
      localProgressList.value.splice(index, 1);
      emitUpdate();
      ElMessage.success(t("删除成功"));
    })
    .catch(() => {
      // 用户取消删除
    });
};

defineExpose({
  addNewRow,
  editRow,
  saveRow,
  deleteRow
});
</script>

<style scoped lang="scss">
.progress-update-table {
  .table-header {
    margin-bottom: 10px;
    display: flex;
    justify-content: flex-start;
  }

  :deep(.el-table) {
    .el-table__cell {
      padding: 8px 0;
    }
  }

  :deep(.el-textarea__inner) {
    resize: none;
  }
}
</style>
