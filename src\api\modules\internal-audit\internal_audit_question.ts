import { ReqPage, ResPage } from "@/api/interface/index";
import { InternalAuditQuestion } from "@/typings/internal-audit/internal_audit_question";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/InternalAuditQuestion`;

// 列表
export const getInternalAuditQuestionList = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditQuestion.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getInternalAuditQuestionDetail = (id: number) => {
  return http.post<InternalAuditQuestion.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createInternalAuditQuestion = (data: InternalAuditQuestion.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editInternalAuditQuestion = (data: InternalAuditQuestion.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteInternalAuditQuestion = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportInternalAuditQuestion = (params?: InternalAuditQuestion.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importInternalAuditQuestion = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importInternalAuditQuestionTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<InternalAuditQuestion.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backAuditQuestion = (params: { id: number; rejectReason: string }) => {
  return http.post<InternalAuditQuestion.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const auditAuditQuestion = (id: number) => {
  return http.post<InternalAuditQuestion.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getAuditQuestionApproveList = (params?: ReqPage) => {
  return http.post<ResPage<InternalAuditQuestion.Item>>(`${baseUrl}/listToBeAudited`, params);
};
