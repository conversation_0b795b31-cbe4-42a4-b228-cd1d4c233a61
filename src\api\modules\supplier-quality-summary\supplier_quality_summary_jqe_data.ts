import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { JqeData } from "@/typings/supplier-quality-summary/jqe_data";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/jqe`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;

// 获取JQE数据列表
export const getJqeData = (params?: ReqPage) => {
  return http.post<ResPage<JqeData.Item>>(`${baseUrl}/list`, params);
};

// 获取JQE数据详情
export const getJqeDataDetail = (id: number) => {
  return http.get<Partial<JqeData.Item>>(`${baseUrl}/get/${id}`);
};

// 创建JQE数据
export const createJqeData = (data: JqeData.NewParams) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改JQE数据
export const editJqeData = (data: JqeData.EditParams) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 导出
export const exportJqeData = (ids: number[]) => {
  return http.post(`${baseUrl}/exportListData`, { ids });
};

// 删除
export const deleteJqeData = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
