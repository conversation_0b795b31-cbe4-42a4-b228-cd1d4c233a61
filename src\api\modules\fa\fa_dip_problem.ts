import { ReqPage, ResPage } from "@/api/interface/index";
import { FaDipProblem } from "@/typings/fa/fa_dip_problem";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/fa/statistics";

const baseUrl = `${API_PREFIX}/faDIPProblem`;

// 列表
export const getFaDipProblemList = (params?: ReqPage) => {
  return http.post<ResPage<FaDipProblem.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getFaDipProblemDetail = (id: number) => {
  return http.post<FaDipProblem.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createFaDipProblem = (data: FaDipProblem.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editFaDipProblem = (data: FaDipProblem.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteFaDipProblem = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportFaDipProblem = (params?: FaDipProblem.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importFaDipProblem = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importFaDipProblemTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 首件DIP问题表 首件DIP问题分析
export const statsFaDipProblem = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsFaDipProblem`, params);
};
