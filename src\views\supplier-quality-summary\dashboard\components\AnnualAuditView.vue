<template>
  <div class="annual-audit-view">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="年度">
          <el-date-picker
            v-model="searchForm.year"
            type="year"
            placeholder="请选择年度"
            format="YYYY"
            value-format="YYYY"
            style="width: 200px"
            @change="handleYearChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-container">
      <!-- 供应商年度审核得分分布 -->
      <div class="chart-card">
        <AuditScoreChart :chart-data="chartData.scoreDistribution" />
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="table-section">
      <div class="table-header">
        <h3>年度审核详情</h3>
        <el-button type="primary" @click="exportData">导出</el-button>
      </div>
      <el-table :data="tableData" border stripe max-height="400" style="width: 100%">
        <el-table-column prop="plant" label="工厂" width="120" />
        <el-table-column prop="supplierName" label="供应商" width="150" />
        <el-table-column prop="materialTeam" label="物料类别" width="120" />
        <el-table-column prop="planAuditDate" label="计划审核日期" width="120" />
        <el-table-column prop="actualAuditDate" label="实际审核日期" width="120" />
        <el-table-column prop="auditResult" label="审核结果" width="100">
          <template #default="{ row }">
            <el-tag :type="getResultTagType(row.auditResult)">
              {{ row.auditResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditScore" label="审核得分" width="100" />
        <el-table-column label="审核报告" width="120">
          <template #default="{ row }">
            <el-button
              v-if="row.reportFileUrl"
              type="primary"
              size="small"
              @click="downloadReport(row.reportFileUrl, row.reportFileName)"
            >
              下载附件
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="tsx" name="AnnualAuditView">
import { ref, reactive, onMounted, watch } from "vue";

import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { statsSupplierAnnualAuditData } from "@/api/modules/supplier-quality-summary/supplier_quality_summary_dashboard";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";
import AuditScoreChart from "./charts/AuditScoreChart.vue";

interface Props {
  filterForm: {
    plant: string;
    materialType: string;
  };
}

const props = defineProps<Props>();

// 搜索表单 - 默认当年
const searchForm = reactive({
  year: dayjs().format("YYYY")
});

// 图表数据
const chartData = ref<Dashboard.SupplierAnnualAuditData>({
  scoreDistribution: [],
  planList: []
});

// 表格数据
const tableData = ref<Dashboard.SupplierAnnualAuditData["planList"]>([]);

// 年度变更处理
const handleYearChange = () => {
  if (props.filterForm.materialType) {
    loadData();
  }
};

// 获取审核结果标签类型
const getResultTagType = (result: string) => {
  switch (result) {
    case "通过":
    case "合格":
      return "success";
    case "不通过":
    case "不合格":
      return "danger";
    case "待审核":
      return "warning";
    default:
      return "info";
  }
};

// 加载数据
const loadData = async () => {
  // 确保materialType不为空
  if (!props.filterForm.materialType) {
    console.warn("materialType为空，跳过加载年度审核数据");
    return;
  }

  try {
    const params: Dashboard.IQueryParamsSupplierAuditData = {
      year: searchForm.year,
      plant: props.filterForm.plant,
      materialType: props.filterForm.materialType
    };

    const response = await statsSupplierAnnualAuditData(params);

    if (response.data) {
      chartData.value = response.data;
      tableData.value = response.data.planList || [];
    }
  } catch (error) {
    console.error("加载年度审核数据失败:", error);
    //ElMessage.error("加载年度审核数据失败");
  }
};

// 下载报告
const downloadReport = (url: string, fileName: string) => {
  if (!url) {
    ElMessage.warning("报告文件不存在");
    return;
  }

  const link = document.createElement("a");
  link.href = url;
  link.download = fileName || "审核报告";
  link.target = "_blank";
  link.click();
};

// 导出数据
const exportData = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning("暂无数据可导出");
    return;
  }

  const headers = ["工厂", "供应商", "物料类别", "计划审核日期", "实际审核日期", "审核结果", "审核得分", "报告文件名"];
  const csvContent = [
    headers.join(","),
    ...tableData.value.map(row =>
      [
        row.plant,
        row.supplierName,
        row.materialTeam,
        row.planAuditDate,
        row.actualAuditDate,
        row.auditResult,
        row.auditScore,
        `"${row.reportFileName}"`
      ].join(",")
    )
  ].join("\n");

  const blob = new Blob(["\uFEFF" + csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = `年度审核数据_${dayjs().format("YYYY-MM-DD")}.csv`;
  link.click();
};

// 监听筛选条件变化
watch(
  () => [props.filterForm.plant, props.filterForm.materialType],
  () => {
    // 只有当materialType不为空时才加载数据
    if (props.filterForm.materialType) {
      loadData();
    }
  },
  { deep: true }
);

// 初始化
onMounted(() => {
  // 只有当materialType不为空时才加载数据
  if (props.filterForm.materialType) {
    loadData();
  }
});
</script>

<style scoped>
.annual-audit-view {
  padding: 20px 0;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #333;
}
</style>
