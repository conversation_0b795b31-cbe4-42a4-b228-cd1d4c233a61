import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { dccPlmList } from "@/typings/dcc/dcc_plm_document";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import axios from "axios";

const baseUrl2 = "/epros-api/findRuleByUser";

// 获取文档详情
export const getDccEprosList = (userCode: string, PNUM: number) => {
  return axios.get(`${baseUrl2}`, { params: { userCode, PNUM } });
};

// 下载文档
export const getDccEprosDownload = (itemNumber: string, attachmentName: string) => {
  return axios.post(`${baseUrl2}/downloadItemFile`, { itemNumber, attachmentName });
};
