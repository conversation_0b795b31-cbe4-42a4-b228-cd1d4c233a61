<template>
  <el-dialog v-model="visible" width="600px" :destroy-on-close="true" :title="`${t(title)}`">
    <el-form
      ref="formRef"
      label-position="top"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      :show-message="isZh"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('工厂')" prop="plant">
            <el-input
              v-model="form.plant"
              :placeholder="$t('请选择工厂')"
              readonly
              @click="openSelectionDialog"
              :disabled="isView"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('物料类别')" prop="materialType">
            <el-input
              v-model="form.materialType"
              :placeholder="$t('请选择物料类别')"
              readonly
              @click="openSelectionDialog"
              :disabled="isView"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplierName">
            <el-input
              v-model="form.supplierName"
              :placeholder="$t('请选择供应商')"
              readonly
              @click="openSelectionDialog"
              :disabled="isView"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('季度')" prop="quarter">
            <el-quarter-picker
              v-if="!isView"
              v-model="form.quarter"
              :placeholder="$t('请选择季度')"
              style="width: 100%"
              @change="handleQuarterChange"
            />
            <span v-else>{{ form.quarter || "-" }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('制程重大品质问题发生次数(不良率≥2%)目标值')" prop="majorQualityIssueTarget">
            <el-input-number
              v-model="form.majorQualityIssueTarget"
              :min="0"
              :precision="0"
              :disabled="isView"
              style="width: 100%"
              :placeholder="$t('请输入')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('客户端投诉次数目标值')" prop="clientComplaintTarget">
            <el-input-number
              v-model="form.clientComplaintTarget"
              :min="0"
              :precision="0"
              :disabled="isView"
              style="width: 100%"
              :placeholder="$t('请输入')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('LAR目标值')" prop="larTarge">
            <div style="display: flex; align-items: center; width: 100%">
              <el-input-number
                v-model="form.larTarge"
                :min="0"
                :max="100"
                :precision="2"
                :disabled="isView"
                style="flex: 1"
                :placeholder="$t('请输入')"
              />
              <span style="margin-left: 8px">%</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 供应商选择器 -->
    <SupplierSelector ref="supplierSelectorRef" @select="handleSupplierSelect" />

    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="TargetModal">
import { ref, reactive, toRefs } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { TargetData } from "@/typings/supplier-quality-summary/target";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import SupplierSelector from "../../../components/SupplierSelector/index.vue";
import ElQuarterPicker from "@/views/components/ElQuarterPicker.vue";

const { isZh } = useLanguageCode();
const { t } = useI18n();

interface TargetModalProps {
  title: string;
  isView: boolean;
  form: Partial<TargetData.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const visible = ref(false);
const formRef = ref<FormInstance>();
const supplierSelectorRef = ref<InstanceType<typeof SupplierSelector>>();

const state = reactive<TargetModalProps>({
  title: "",
  isView: false,
  form: {},
  api: undefined,
  getTableList: undefined
});

const { form, title, isView } = toRefs(state);

// 表单验证规则
const rules = reactive({
  plant: [{ required: true, message: t("请选择工厂"), trigger: "change" }],
  materialType: [{ required: true, message: t("请填写物料类别"), trigger: "blur" }],
  supplierName: [{ required: true, message: t("请填写供应商名称"), trigger: "blur" }],
  quarter: [{ required: true, message: t("请选择季度"), trigger: "change" }],
  majorQualityIssueTarget: [{ required: true, message: t("请输入制程重大品质问题发生次数目标值"), trigger: "blur" }],
  clientComplaintTarget: [{ required: true, message: t("请输入客户端投诉次数目标值"), trigger: "blur" }],
  larTarge: [{ required: true, message: t("请输入LAR目标值"), trigger: "blur" }]
});

// 设置弹窗显示状态
const setVisible = (val: boolean) => {
  visible.value = val;
};

// 接收父组件参数
const acceptParams = (params: TargetModalProps) => {
  Object.assign(state, params);
  setVisible(true);
};

// 打开选择弹窗
const openSelectionDialog = () => {
  supplierSelectorRef.value?.open();
};

// 处理供应商选择
const handleSupplierSelect = (data: SupplierInformation.Item) => {
  form.value.plant = data.plant;
  form.value.materialType = data.materialType;
  form.value.supplierName = data.supplierName;
};

// 处理季度变化
const handleQuarterChange = (value: any) => {
  // 季度选择器返回的值可能需要格式化
  console.log("Quarter changed:", value);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!(form.value);
      ElMessage.success({ message: t(`保存成功`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
