<template>
  <div class="no-min-height-table">
    {{ isView }}
    <el-table :data="localList" style="width: 100%">
      <el-table-column prop="staffName" label="姓名" />
      <el-table-column prop="staffNo" label="工号" />
      <el-table-column prop="staffEmail" label="邮箱" />
      <el-table-column prop="sort" label="顺序" />
      <el-table-column v-if="isView" prop="isAudited" label="审核状态">
        <template #default="{ row }">
          <el-tag :type="row.isAudited === '已审核' ? 'success' : 'primary'" disable-transitions>
            {{ row.isAudited === "已审核" ? "已审核" : "未审核" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="!isView" label="操作" width="120">
        <template #default="scope">
          <el-button size="small" @click="openEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button v-if="!isView" type="primary" @click="openAdd" style="margin-top: 10px">{{ $t("新增") }}</el-button>

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="400px" @close="resetForm">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-row>
          <el-col :span="20">
            <el-form-item :label="$t('姓名')" prop="staffName" style="margin-bottom: 18px">
              <RemoteSearch :job-num="form.staffNo" v-model="form.staffName" @extra="staffExtra"></RemoteSearch>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item :label="$t('顺序')" prop="sort" style="margin-bottom: 18px">
              <el-input v-model="form.sort" :placeholder="$t('请填写')" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import {
  createSupplierAuditStaff,
  editSupplierAuditStaff,
  deleteSupplierAuditStaff
} from "@/api/modules/supplier-annual-audit/supplier_audit_staff";
import { Staff } from "@/typings/staff";
import RemoteSearch from "@/views/components/RemoteSearch.vue";
import { updateUserSearchExtra } from "@/utils";

const props = defineProps<{
  planId: number | string;
  isView?: boolean;
  list: any[];
}>();
const emit = defineEmits(["updateData"]);

const localList = ref<any[]>([...(props.list ?? [])]);
watch(
  () => props.list,
  val => {
    localList.value = [...(val ?? [])];
  }
);

const dialogVisible = ref(false);
const dialogTitle = ref("新增审核人员");
const form = ref<any>({
  staffName: "",
  staffNo: "",
  sort: ""
});
const editingId = ref<number | null>(null);
const formRef = ref<FormInstance>();
const rules = {
  staffName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  sort: [{ required: true, message: "请输入顺序", trigger: "blur" }]
};

const staffExtra = (staff: Staff.Extra) => {
  updateUserSearchExtra(form, staff, "staff", "审核人没有绑定邮箱,请更换");
};

const openAdd = () => {
  dialogTitle.value = "新增审核人员";
  editingId.value = null;
  form.value = { staffName: "", staffNo: "", sort: "" };
  dialogVisible.value = true;
};

const openEdit = (row: any) => {
  dialogTitle.value = "编辑审核人员";
  editingId.value = row.id;
  form.value = { staffName: row.staffName, staffNo: row.staffNo, sort: row.sort };
  dialogVisible.value = true;
};

const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (!valid) return;
    const submitData = { ...form.value, planId: props.planId };
    if (editingId.value) {
      await editSupplierAuditStaff({ ...submitData, id: editingId.value });
      ElMessage.success("编辑成功");
      // 更新本地数据
      const idx = localList.value.findIndex(item => item.id === editingId.value);
      if (idx !== -1) localList.value[idx] = { ...localList.value[idx], ...form.value };
    } else {
      const res = await createSupplierAuditStaff(submitData);
      ElMessage.success("新增成功");
      localList.value.push(res.data || submitData);
    }
    dialogVisible.value = false;
    emit("updateData", localList.value);
  });
};

const handleDelete = (row: any) => {
  ElMessageBox.confirm("确定删除该审核人员？", "提示", { type: "warning" }).then(async () => {
    await deleteSupplierAuditStaff([row.id]);
    ElMessage.success("删除成功");
    localList.value = localList.value.filter(item => item.id !== row.id);
    emit("updateData", localList.value);
  });
};

const resetForm = () => {
  formRef.value?.resetFields();
};
</script>
