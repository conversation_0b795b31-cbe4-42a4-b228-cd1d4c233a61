import { ReqPage, ResPage } from "@/api/interface/index";
import { ObaDipWorkCenter } from "@/typings/oba-dip/oba_dip_work_center";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/obaDIPWorkCenter`;

// 列表
export const getObaDipWorkCenterList = (params?: ReqPage) => {
  return http.post<ResPage<ObaDipWorkCenter.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getObaDipWorkCenterDetail = (id: number) => {
  return http.post<ObaDipWorkCenter.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createObaDipWorkCenter = (data: ObaDipWorkCenter.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editObaDipWorkCenter = (data: ObaDipWorkCenter.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteObaDipWorkCenter = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportObaDipWorkCenter = (params?: ObaDipWorkCenter.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importObaDipWorkCenter = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importObaDipWorkCenterTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
