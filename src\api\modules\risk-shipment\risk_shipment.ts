import { ReqPage, ResPage } from "@/api/interface/index";
import { RiskShipment } from "@/typings/risk-shipment/risk_shipment";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/risk-shipment/statistics";

const baseUrl = `${API_PREFIX}/riskShipment`;

// 列表
export const getRiskShipmentList = (params?: ReqPage) => {
  return http.post<ResPage<RiskShipment.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getRiskShipmentDetail = (id: number) => {
  return http.post<RiskShipment.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createRiskShipment = (data: RiskShipment.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editRiskShipment = (data: RiskShipment.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteRiskShipment = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportRiskShipment = (params?: RiskShipment.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importRiskShipment = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importRiskShipmentTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

export const statsOrderQtyOverview = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsOrderQtyOverview`, params);
};

export const statsNgQtyOverview = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsNgQtyOverview`, params);
};
