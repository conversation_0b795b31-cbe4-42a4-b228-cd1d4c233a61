import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl1 = `${API_PREFIX}/dashboard`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;

// 获取供应商汇总数据
export const statsSummaryData = (params?: Dashboard.IQueryParamsSummary) => {
  return http.post<Dashboard.SummaryData>(`${baseUrl1}/summaryData`, params);
};

// 获取供应商月度数据
export const statsMonthlyData = (params?: Dashboard.IQueryParamsMonthlyData) => {
  return http.post<Dashboard.MonthlyData>(`${baseUrl1}/statsSupplierTrend`, params);
};

// 获取供应商季度数据
export const statsQuarterlyData = (params?: Dashboard.IQueryParamsQuarterlyData) => {
  return http.post<Dashboard.QuarterlyData>(`${baseUrl1}/statsQualitySummaryByQuarter`, params);
};

// 获取供应商QCC专案数据
export const statsQccData = (params?: Dashboard.IQueryParamsQccData) => {
  return http.post<Dashboard.QccData>(`${baseUrl1}/statsByQccProjectSummary`, params);
};

// 获取供应商JQE数据
export const statsJqeData = (params?: Dashboard.IQueryParamsJqeData) => {
  return http.post<Dashboard.JqeData>(`${baseUrl1}/statsByOqcAndReinspectionTrend`, params);
};

// 获取供应商年度审核数据
export const statsSupplierAnnualAuditData = (params?: Dashboard.IQueryParamsSupplierAuditData) => {
  return http.post<Dashboard.SupplierAnnualAuditData>(`${baseUrl1}/statsPlanAll`, params);
};

// 获取供应商专项审核数据
export const statsSupplierSpecialAuditData = (params?: Dashboard.IQueryParamsSupplierAuditData) => {
  return http.post<Dashboard.SupplierAuditTableData>(`${baseUrl1}/listByPage`, params);
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
