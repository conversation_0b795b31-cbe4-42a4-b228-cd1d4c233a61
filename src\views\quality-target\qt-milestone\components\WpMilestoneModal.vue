<template>
  <el-dialog v-model="visible" width="80%" :destroy-on-close="true" :title="`${t(title)}`">
    <el-alert
      v-if="form.rejectReason && [`已退回`].includes(form.reportStatus ?? '')"
      class="mb20"
      :title="t('退回理由')"
      type="error"
      :description="form.rejectReason"
      show-icon
      :closable="false"
    />
    <el-form ref="formRef" label-width="auto" label-suffix=" :" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <div class="block-tip">{{ $t("任务信息") }}</div>
      <el-descriptions border column="3" class="mt-[10px] mb-[20px]">
        <el-descriptions-item :label="$t('目标编号')">{{ form.targetNo }}</el-descriptions-item>
        <el-descriptions-item :label="$t('年度')">{{ form.year }}</el-descriptions-item>
        <el-descriptions-item :label="$t('分部')">{{ form.dept }}</el-descriptions-item>
        <el-descriptions-item :label="$t('责任人')">{{ form.responsibleStaffName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('质量目标')">{{ form.targetName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('汇报频率')">{{ form.reportFreq }}</el-descriptions-item>
        <el-descriptions-item :label="$t('目标值')">
          {{ form.target }}{{ form.isPercent === "1" ? "%" : "" }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="block-tip">{{ t("填报内容") }}</div>
      <el-descriptions border column="3" class="mt-[10px] mb-[20px]">
        <el-descriptions-item :label="$t('里程碑')">{{ form.milestone }}</el-descriptions-item>
        <el-descriptions-item v-if="form.denominatorName" :label="$t(form.denominatorName)">
          <el-input v-model="form.denominatorValue" @blur="autoCalcValue" :placeholder="$t('请填写')" type="number" clearable />
        </el-descriptions-item>
        <el-descriptions-item v-if="form.numeratorName" :label="$t(form.numeratorName)">
          <el-input v-model="form.numeratorValue" @blur="autoCalcValue" :placeholder="$t('请填写')" type="number" clearable />
        </el-descriptions-item>
        <el-descriptions-item :label="$t(form.targetName)">
          <template v-if="form.isCalc === '是'">{{ `${form.reportScore}${form.isPercent === "是" ? "%" : ""}` }}</template>
          <template v-else>
            <el-input v-model="form.reportScore" :placeholder="$t('请填写')" type="number" clearable>
              <template v-if="form.isPercent === '是'" #append>%</template>
            </el-input>
          </template>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('汇报结果')">
          <el-select v-model="form.reportResult" :placeholder="$t('请选择')" clearable filterable>
            <el-option v-for="({ label }, index) of ReportResultList" :key="index" :label="$t(label)" :value="label" />
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('汇报时间')">{{ form.reportAt }}</el-descriptions-item>
      </el-descriptions>

      <div class="block-tip" v-if="form.reportResult === '未达标'">{{ $t("改善信息") }}</div>

      <el-descriptions v-if="form.reportResult === '未达标'" :column="2" border>
        <el-descriptions-item :label="$t('改善类型')">
          <el-select v-model="form.improveType" :placeholder="$t('请选择')" clearable filterable>
            <el-option v-for="({ label }, index) of wp_improve_type" :key="index" :label="$t(label)" :value="label" />
          </el-select>
        </el-descriptions-item>

        <el-descriptions-item v-if="form.improveType !== '附件报告'" :label="$t('改善链接')">
          <el-input v-model="form.improveLink" :placeholder="$t('请填写')" clearable />
        </el-descriptions-item>
      </el-descriptions>

      <!-- 附件上传单独占据一行 -->
      <el-descriptions v-if="form.reportResult === '未达标' && form.improveType === '附件报告'" :column="1" border>
        <el-descriptions-item :label="$t('上传改善附件')">
          <UploadFiles
            :file-url="improveFile"
            :multiple="false"
            :is-show-tip="true"
            :immediate="true"
            :limit="1"
            :file-type="['xls', 'xlsx']"
            :btn-name="$t('上传附件')"
            @upload-success="improveFileSuccess"
            @file-list-empty="improveFileEmpty"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("关闭") }}</el-button>
      <template
        v-if="
          [`已退回`, `待填报`, `临近逾期`, `进行中`].includes(form.reportStatus ?? '') &&
          userInfo.jobNum === form.responsibleStaffNo
        "
      >
        <el-button type="primary" @click="handleSubmit(false)">{{ $t("保存") }}</el-button>
        <el-button v-show="!isView" type="primary" @click="handleSubmit(true)">{{ $t("提交") }}</el-button>
      </template>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="WpMilestoneModal">
import { QtMilestone } from "@/typings/quality-target/qt_milestone";
import { ElMessage, FormInstance, ElMessageBox } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { useI18n } from "vue-i18n";
import { submitToAudit, autoCalc } from "@/api/modules/quality-target/qt_milestone";
import UploadFiles from "@/components/Upload/UploadFiles.vue";
import { ReportResultList } from "@/enums/statusQualityTarget";
import useUserStore from "@/stores/modules/user";
import { useDict } from "@/hooks/useDict";
import { isEmpty } from "@/utils/is";
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const { t } = useI18n();

interface IState {
  title: string;
  isView: boolean;
  form: QtMilestone.Item;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  clearSelection?: () => void;
}

const { wp_improve_type } = useDict("wp_improve_type");

const rules = {
  reportResult: [{ required: true, message: "请填写汇报结果", trigger: "blur" }],
  reportFileName: [{ required: true, message: "请填写上传附件名称", trigger: "blur" }],
  improveType: [{ required: true, message: "请填写改善类型", trigger: "blur" }],
  reportScore: [{ required: true, message: "请填写汇报数据", trigger: "blur" }],
  reportContent: [{ required: true, message: "请填写汇报内容", trigger: "blur" }],
  improveLink: [{ required: true, message: "请填写改善链接", trigger: "blur" }]
};

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {} as QtMilestone.Item
});
const { form, title, isView } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

const improveFile = computed(() => {
  if (!isEmpty(form.value.improveFileUrl)) {
    return [{ name: form.value.improveFileName ?? "", url: form.value.improveFileUrl ?? "" }];
  } else {
    return [];
  }
});
const improveFileSuccess = (res: { name: string; url: string }) => {
  form.value.improveFileName = res.name;
  form.value.improveFileUrl = res.url;
};
const improveFileEmpty = () => {
  form.value.improveFileName = "";
  form.value.improveFileUrl = "";
};

const autoCalcValue = async () => {
  if (form.value.isCalc === "是") {
    if (!isEmpty(form.value.denominatorName) && isEmpty(form.value.denominatorValue)) {
      return false;
    }
    if (!isEmpty(form.value.numeratorName) && isEmpty(form.value.numeratorValue)) {
      return false;
    }
    try {
      const { success, data } = await autoCalc(form.value as QtMilestone.Item);
      if (success) {
        const score = parseFloat(data?.toString()) || 0;
        const target = parseFloat(form.value.target) || 0;
        form.value.reportScore = score;
        let isMet = false;
        switch (form.value.operator) {
          case ">":
            isMet = score > target;
            break;
          case ">=":
          case "≥":
            isMet = score >= target;
            break;
          case "<":
            isMet = score < target;
            break;
          case "<=":
          case "≤":
            isMet = score <= target;
            break;
          case "=":
          case "==":
            isMet = score === target;
            break;
          case "!=":
            isMet = score !== target;
            break;
          default:
            isMet = score >= target;
        }
        form.value.reportResult = isMet ? "已达标" : "未达标";
      }
    } catch (err) {
      console.log(err);
    }
  }
};
const handleSubmit = (isApproval: boolean = false) => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const { success } = await state.api!(form.value);
      if (!success) {
        ElMessage.error({ message: t(`保存失败`) });
        return;
      }
      if (isApproval) {
        ElMessageBox.confirm(t("确定提交审批"), t("提示"), {
          confirmButtonText: t("确认"),
          cancelButtonText: t("取消"),
          type: ""
        }).then(async () => {
          const { success } = await submitToAudit(form.value as QtMilestone.Item);
          if (!success) {
            ElMessage.error({ message: t(`提交失败`) });
            return;
          }
          ElMessage.success({ message: t(`提交成功`) });
          state.getTableList!();
          state.clearSelection!();
          setVisible(false);
        });
      } else {
        ElMessage.success({ message: t(`保存成功`) });
        state.getTableList!();
        setVisible(false);
      }
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams,
  setVisible
});
</script>
