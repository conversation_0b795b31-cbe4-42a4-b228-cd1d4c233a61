import { ReqPage, ResPage } from "@/api/interface/index";
import { SmtCategory } from "@/typings/ipqc-smt/ipqc_smt_category";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ipqcSMTCategory`;

// 列表
export const getSmtCategoryList = (params?: ReqPage) => {
  return http.post<ResPage<SmtCategory.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSmtCategoryDetail = (id: number) => {
  return http.post<SmtCategory.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSmtCategory = (data: SmtCategory.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSmtCategory = (data: SmtCategory.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSmtCategory = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSmtCategory = (params?: SmtCategory.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSmtCategory = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSmtCategoryTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
