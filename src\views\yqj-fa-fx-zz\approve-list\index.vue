<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'yqj-fa-fx-zz-apply:audit'" type="primary" @click="openModal('审批')">{{ $t("审批") }}</el-button>
      </template>
    </ProTable>
    <Modal ref="modalRef" />
  </div>
</template>

<script setup lang="tsx" name="yqj-fa-fx-zz-approve-list">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";

import Modal from "./components/Modal.vue";

import { <PERSON><PERSON><PERSON>on, ElMessage } from "element-plus";
import { useDict } from "@/hooks/useDict";

import { isEmpty } from "@/utils/is";
import DateRange from "../../components/DateRange.vue";
import { ref, reactive, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";

import { getList, create, update, remove } from "@/api/modules/yqj-fa-fx-zz/approve";

import { computed } from "vue";
import { isArray, isNil, last } from "lodash-es";
import useUserStore from "@/stores/modules/user";
import { Dept } from "@/typings/dept";
import { getDeptAll } from "@/api/modules/dept";

// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();

const deptListAll = ref<Dept.Item[]>([]);

const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const modalRef = ref<InstanceType<typeof Modal> | null>(null);

const userStore = useUserStore();

const userInfo = computed(() => userStore.userInfo);

const initParam = reactive({});

const resetCounter = ref(0);
const filterDate = ref([]);

const {
  yqj_fa_fx_zz_material_type,
  yqj_fa_fx_zz_root_reason_type,
  sys_yes_no,
  yqj_fa_fx_zz_exception_type,
  yqj_fa_fx_zz_apply_status,
  yqj_fa_fx_zz_approval_status,
  yqj_fa_fx_zz_apply_node
} = useDict(
  "quality_person_training_institutions",
  "sys_user_sex",
  "yqj_fa_fx_zz_material_type",
  "yqj_fa_fx_zz_root_reason_type",
  "sys_yes_no",
  "yqj_fa_fx_zz_exception_type",
  "yqj_fa_fx_zz_apply_status",
  "yqj_fa_fx_zz_approval_status",
  "yqj_fa_fx_zz_apply_node"
);

// 表格配置项
const columns = computed<ColumnProps<YqjFaFxZzApply.Item>[]>(() => [
  { type: "selection", fixed: "left", width: 40 },
  {
    prop: "number",
    label: "申请单号",
    width: 150,
    fixed: "left",
    search: {
      el: "input",
      order: 1
    },
    render: ({ row }) => {
      return (
        <ElButton link type="primary" onClick={() => openModal("审批", row)}>
          {row.number}
        </ElButton>
      );
    }
  },
  {
    prop: "approveStatus",
    label: "状态",
    fixed: "left",
    width: 120,
    render: ({ row }) => {
      const { approveRecords } = row;
      const { status } = last(approveRecords) ?? ({} as YqjFaFxZzApply.ApproveRecord);
      const label = yqj_fa_fx_zz_approval_status.value.find(item => item.value === status)?.label ?? "--";
      return label;
    },
    enum: yqj_fa_fx_zz_approval_status.value,
    search: { order: 4, el: "select", props: { filterable: true } }
  },
  {
    prop: "applicant",
    label: "申请人",
    width: 120,
    search: {
      order: 10,
      el: "input"
    }
  },

  { prop: "applicantWorkNo", label: "工号", width: 180 },
  { prop: "applicantDept", label: "部门", width: 120, enum: deptListAll.value, fieldNames: { label: "name", value: "id" } },
  { prop: "sqe", label: "sqe", width: 120 },
  { prop: "applicantLeader", label: "申请人领导", width: 120 },
  { prop: "sqeLeader", label: "sqe领导", width: 120 },
  { prop: "sampleSendTime", label: "送样日期", width: 120 },
  {
    prop: "materialType",
    label: "物料类别",
    width: 120,
    enum: yqj_fa_fx_zz_material_type.value,

    search: { order: 2, el: "select", props: { filterable: true } }
  },
  {
    prop: "materialNumber",
    label: "物料编号",
    width: 120,

    search: { order: 5, el: "input", props: { filterable: true } }
  },
  {
    prop: "exceptionType",
    label: "异常类别",
    width: 120,
    enum: yqj_fa_fx_zz_exception_type.value,
    search: { order: 3, el: "select", props: { filterable: true } }
  },
  {
    prop: "activeNode",
    label: "当前节点",
    width: 120,
    enum: yqj_fa_fx_zz_apply_node.value,
    search: { order: 5, el: "select", props: { filterable: true } }
  },
  {
    prop: "createAt",
    label: "申请日期",
    width: 150,

    search: {
      order: 10,
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  {
    prop: "viewRange",
    label: "查看范围",
    width: 150,
    isShow: false,
    enum: [
      {
        label: "只看我的",
        value: "my"
      },
      {
        label: "查看所有",
        value: "all"
      }
    ],
    search: {
      order: 11,
      el: "select"
    }
  }
]);

const getDeptListAll = async () => {
  const { data } = await getDeptAll();
  deptListAll.value = data;
};

const getTableList = async (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.applyStartDate = filterDate.value[0];
    condition.applyEndDate = filterDate.value[1];
  }
  if (!isNil(condition.viewRange)) {
    if (condition.viewRange === "my") {
      condition.isViewMy = true;
    } else {
      condition.isViewMy = false;
    }
    delete condition.viewRange;
  } else {
    condition.isViewMy = false;
  }

  return getList({
    ...condition,
    pageNum,
    pageSize
  });
};

const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const batchDelete = async (list?: YqjFaFxZzApply.Item | YqjFaFxZzApply.Item[]) => {
  const _list = isArray(list) ? list : [list];
  const noAuth = _list.filter(item => item?.applicantWorkNo !== userInfo.value.jobNum).map(v => v?.number);
  if (!isEmpty(noAuth)) {
    return ElMessage.error(t(`无法删除编号`) + noAuth.join());
  }
  const ids = _list.map(item => item?.id);
  // const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(remove, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openModal = async (title: string, row: Partial<YqjFaFxZzApply.Item> = {}) => {
  check(row?.id);

  const applyForm = { ...currentRow.value } as YqjFaFxZzApply.Item;
  const sqeForm = applyForm.sqeForm ?? ({} as YqjFaFxZzApply.SqeItem);
  const params = {
    title,
    isView: true,
    disabled: true,
    yqj_fa_fx_zz_material_type: yqj_fa_fx_zz_material_type.value,
    yqj_fa_fx_zz_root_reason_type: yqj_fa_fx_zz_root_reason_type.value,
    sys_yes_no: sys_yes_no.value,
    yqj_fa_fx_zz_exception_type: yqj_fa_fx_zz_exception_type.value,
    applyForm,
    sqeForm,
    api: title === "新增" ? create : title === "编辑" ? update : undefined,
    getTableList: proTable.value?.getTableList
  };
  modalRef.value?.acceptParams(params);
};

onMounted(getDeptListAll);

onMounted(() => {
  console.log("onMounted approve-list");
});
</script>
