import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerSatOrder } from "@/typings/customer-satisfaction/customer_sat_order";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/customer-satisfaction/statistics";

const baseUrl = `${API_PREFIX}/cssOrder`;

// 列表
export const getCustomerSatOrderList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerSatOrder.Item>>(`${baseUrl}/list`, params);
};
//待反馈
export const getListToBeFeedback = (params?: ReqPage) => {
  return http.post<ResPage<CustomerSatOrder.Item>>(`${baseUrl}/listToBeFeedback`, params);
};

// 详情
export const getCustomerSatOrderDetail = (id: number) => {
  return http.post<CustomerSatOrder.Item>(`${baseUrl}/get/${id}`);
};

export const getCustomerSatOrderDetailByOrderNo = (uuid: string) => {
  return http.post<CustomerSatOrder.Item>(`${baseUrl}/getOne/${uuid}`, {}, { loading: false });
};

// 新增
export const createCustomerSatOrder = (data: CustomerSatOrder.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerSatOrder = (data: CustomerSatOrder.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

export const submitOneRecord = (data: CustomerSatOrder.Record) => {
  return http.post(`${baseUrl}/submitOneRecord`, data, { loading: false });
};
// 删除
export const deleteCustomerSatOrder = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerSatOrder = (params?: CustomerSatOrder.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerSatOrder = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerSatOrderTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 撤回
export const revokeByCQE = (id: number) => {
  return http.post<CustomerSatOrder.Item>(`${baseUrl}/revokeByCQE/${id}`);
};

// 发布
export const releaseByCQE = (id: number) => {
  return http.post<CustomerSatOrder.Item>(`${baseUrl}/releaseByCQE/${id}`);
};

// 关闭
export const cancelByCQE = (id: number) => {
  return http.post<CustomerSatOrder.Item>(`${baseUrl}/cancelByCQE/${id}`);
};

//完成调查表
export const finishSurvey = (data: CustomerSatOrder.Item) => {
  return http.post<CustomerSatOrder.Item>(`${baseUrl}/finishSurvey`, data);
};
//和而泰反馈
export const feedbackByHet = (data: CustomerSatOrder.Item) => {
  return http.post<CustomerSatOrder.Item>(`${baseUrl}/feedbackByHet`, data);
};

// 客户满意度一览表
export const getStatsOverviewByMonth = (params?: CustomerSatOrder.IQueryParams) => {
  return http.post(`${baseUrl}/statsOverviewByMonth`, params);
};

// 导出
export const exportOverviewData = (params?: CustomerSatOrder.IQueryParams) => {
  return http.post(`${baseUrl}/exportOverviewData`, params);
};

// 和而泰工厂评分统计
export const statsLineByHetPlant = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsLineByHetPlant`, params);
};

// 和而泰BU评分统计
export const statsLineByHetBu = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsLineByHetBu`, params);
};
