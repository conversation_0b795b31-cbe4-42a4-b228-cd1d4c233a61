import { ReqPage, ResPage } from "@/api/interface/index";
import { QccTopic } from "@/typings/qcc/qcc_topic";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/topic`;

// 列表
export const getQccTopicList = (params?: ReqPage) => {
  return http.post<ResPage<QccTopic.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQccTopicDetail = (id: number) => {
  return http.post<QccTopic.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQccTopic = (data: QccTopic.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQccTopic = (data: QccTopic.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

//评分
export const scoreQccTopic = (data: QccTopic.Item) => {
  return http.post(`${baseUrl}/score`, data);
};

// 删除
export const deleteQccTopic = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQccTopic = (params?: QccTopic.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQccTopic = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

// 根据期数查询课题
export const listToPeriodNum = () => {
  return http.post<string[]>(`${baseUrl}/listToPeriodNum`);
};
//下载模板
export const importQccTopicTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 提交
export const submitQccTopic = (id: number) => {
  return http.post(`${baseUrl}/submit/${id}`);
};

// 流程信息
export const getFlow = (params?: ReqPage) => {
  return http.post<ResPage<QccTopic.Detail>>(`${baseUrl}/processInf`, params);
};

// 新增月度收益
export const createQccTopicMonthlyBenefits = (data: { topicId: number }) => {
  return http.post<QccTopic.MonthlyBenefits>(`${baseUrl}/monthlyBenefits/create`, data);
};

// 修改月度收益
export const editQccTopicMonthlyBenefits = (data: QccTopic.MonthlyBenefits) => {
  return http.post<QccTopic.MonthlyBenefits>(`${baseUrl}/monthlyBenefits/update`, data);
};

// 删除
export const deleteQccTopicMonthlyBenefits = (id: number) => {
  return http.post(`${baseUrl}/monthlyBenefits/delete/${id}`);
};

// 月度收益列表
export const getQccTopicMonthlyBenefitsList = (params?: ReqPage) => {
  return http.post<ResPage<QccTopic.MonthlyBenefits>>(`${baseUrl}/listMb`, params);
};

// 退回
export const backQccTopic = (params: { id: number; rejectReason: string }) => {
  return http.post<QccTopic.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const auditQccTopic = (id: number) => {
  return http.post<QccTopic.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getQccTopicApproveList = (params?: ReqPage) => {
  return http.post<ResPage<QccTopic.Item>>(`${baseUrl}/listToBeAudited`, params);
};
//审批详情
export const getQccTopicApproveDetail = (id: number) => {
  return http.post<QccTopic.Item>(`${baseUrl}/listToBeAudited/get/${id}`);
};
// 我的任务列表
export const getQccTopicMyList = (params?: ReqPage) => {
  return http.post<ResPage<QccTopic.Item>>(`${baseUrl}/listToMyQuest`, params);
};

// 我的任务详情
export const getQccTopicMyListDetail = (id: number) => {
  return http.post<QccTopic.Item>(`${baseUrl}/listToMyQuest/get/${id}`);
};

export const submitDetailToAudit = (id: number) => {
  return http.post<QccTopic.Detail>(`${baseUrl}/submitDetailToAudit/${id}`);
};
// 我的任务详情
export const getQccTopicDetailList = (params?: ReqPage) => {
  return http.post<ResPage<QccTopic.Detail>>(`${baseUrl}/listDetail`, params);
};
// 修改
export const modifyDetail = (data: QccTopic.Detail) => {
  return http.post<QccTopic.Detail>(`${baseUrl}/modifyDetail`, data);
};

// 首页看板
export const getQccTopicBoard = () => {
  return http.post<QccTopic.Board>(`${baseUrl}/board`);
};

//根据部门统计课题数
export const statsByDeptTopic = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsByDeptTopic[]>(`${baseUrl}/statsByDeptTopic`, params);
};
//根据期数统计课题数
export const statsByPeriodTopic = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsByPeriodTopic[]>(`${baseUrl}/statsByPeriodTopic`, params);
};
//按部门统计节约成本
export const statsCostSavingByDept = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsCostSavingByDept[]>(`${baseUrl}/statsCostSavingByDept`, params);
};
//按期数统计节约成本
export const statsCostSavingByPeriod = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsCostSavingByPeriod[]>(`${baseUrl}/statsCostSavingByPeriod`, params);
};
//按部门统计状态
export const statsByDeptStatus = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsByDeptStatus[]>(`${baseUrl}/statsByDeptStatus`, params);
};
//按期数统计状态
export const statsByPeriodStatus = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsByPeriodStatus[]>(`${baseUrl}/statsByPeriodStatus`, params);
};
//按部门统计参与人员
export const statsByDeptMember = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsByDeptMember[]>(`${baseUrl}/statsByDeptMember`, params);
};

//按期数统计参与人员
export const statsByPeriodMember = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsByPeriodMember[]>(`${baseUrl}/statsByPeriodMember`, params);
};
//按期数统计节约金额
export const statsByPeriodSavingTotal = (params?: QccTopic.StatsQueryParams) => {
  return http.post<QccTopic.statsByPeriodSavingTotal>(`${baseUrl}/statsByPeriodSavingTotal`, params);
};
