export namespace QccData {
  export interface Item {
    id: number;
    caseNo: string;
    plant: string;
    supplierName: string;
    materialType: string;
    projectContent: string;
    revenueSummary: string;
    status: string;
    planCompleteDate: string;
    improvementReportName: string;
    improvementReportUrl: string;
    progressUpdate: string;
    progressUpdateList: progressUpdateDetail[];
    deleted: number;
    creatEmail: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface IQueryParams {
    startDate: string;
    endDate: string;
    plant: string;
    supplierName: string;
    materialType: string;
  }
  export interface NewParams {
    plant: string;
    supplierName: string;
    materialType: string;
    projectContent: string;
    revenueSummary: string;
    status: string;
    planCompleteDate: string;
    improvementReportName: string;
    improvementReportUrl: string;
    progressUpdateList: progressUpdateDetail[];
  }
  export interface EditParams {
    id: number;
    plant: string;
    supplierName: string;
    materialType: string;
    projectContent: string;
    revenueSummary: string;
    status: string;
    planCompleteDate: string;
    improvementReportName: string;
    improvementReportUrl: string;
    progressUpdateList: progressUpdateDetail[];
  }
  interface progressUpdateDetail {
    progressUpdateText: string;
    week: string;
  }
}
