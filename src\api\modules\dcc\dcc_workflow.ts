import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { dccWorkflowList } from "@/typings/dcc/dcc_setting_workflow";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/processDcc`;

// 流程列表
export const getDccWorkflowList = (params?: ReqPage) => {
  return http.post<ResPage<dccWorkflowList.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getDccWorkflowDetail = (id: number) => {
  return http.post<Partial<dccWorkflowList.Item>>(`${baseUrl}/get/${id}`);
};

// 新增
export const createDccWorkflowData = (data: dccWorkflowList.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editDccWorkflowData = (data: dccWorkflowList.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteDccWorkflowData = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};
