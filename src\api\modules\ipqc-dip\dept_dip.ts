import { ReqPage, ResPage } from "@/api/interface/index";
import { DeptDip } from "@/typings/ipqc-dip/dept_dip";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ipqcDIPDept`;

// 列表
export const getDeptList = (params?: ReqPage) => {
  return http.post<ResPage<DeptDip.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getDeptDetail = (id: number) => {
  return http.post<DeptDip.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createDept = (data: DeptDip.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editDept = (data: DeptDip.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteDept = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportDept = (params?: DeptDip.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importDept = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importDeptTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
