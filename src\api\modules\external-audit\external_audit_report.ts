import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { ExternalAuditReport } from "@/typings/external-audit/external_audit_report";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/external-audit/statistics";

const baseUrl = `${API_PREFIX}/ExternalAuditReport`;
// 列表
export const getExternalAuditReportList = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditReport.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getExternalAuditReportDetail = (id: number) => {
  return http.post<ExternalAuditReport.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createExternalAuditReport = (data: ExternalAuditReport.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editExternalAuditReport = (data: ExternalAuditReport.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteExternalAuditReport = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportExternalAuditReport = (params?: ExternalAuditReport.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importExternalAuditReport = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importExternalAuditReportTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
// 看板
export const statsPanel = (params?: StatisticsReport) => {
  return http.post<Statistics.StatsScoreLineBar>(`${baseUrl}/statsPanel`, params);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<ExternalAuditReport.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backAuditReport = (params: { id: number; rejectReason: string }) => {
  return http.post<ExternalAuditReport.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const auditAuditReport = (id: number) => {
  return http.post<ExternalAuditReport.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getAuditReportApproveList = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditReport.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 审核通过率
export const statsEAPass = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsEAPass`, params);
};

// 审核问题点及时关闭率
export const statsEAQuestionClose = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsEAQuestionClose`, params);
};

// 审核计划按期实施率
export const statsPlanOnScheduleImplementationRate = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsPlanOnScheduleImplementationRate`, params);
};
