<template>
  <div class="table-box bg-white py-2 flex-1 flex flex-col">
    <div class="w-full text-center text-1xl font-bold">检验批次合格率</div>
    <!-- 明确设置了高度的Echarts容器 -->
    <div class="grid w-full gap-6 mb-[30px]" style="height: 350px">
      <ECharts :option="option" autoresize />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { DataAnalysis } from "@/typings/iqc/data_analysis";
import { ECOption } from "@/components/ECharts/config";

const colorMap = {
  targetLineColor: "#EC808D",
  axisColor: "#639FD2",
  normal: "#4ead5b",
  abnormal: "#ea3323",
  ytd: "#4338ca"
};

type Props = {
  chartData: DataAnalysis.BatchQualifyRateByMaterialGroup[];
  searchParams: DataAnalysis.IQueryParams;
};

const props = defineProps<Props>();

// 将API返回的数据转换为echarts需要的格式
const seriesData = computed(() => {
  if (!props.chartData || props.chartData.length === 0) return [];

  // 获取所有物料类型
  const allMaterialTypes = new Set<string>();
  props.chartData.forEach(item => {
    item.itemList.forEach(material => {
      allMaterialTypes.add(material.materialType);
    });
  });

  // 为每个物料类型创建一个series
  const materialSeries = Array.from(allMaterialTypes).map(materialType => {
    // 收集该物料类型在每个时间点的数据
    const data = props.chartData.map(timeItem => {
      const materialData = timeItem.itemList.find(item => item.materialType === materialType);
      return materialData ? materialData.passRate : 0;
    });

    return {
      name: materialType,
      type: "line",
      smooth: true,
      emphasis: {
        focus: "series"
      },
      data
    };
  });

  // 添加目标线
  const targetData = props.chartData.map(timeItem => {
    const firstItem = timeItem.itemList[0];
    return firstItem ? parseFloat(firstItem.target) : 90;
  });

  const targetSeries = {
    name: "Target",
    type: "line",
    smooth: true,
    lineStyle: {
      color: colorMap.targetLineColor,
      type: "dashed"
    },
    itemStyle: {
      color: colorMap.targetLineColor
    },
    data: targetData
  };

  return [...materialSeries, targetSeries];
});

// 提取时间数据作为X轴
const xData = computed(() => {
  if (!props.chartData) return [];
  return props.chartData.map(item => item.dateLabel);
});

const option = computed<ECOption>(() => {
  return {
    backgroundColor: "#fff",
    tooltip: {
      showContent: true,
      trigger: "axis",
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}%<br/>`;
        });
        return result;
      }
    },
    textStyle: {
      color: "#c0c3cd",
      fontSize: 14
    },
    legend: {
      bottom: 0,
      type: "scroll"
    },
    xAxis: {
      nameTextStyle: {
        color: "#c0c3cd",
        padding: [0, 0, -10, 0],
        fontSize: 14
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      data: xData.value,
      type: "category"
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value: number) => `${value}%`,
        textStyle: {
          color: colorMap.axisColor
        }
      },
      min: 0,
      max: 100
    },
    series: seriesData.value
  } as ECOption;
});
</script>
