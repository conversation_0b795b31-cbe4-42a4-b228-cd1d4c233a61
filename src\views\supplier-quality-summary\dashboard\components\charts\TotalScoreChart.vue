<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">供应商总得分分析</h3>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="TotalScoreChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";

interface Props {
  chartData: Dashboard.TotalScoreData[];
}

const props = defineProps<Props>();

// 检查是否有数据
const hasData = computed(() => {
  return props.chartData && props.chartData.length > 0;
});

// 图表配置
const chartOption = computed<ECOption>(() => {
  if (!hasData.value) return {};

  const suppliers = props.chartData.map(item => item.supplier);
  const scores = props.chartData.map(item => item.score);

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: suppliers,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: "value",
      name: "得分"
    },
    series: [
      {
        name: "总得分",
        type: "bar",
        data: scores,
        itemStyle: {
          color: "#5470c6"
        },
        label: {
          show: true,
          position: "top"
        }
      }
    ]
  };
});
</script>

<style scoped>
.echarts-container {
  width: 100%;
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.echarts-content {
  height: 350px;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
