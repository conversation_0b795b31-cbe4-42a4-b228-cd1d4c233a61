<!-- eslint-disable vue/no-parsing-error -->
<!-- eslint-disable prettier/prettier -->
<template>
  <el-dialog
    v-model="visible"
    width="900px"
    draggable
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="`${t(title)}`"
    modal-class="draggable-modal"
  >
    <el-form ref="formRef" label-position="top" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('季度')" prop="quarter">
            <el-input v-model="form.quarter" :placeholder="$t('季度')" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('工厂')" prop="plant">
            <el-input v-model="form.plant" :placeholder="$t('工厂')" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('供应商')" prop="supplierName">
            <el-input v-model="form.supplierName" :placeholder="$t('供应商')" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('物料类别')" prop="materialType">
            <el-input v-model="form.materialType" :placeholder="$t('物料类别')" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('IQC检验批次数')" prop="iqcInspectionBatchCount">
            <el-input-number v-model="form.iqcInspectionBatchCount" :min="0" style="width: 100%" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('IQC合格批次数')" prop="iqcQualifiedBatchCount">
            <el-input-number v-model="form.iqcQualifiedBatchCount" :min="0" style="width: 100%" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('LAR(%)')" prop="lar">
            <el-input v-model="form.lar"  style="width: 100%" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('制程重大品质问题发生次数(不良率≥2%)')" prop="majorQualityIssue">
            <el-input-number v-model="form.majorQualityIssue" :min="0" style="width: 100%" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('制程品质问题发生次数(不良率<2%)')" prop="minorQualityIssue">
            <el-input-number v-model="form.minorQualityIssue" :min="0" style="width: 100%" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('制程物料下线率(DPPM)')" prop="dppm">
            <el-input-number v-model="form.dppm" :min="0" style="width: 100%" :disabled="isView" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('客户投诉次数')" prop="clientComplaint">
            <el-input-number v-model="form.clientComplaint" :min="0" style="width: 100%" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('品质改善配合度')" prop="qualityImprovementCooperation">
            <el-input v-model="form.qualityImprovementCooperation" :placeholder="$t('请填写')" clearable :disabled="isView" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('品质等级')" prop="qualityGrade">
            <el-select v-model="form.qualityGrade" :placeholder="$t('请选择')" clearable filterable>
              <el-option
                v-for="({ label }, index) of state.supplier_quality_summary_quality_level"
                :key="index"
                :label="$t(label)"
                :value="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('红黑榜')" prop="redBlackList">
            <el-radio-group v-model="form.redBlackList" :disabled="isView">
              <el-radio :label="$t('红榜')">{{ $t('红榜') }}</el-radio>
              <el-radio :label="$t('黑榜')">{{ $t('黑榜') }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('品质考核总得分')" prop="totalScore">
            <el-input-number v-model="form.totalScore" :min="0" style="width: 100%" :disabled="isView" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="QuarterlyDataModal">
import { QuarterlyData } from "@/typings/supplier-quality-summary/quarterly_data";
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, watch } from "vue";
import { Dict } from "@/typings/dict";
import { useI18n } from "vue-i18n";
import { isEmpty } from "@/utils/is";

const { t } = useI18n();

interface IState {
  title: string;
  isView: boolean;
  factory: Dict.IDataItem[];
  supplier_quality_summary_quality_level: Dict.IDataItem[];
  form: Partial<QuarterlyData.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  dppm: [{ required: true, message: "请填写制程物料下线率DPPM", trigger: "blur" }],
  qualityImprovementCooperation: [{ required: true, message: "请填写品质改善配合度", trigger: "blur" }],
  totalScore: [{ required: true, message: "请填写品质考核总得分", trigger: "blur" }],
  qualityGrade: [{ required: true, message: "请填写品质等级", trigger: "blur" }],
  redBlackList: [{ required: true, message: "请填写红黑榜", trigger: "blur" }]
};

const formRef = ref<FormInstance>();
const visible = ref(false);

const state = reactive<IState>({
  title: "",
  isView: false,
  factory: [],
  supplier_quality_summary_quality_level: [],
  form: {
    redBlackList: "红榜"
  },
  api: undefined,
  getTableList: undefined
});

const { form, title, isView } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

const acceptParams = (params: IState) => {
  Object.assign(state, params);
  // 设置默认值
  if (isEmpty(params.form.dppm)) {
    params.form.dppm = 0;
  }
  if (isEmpty(params.form.totalScore)) {
    params.form.totalScore = 0;
  }
  setVisible(true);
};

const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      // 只提交可编辑的字段
      const editParams: QuarterlyData.EditParams = {
        id: form.value.id!,
        dppm: form.value.dppm!,
        totalScore: form.value.totalScore!,
        qualityImprovementCooperation: form.value.qualityImprovementCooperation!,
        qualityGrade: form.value.qualityGrade!,
        redBlackList: form.value.redBlackList!
      };

      const { success } = await state.api!(editParams);
      if (!success) {
        ElMessage.error({ message: t(`保存失败`) });
        return;
      }
      ElMessage.success({ message: t(`保存成功`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style scoped>
/* 组件样式 */
</style>
