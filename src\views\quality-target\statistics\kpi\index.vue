<script setup lang="tsx" name="quality-target-kpi">
import { computed, ref, onMounted, watch, reactive } from "vue";
import { useDict } from "@/hooks/useDict";
import { Statistics } from "@/typings/quality-target/statistics";
import { useI18n } from "vue-i18n";
import { getTimeRange } from "@/utils/index";
import useFormulaStore from "@/stores/modules/formula";
import { ColumnProps } from "@/components/ProTable/interface";
import SearchHeader from "../../../components/SearchHeader.vue";
import { QtGroup } from "@/typings/quality-target/qt_group";
import { Frequency, ReportResultList, ReportStatusList } from "@/enums/statusQualityTarget";
import Chart from "./components/Chart.vue";
import RemoteSearchJobNum from "../../../components/RemoteSearchJobNum.vue";
import { getQualityTargetList, qtTargetList } from "@/utils/qtTargetService";
import { getStatsKpiRadar } from "@/api/modules/quality-target/qt_milestone";
import { isEmpty } from "@/utils/is";

const { t } = useI18n();
const { qt_factory: factory } = useDict("qt_factory");

const formulaStore = useFormulaStore();
const formulaContent = computed(() => formulaStore.getFormulaContent("quality-target-kpi"));
const queryParams = ref<Statistics.IQueryRadarParams>({} as Statistics.IQueryRadarParams);
const type = ref(0);
const types = [
  { label: "月度", type: 0, value: "month" },
  { label: "季度", type: 1, value: "quarter" },
  { label: "年度", type: 2, value: "year" }
];

const typeValue = computed(() => types.find(v => v.type === type.value)?.value ?? "quarter");
const range = ref(getTimeRange(typeValue.value));
const resetCounter = ref(0);
const statsKpiRadar = ref<Statistics.kpiRadarDataRespList>({} as Statistics.kpiRadarDataRespList);

const filterParams = () => {
  queryParams.value = {
    ...queryParams.value
    //statsType: types.find(v => v.type === type.value)?.label ?? "季度"
  };
  /*   if (isEmpty(queryParams.value.year)) {
    queryParams.value.year = new Date().getFullYear().toString();
  } */
  return queryParams.value;
};

const getStatsKpiRadarData = async () => {
  const { data } = await getStatsKpiRadar(queryParams.value as Statistics.IQueryRadarParams);
  statsKpiRadar.value = data ?? [];
};

// 初始化默认值
const initDefaultValues = () => {
  // 设置分部默认值为"集团"
  queryParams.value.dept = "集团";
  // 设置默认起始月份为当前年份1月
  const currentYear = new Date().getFullYear();
  queryParams.value.statsMonthStart = `${currentYear}-01`;
  // 设置默认结束月份为当前年份12月
  queryParams.value.statsMonthEnd = `${currentYear}-12`;
};

const resetForm = () => {
  queryParams.value = {} as Statistics.IQueryRadarParams;
  range.value = getTimeRange(typeValue.value);
  getData();
};

const columns = reactive<ColumnProps<QtGroup.Item>[]>([
  {
    prop: "statsMonthStart",
    label: "起始月份",
    width: 180,
    search: {
      el: "date-picker",
      props: { type: "month", valueFormat: "YYYY-MM", placeholder: "请选择起始月份" },
      order: 1
    }
  },
  {
    prop: "statsMonthEnd",
    label: "结束月份",
    width: 180,
    search: {
      el: "date-picker",
      props: { type: "month", valueFormat: "YYYY-MM", placeholder: "请选择结束月份" },
      order: 2
    }
  },
  {
    prop: "dept",
    label: "分部",
    width: 120,
    search: { el: "select", order: 3, defaultValue: "集团" },
    enum: factory,
    fieldNames: { label: "label", value: "label" }
  },
  // {
  //   prop: "targetName",
  //   label: "质量目标",
  //   width: 120,
  //   tag: true,
  //   search: { el: "select", order: 3 },
  //   enum: qtTargetList,
  //   fieldNames: { label: "targetName", value: "targetName" }
  // },
  {
    prop: "reportStatus",
    label: "状态",
    width: 120,
    tag: true,
    search: { el: "select", order: 4 },
    enum: ReportStatusList,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "reportFreq",
    label: "汇报频率",
    width: 180,
    search: { el: "select", order: 5 },
    enum: Frequency,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "reportResult",
    label: "目标结果",
    width: 120,
    tag: true,
    search: { el: "select", order: 6 },
    enum: ReportResultList,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "responsibleStaffNo",
    label: "责任人",
    width: 120,
    isShow: false,
    search: {
      el: "select",
      order: 7,
      render: () => {
        return <RemoteSearchJobNum resetSignal={resetCounter.value}></RemoteSearchJobNum>;
      }
    }
  }
]);

const getData = () => {
  filterParams();
  getStatsKpiRadarData();
};

onMounted(() => {
  initDefaultValues();
  getData();
  getQualityTargetList();
});

watch([type], () => {
  range.value = getTimeRange(typeValue.value);
  getData();
});
</script>

<template>
  <div class="table-box only-search-box">
    <SearchHeader :reset="resetForm" :search="getData" :columns="columns" :search-param="queryParams" />
    <div class="flex-shrink-0 w-full card">
      <!-- <div class="flex items-center justify-between flex-shrink-0 pb-10">
        <div class="flex justify-center w-[150px]"></div>
        <div class="flex justify-center">
          <el-radio-group v-model="type">
            <el-radio-button v-for="(value, index) of types" :key="index" :label="$t(value.label)" :value="value.type" />
          </el-radio-group>
        </div>
        <span class="w-[150px]"></span>
      </div> -->

      <div class="grid flex-1 w-full gap-6 mb-[30px]">
        <Chart :chart-data="statsKpiRadar" :chart-title="$t('目标KPI分布')" :formula-content="formulaContent" />
      </div>
    </div>
  </div>
</template>
