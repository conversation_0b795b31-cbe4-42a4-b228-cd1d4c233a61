import { ReqPage, ResPage } from "@/api/interface/index";
import { CiProjectCommittee } from "@/typings/ci-project/ci_project_committee";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/committee`;

// 列表
export const getCiProjectCommitteeList = (params?: ReqPage) => {
  return http.post<ResPage<CiProjectCommittee.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCiProjectCommitteeDetail = (id: number) => {
  return http.post<CiProjectCommittee.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCiProjectCommittee = (data: CiProjectCommittee.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCiProjectCommittee = (data: CiProjectCommittee.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCiProjectCommittee = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCiProjectCommittee = (params?: CiProjectCommittee.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCiProjectCommittee = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCiProjectCommitteeTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
