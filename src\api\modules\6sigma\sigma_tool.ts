import { ReqPage, ResPage } from "@/api/interface/index";
import { SigmaTool } from "@/typings/6sigma/sigma_tool";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/6sigmaTool`;

// 列表
export const getSigmaToolList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaTool.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSigmaToolDetail = (id: number) => {
  return http.post<SigmaTool.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSigmaTool = (data: SigmaTool.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSigmaTool = (data: SigmaTool.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSigmaTool = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSigmaTool = (params?: SigmaTool.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSigmaTool = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSigmaToolTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
