import { ReqPage, ResPage } from "@/api/interface/index";
import { OqaShipment } from "@/typings/oqa/oqa_shipment";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/oqaShipment`;

// 列表
export const getOqaShipmentList = (params?: ReqPage) => {
  return http.post<ResPage<OqaShipment.Item>>(`${baseUrl}/list`, params);
};
//操作记录
export const getChangeLogList = (params?: ReqPage) => {
  return http.post<ResPage<OqaShipment.Log>>(`${baseUrl}/getChangeLogList`, params);
};
// 详情
export const getOqaShipmentDetail = (id: number) => {
  return http.post<OqaShipment.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createOqaShipment = (data: OqaShipment.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editOqaShipment = (data: OqaShipment.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteOqaShipment = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportOqaShipment = (params?: OqaShipment.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importOqaShipment = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importOqaShipmentTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
