import { ReqPage, ResPage } from "@/api/interface/index";
import { WpChange } from "@/typings/quality-work-plan/wp_change";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/wpChange`;

// 列表
export const getWpChangeList = (params?: ReqPage) => {
  return http.post<ResPage<WpChange.Item>>(`${baseUrl}/list`, params);
};

export const getListMyChangeList = (params?: ReqPage) => {
  return http.post<ResPage<WpChange.Item>>(`${baseUrl}/listMyChangeList`, params);
};

// 详情
export const getWpChangeDetail = (id: number) => {
  return http.post<WpChange.Item>(`${baseUrl}/get/${id}`);
};
//根据分部计划id获取最新变更任务
export const getByDeptPlanId = (id: number) => {
  return http.post<WpChange.Item>(`${baseUrl}/getByDeptPlanId/${id}`);
};

//根据个人计划id获取最新变更任务
export const getByStaffPlanId = (id: number) => {
  return http.post<WpChange.Item>(`${baseUrl}/getByStaffPlanId/${id}`);
};

// 新增
export const createWpChange = (data: WpChange.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editWpChange = (data: WpChange.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteWpChange = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportWpChange = (params?: WpChange.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importWpChange = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importWpChangeTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 退回
export const dismiss = (params: { id: number; rejectReason: string }) => {
  return http.post<WpChange.Item>(`${baseUrl}/dismiss`, params);
};

// 审批通过
export const pass = (data: WpChange.Item) => {
  return http.post(`${baseUrl}/pass`, data);
};

//提交
export const submit = (data: WpChange.Item) => {
  return http.post(`${baseUrl}/submit`, data);
};
