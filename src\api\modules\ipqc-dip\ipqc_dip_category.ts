import { ReqPage, ResPage } from "@/api/interface/index";
import { IpqcDipCategory } from "@/typings/ipqc-dip/ipqc_dip_category";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ipqcDIPCategory`;

// 列表
export const getDipCategoryList = (params?: ReqPage) => {
  return http.post<ResPage<IpqcDipCategory.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getDipCategoryDetail = (id: number) => {
  return http.post<IpqcDipCategory.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createDipCategory = (data: IpqcDipCategory.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editDipCategory = (data: IpqcDipCategory.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteDipCategory = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportDipCategory = (params?: IpqcDipCategory.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importDipCategory = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importDipCategoryTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
