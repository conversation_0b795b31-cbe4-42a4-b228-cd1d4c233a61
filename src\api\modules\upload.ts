import { Upload } from "@/api/interface/index";
import { API_PREFIX } from "@/api/config/servicePort";
import http from "@/api";

/**
 * @name 文件上传模块
 */
// 图片上传
export const uploadImg = (params: FormData) => {
  return http.post<Upload.ResFileUrl>(`${API_PREFIX}/common/upload`, params);
};

export const getFileDetail = (id: number) => {
  return http.get<Upload.ResFileUrl>(`${API_PREFIX}/sys_assets/${id}`);
};

// 视频上传
export const uploadVideo = (params: FormData) => {
  return http.post<Upload.ResFileUrl>(`${API_PREFIX}/common/upload`, params);
};
