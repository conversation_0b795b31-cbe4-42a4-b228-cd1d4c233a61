<!-- eslint-disable vue/html-closing-bracket-newline -->
<!-- eslint-disable prettier/prettier -->
<template>
  <div class="table-box">
    <el-tabs v-model="activeTab" type="card" class="document-tabs">
      <el-tab-pane name="unpublished">
        <template #label>
          <el-icon style="margin-right: 8px"><EditPen /></el-icon>
          <span>{{ $t("未发布") }}</span>
        </template>
        <unpublished ref="unpublishedRef" />
      </el-tab-pane>

      <el-tab-pane name="published">
        <template #label>
          <el-icon style="margin-right: 8px"><Finished /></el-icon>
          <span>{{ $t("已发布") }}</span>
        </template>
        <published ref="publishedRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="tsx" name="dcc-dcc-my-document">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import MyDocumentEditModal from "./components/MyDocumentEditModal.vue";
import MyDocumentVersionUpdateModal from "./components/MyDocumentVersionUpdateModal.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { DocumentRemove, DocumentChecked } from "@element-plus/icons-vue";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { ref, reactive, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import useUserStore from "@/stores/modules/user";
import unpublished from "./unpublished.vue";
import published from "./published.vue";

const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const { t } = useI18n();
const activeTab = ref("unpublished");

const unpublishedRef = ref<InstanceType<typeof unpublished> | null>(null);
const publishedRef = ref<InstanceType<typeof published> | null>(null);
</script>

<style scoped>
.document-tabs {
  margin-bottom: 16px;
  background-color: white;
  padding: 10px;
}
.document-tabs ::v-deep .el-tabs__item {
  font-size: 14px; /* 设置字体大小 */
}
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.block-tip {
  margin: 10px 0;
  font-weight: bold;
}

.draggable-modal .el-dialog__header {
  cursor: move;
  user-select: none;
}
</style>
