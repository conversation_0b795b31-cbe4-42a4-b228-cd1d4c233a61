import { Interface } from "readline";
import { QtStaff } from "./qt_staff";

export namespace Statistics {
  export interface FormulaExplanation {
    code: string;
    title: string;
    content: string;
  }

  interface IQueryParams {
    statsType: string;
    year: string;
    dept?: string;
    targetName?: string;
    reportStatus?: string;
    reportResult?: string;
    reportFreq?: string;
    responsibleStaffNo?: string;
  }

  interface IQueryRadarParams {
    //statsType: string;
    //year: string;
    statsMonthStart: string;
    statsMonthEnd: string;
    dept?: string;
    targetName?: string;
    reportStatus?: string;
    reportResult?: string;
    reportFreq?: string;
    responsibleStaffNo?: string;
  }

  interface IQueryHomeParams {
    statsType: string;
    searchStartDate: string;
    searchEndDate: string;
  }
  interface IQueryStaffParams {
    searchYear: string;
    category?: string;
    kpiName?: string;
  }

  interface StatusItem {
    reportStatus: string;
    totalQty: number;
  }

  interface statsPassedRatio {
    passedRatioDataList: RatioItem[];
    deptList: string[];
    statsDataLabelList: string[];
  }

  interface statusPieDataList {
    statusPieDataList: StatusItem[];
    deptList: string[];
    statsDataLabelList: string[];
  }

  interface RatioItem {
    statsDataLabel: string;
    dept: string;
    totalQty: number;
    passedQty: number;
    passedRatio: number;
  }

  interface statsDeptPassedRatio {
    dataList: RatioDeptItem[];
    statsDataLabelList: string[];
    responsibleStaffList: string[];
  }
  interface RatioDeptItem {
    statsDataLabel: string;
    responsibleStaffName: string;
    totalQty: number;
    passedQty: number;
    passedRatio: number;
  }

  interface OverviewList {
    overviewList: OverviewListItem[];
  }

  interface OverviewListItem {
    statsDataLabel: string;
    totalScore: number;
  }

  interface TreeList {
    treeList: TreeListItem[];
  }
  interface TreeListItem {
    categoryName: string;
    id?: number;
    children: TreeListItemChildren[];
  }
  interface TreeListItemChildren {
    kpiName?: string;
    categoryName?: string;
    totalScore?: number;
    id?: number;
  }
  interface DataList {
    dataList: QtStaff.Item[];
  }

  interface statsStaffPerformanceBar {
    dataList: StaffPerformanceBarDataItem[];
  }

  interface StaffPerformanceBarDataItem {
    kpiName: string;
    totalScore: number;
  }

  interface StatsFinishedRatioRadar {
    finishedRatioRadar: finishedRatioRadarItem[];
    passedRatioRadar: finishedRatioRadarItem[];
  }

  interface finishedRatioRadarItem {
    kpiName: string;
    finishedRatio: number;
  }

  interface kpiRadarDataRespItem {
    targetName: string;
    radarIndicator: string;
    finishedIndicator: string;
  }

  interface kpiRadarDataRespList {
    kpiRadarDataRespList: kpiRadarDataRespItem[];
  }

  interface statsKpiTrend {
    trendDataRespList: statsKpiTrendItem[];
  }

  interface statsKpiTrendItem {
    statsDataLabel: string;
    dept: string;
    targetName: string;
    targetRatio: number;
    finishedIndicator: number;
  }

  interface Home {
    statsDataLabelList: string[];
    deptList: string[];
    passedRatioDataList: RatioItem[];
    statusPieDataList: StatusItem[];
    bannerResp: BannerResp;
  }
  interface BannerResp {
    totalQty: number;
    finishedQty: number;
    expiredQty: number;
    closeToExpirationQty: number;
  }

  interface StatsDataItem {
    statsDataLabel: string;
    statsValue: string;
  }

  interface CategoryData {
    type: string;
    categoryName: string;
    statsDataList: StatsDataItem[];
  }

  interface TargetData {
    dept: string;
    targetName: string;
    target: string;
    aviator: string;
    operator: string;
    isPercent: string;
    categoryNumerator: CategoryData;
    categoryDenominator: CategoryData;
    categoryStatsResult: CategoryData;
  }
}
