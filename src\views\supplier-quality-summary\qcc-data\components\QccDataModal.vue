<template>
  <el-dialog
    v-model="visible"
    width="1200px"
    draggable
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="`${t(title)}`"
    modal-class="draggable-modal"
  >
    <el-form ref="formRef" label-position="top" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <!-- 专案信息 -->
      <div class="section-title">{{ $t("专案信息") }}</div>
      <el-descriptions :column="4" border>
        <el-descriptions-item :label="$t('案例编号')">
          <el-input v-if="!isView" v-model="form.caseNo" :placeholder="$t('请输入案例编号')" />
          <span v-else>{{ form.caseNo || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('工厂')">
          <el-input v-if="!isView" v-model="form.plant" :placeholder="$t('请选择工厂')" readonly @click="openSelectionDialog" />
          <span v-else>{{ form.plant || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('供应商')">
          <span>{{ form.supplierName || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('物料类别')">
          <span>{{ form.materialType || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('专案内容')" :span="4">
          <el-input v-if="!isView" v-model="form.projectContent" type="textarea" :placeholder="$t('请输入专案内容')" />
          <span v-else>{{ form.projectContent || "-" }}</span>
        </el-descriptions-item>
      </el-descriptions>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item :label="$t('状态')" prop="status">
            <el-select v-model="form.status" :placeholder="$t('请选择状态')" style="width: 100%">
              <el-option label="进行中" value="进行中" />
              <el-option label="已完成" value="已完成" />
              <el-option label="暂停" value="暂停" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('计划完成日期')" prop="planCompleteDate">
            <el-date-picker
              v-model="form.planCompleteDate"
              type="date"
              :placeholder="$t('请选择日期')"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('收益汇总')" prop="revenueSummary">
            <el-input v-model="form.revenueSummary" type="textarea" :placeholder="$t('请输入收益汇总')" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('改善报告')" prop="improvementReportUrl">
            <UploadFiles
              :file-url="reportFile"
              :multiple="false"
              :is-show-tip="true"
              :immediate="true"
              :limit="1"
              :file-type="['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'pdf']"
              btn-name="上传附件"
              @upload-success="reportFileSuccess"
              @file-list-empty="reportFileEmpty"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 进度更新 -->
      <div class="section-title">{{ $t("进度更新") }}</div>
      <ProgressUpdateTable
        ref="progressUpdateTableRef"
        :progress-list="form.progressUpdateList"
        :is-view="isView"
        @update:progress-list="value => (form.progressUpdateList = value)"
      />
    </el-form>

    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-if="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>

    <SupplierSelector ref="supplierSelectorRef" @select="handleSupplierSelect" @cancel="handleSupplierCancel" />
  </el-dialog>
</template>

<script setup lang="tsx" name="QccDataModal">
import { QccData } from "@/typings/supplier-quality-summary/qcc_data";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs } from "vue";
import { Dict } from "@/typings/dict";
import { useI18n } from "vue-i18n";
import { isEmpty } from "@/utils/is";
import SupplierSelector from "../../components/SupplierSelector/index.vue";
import ProgressUpdateTable from "@/views/supplier-quality-summary/qcc-data/components/ProgressUpdateTable.vue";
import UploadFiles from "@/components/Upload/UploadFiles.vue";

const { t } = useI18n();

interface IState {
  title: string;
  isView: boolean;
  factory: Dict.IDataItem[];
  form: Partial<QccData.Item>;
  api?: (params: any) => Promise<any>;
  updateApi?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const visible = ref(false);
const formRef = ref<FormInstance>();
const supplierSelectorRef = ref<InstanceType<typeof SupplierSelector> | null>(null);
const progressUpdateTableRef = ref<InstanceType<typeof ProgressUpdateTable> | null>(null);

const reportFile = ref<{ name: string; url: string }[]>([]);

const state = reactive<IState>({
  title: "",
  isView: false,
  factory: [],
  form: {
    progressUpdateList: [] as any[]
  },
  api: undefined,
  updateApi: undefined,
  getTableList: undefined
});

const rules = reactive({
  caseNo: [{ required: true, message: t("请输入案例编号"), trigger: "blur" }],
  plant: [{ required: true, message: t("请选择工厂"), trigger: "blur" }],
  projectContent: [{ required: true, message: t("请输入专案内容"), trigger: "blur" }],
  status: [{ required: true, message: t("请选择状态"), trigger: "change" }],
  planCompleteDate: [{ required: true, message: t("请选择计划完成日期"), trigger: "change" }]
});

const { form, title, isView } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

// 打开选择弹窗
const openSelectionDialog = () => {
  supplierSelectorRef.value?.open();
};

// 处理供应商选择
const handleSupplierSelect = (data: SupplierInformation.Item) => {
  form.value.plant = data.plant;
  form.value.supplierName = data.supplierName;
  form.value.materialType = data.materialType;
};

// 处理供应商取消
const handleSupplierCancel = () => {
  // 取消选择逻辑
};

// 附件上传成功
const reportFileSuccess = (file: { name: string; url: string }) => {
  form.value.improvementReportName = file.name;
  form.value.improvementReportUrl = file.url;
};

// 附件列表为空
const reportFileEmpty = () => {
  form.value.improvementReportName = "";
  form.value.improvementReportUrl = "";
};

const acceptParams = (params: IState) => {
  Object.assign(state, params);

  // 初始化附件
  if (params.form.improvementReportName && params.form.improvementReportUrl) {
    reportFile.value = [
      {
        name: params.form.improvementReportName,
        url: params.form.improvementReportUrl
      }
    ];
  } else {
    reportFile.value = [];
  }

  // 初始化进度更新列表
  if (!Array.isArray(params.form.progressUpdateList)) {
    params.form.progressUpdateList = [];
  }

  setVisible(true);
};

const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const { success, data } = await state.api!(form.value);
      if (!success) {
        ElMessage.error({ message: t(`保存失败`) });
        return;
      }
      form.value = data;
      if (!isEmpty(form.value.id) && form.value.id! > 0) {
        state.api = state.updateApi;
      }
      ElMessage.success({ message: t(`保存成功`) });
      state.getTableList!();
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 10px 0;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

:deep(.el-descriptions__body) {
  background-color: #fafafa;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}
</style>
