import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { QccData } from "@/typings/supplier-quality-summary/qcc_data";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qccImprove`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;

// 获取QCC数据列表
export const getQccData = (params?: ReqPage) => {
  return http.post<ResPage<QccData.Item>>(`${baseUrl}/list`, params);
};

// 获取QCC数据详情
export const getQccDataDetail = (id: number) => {
  return http.get<Partial<QccData.Item>>(`${baseUrl}/get/${id}`);
};

// 创建质量数据
export const createQccData = (data: QccData.NewParams) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改质量数据
export const editQccData = (data: QccData.EditParams) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 导出
export const exportQccData = (ids: number[]) => {
  return http.post(`${baseUrl}/exportListData`, { ids });
};

// 删除
export const deleteQccData = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
