<template>
  <div class="jqe-data-view">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="月份">
          <el-date-picker
            v-model="searchForm.statsMonthStart"
            type="month"
            placeholder="开始月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 150px"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="—">
          <el-date-picker
            v-model="searchForm.statsMonthEnd"
            type="month"
            placeholder="结束月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 150px"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-container">
      <!-- 复检不良率趋势 -->
      <div class="chart-card">
        <ReinspectionDefectChart :chart-data="chartData.reinspectionDefectRateTrend" />
      </div>

      <!-- OQC不良率趋势 -->
      <div class="chart-card">
        <OqcDefectChart :chart-data="chartData.oqcDefectRateTrend" />
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="table-section">
      <div class="table-header">
        <h3>JQE+黑榜详情</h3>
        <el-button type="primary" @click="exportData">导出</el-button>
      </div>
      <el-table :data="tableData" border stripe max-height="400" style="width: 100%">
        <el-table-column prop="monthly" label="月份" width="120" />
        <el-table-column prop="plant" label="工厂" width="120" />
        <el-table-column prop="supplier" label="供应商" width="150" />
        <el-table-column prop="materialType" label="物料类别" width="120" />
        <el-table-column label="是否指定JQE" width="120">
          <template #default="{ row }">
            <el-tag :type="row.isJqeAssigned ? 'success' : 'danger'">
              {{ row.isJqeAssigned ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="上季度是否黑榜" width="140">
          <template #default="{ row }">
            <el-tag :type="row.wasBlacklistedLastQuarter ? 'danger' : 'success'">
              {{ row.wasBlacklistedLastQuarter ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建人" width="120" />
        <el-table-column prop="createAt" label="创建时间" width="165" />
        <el-table-column prop="updateBy" label="修改人" width="120" />
        <el-table-column prop="updateAt" label="修改时间" width="165" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="tsx" name="JqeDataView">
import { ref, reactive, onMounted, watch } from "vue";

import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { statsJqeData } from "@/api/modules/supplier-quality-summary/supplier_quality_summary_dashboard";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";
import ReinspectionDefectChart from "./charts/ReinspectionDefectChart.vue";
import OqcDefectChart from "./charts/OqcDefectChart.vue";

interface Props {
  filterForm: {
    plant: string;
    materialType: string;
  };
}

const props = defineProps<Props>();

// 搜索表单 - 默认最近三个月
const searchForm = reactive({
  statsMonthStart: dayjs().subtract(3, "month").format("YYYY-MM"),
  statsMonthEnd: dayjs().format("YYYY-MM")
});

// 图表数据
const chartData = ref<Dashboard.JqeData>({
  reinspectionDefectRateTrend: [],
  oqcDefectRateTrend: [],
  list: []
});

// 表格数据
const tableData = ref<Dashboard.JqeData["list"]>([]);

// 日期变更处理
const handleDateChange = () => {
  if (props.filterForm.materialType) {
    loadData();
  }
};

// 加载数据
const loadData = async () => {
  // 确保materialType不为空
  if (!props.filterForm.materialType) {
    console.warn("materialType为空，跳过加载JQE数据");
    return;
  }

  try {
    const params: Dashboard.IQueryParamsJqeData = {
      statsMonthStart: searchForm.statsMonthStart,
      statsMonthEnd: searchForm.statsMonthEnd,
      plant: props.filterForm.plant,
      materialType: props.filterForm.materialType
    };

    const response = await statsJqeData(params);

    if (response.data) {
      chartData.value = response.data;
      tableData.value = response.data.list || [];
    }
  } catch (error) {
    console.error("加载JQE数据失败:", error);
    ElMessage.error("加载JQE数据失败");
  }
};

// 导出数据
const exportData = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning("暂无数据可导出");
    return;
  }

  const headers = [
    "月份",
    "工厂",
    "供应商",
    "物料类别",
    "是否指定JQE",
    "上季度是否黑榜",
    "创建人",
    "创建时间",
    "修改人",
    "修改时间"
  ];
  const csvContent = [
    headers.join(","),
    ...tableData.value.map(row =>
      [
        row.monthly,
        row.plant,
        row.supplier,
        row.materialType,
        row.isJqeAssigned ? "是" : "否",
        row.wasBlacklistedLastQuarter ? "是" : "否",
        row.createBy,
        row.createAt,
        row.updateBy,
        row.updateAt
      ].join(",")
    )
  ].join("\n");

  const blob = new Blob(["\uFEFF" + csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = `JQE黑榜数据_${dayjs().format("YYYY-MM-DD")}.csv`;
  link.click();
};

// 监听筛选条件变化
watch(
  () => [props.filterForm.plant, props.filterForm.materialType],
  () => {
    // 只有当materialType不为空时才加载数据
    if (props.filterForm.materialType) {
      loadData();
    }
  },
  { deep: true }
);

// 初始化
onMounted(() => {
  // 只有当materialType不为空时才加载数据
  if (props.filterForm.materialType) {
    loadData();
  }
});
</script>

<style scoped>
.jqe-data-view {
  padding: 20px 0;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #333;
}
</style>
