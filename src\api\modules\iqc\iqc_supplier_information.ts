import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { SupplierData } from "@/typings/iqc/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/supplierInfo`;

// 获取供应商数据列表
export const getSupplierInformation = (params?: ReqPage) => {
  return http.post<ResPage<SupplierData.Item>>(`${baseUrl}/list`, params);
};

// 创建供应商数据
export const createSupplierInformation = (data: SupplierData.NewParams) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改供应商数据
export const editSupplierInformation = (data: SupplierData.EditParams) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 导入
export const importSupplierInformation = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSupplierInformationTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 删除
export const deleteSupplierInformation = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};
