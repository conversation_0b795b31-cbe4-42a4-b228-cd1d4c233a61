export namespace Statistics {
  export interface TotalScoreData {
    supplier: string;
    score: number;
  }
  export interface GradeDistributionData {
    name: string;
    value: number;
  }
  export interface ClientComplaintData {
    supplier: string;
    clientComplaint: number;
    target: number;
  }
  export interface LarQuarterlyData {
    supplier: string;
    lar: number;
    target: number;
  }
  export interface MajorQualityIssueData {
    supplier: string;
    actual: number;
    target: number;
  }

  export interface IQueryParams {
    quarter: string;
    plant: string;
    materialType: string;
    supplierName: string[];
  }
}
