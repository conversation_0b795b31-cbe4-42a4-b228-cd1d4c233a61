import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerReturnOrder } from "@/typings/customer-refund/customer_return_order";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/customer-refund/statistics";

const baseUrl = `${API_PREFIX}/CustomerReturn`;

// 列表
export const getCustomerReturnOrderList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerReturnOrder.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerReturnOrderDetail = (id: number) => {
  return http.post<CustomerReturnOrder.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerReturnOrder = (data: CustomerReturnOrder.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerReturnOrder = (data: CustomerReturnOrder.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerReturnOrder = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerReturnOrder = (params?: CustomerReturnOrder.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerReturnOrder = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerReturnOrderTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 客退数量统计
export const statsCustomerReturnNumber = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ItemList[]>(`${baseUrl}/statsCustomerReturnNumber`, params);
};

// 退货金额统计
export const statsCustomerReturnCost = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ItemList[]>(`${baseUrl}/statsCustomerReturnCost`, params);
};
