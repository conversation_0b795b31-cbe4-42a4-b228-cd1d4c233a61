export namespace Statistics {
  export interface TotalScoreData {
    supplier: string;
    score: number;
  }

  export interface ClientComplaintData {
    supplier: string;
    clientComplaint: number;
    target: number;
  }
  export interface LarMonthlyData {
    supplier: string;
    lar: number;
    target: number;
  }
  export interface MajorQualityIssueData {
    supplier: string;
    actual: number;
    target: number;
  }

  export interface IQueryParams {
    monthly: string;
    plant: string;
    materialType: string;
    supplierName: string[];
  }
}
