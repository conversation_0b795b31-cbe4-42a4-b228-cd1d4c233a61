import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerDemandProductInfo } from "@/typings/customer-demand/customer_demand_productInfo";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/productInfo`;

// 列表
export const getCustomerDemandProductInfoList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerDemandProductInfo.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerDemandProductInfoDetail = (id: number) => {
  return http.post<CustomerDemandProductInfo.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerDemandProductInfo = (data: CustomerDemandProductInfo.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerDemandProductInfo = (data: CustomerDemandProductInfo.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerDemandProductInfo = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerDemandProductInfo = (params?: CustomerDemandProductInfo.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerDemandProductInfo = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerDemandProductInfoTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
