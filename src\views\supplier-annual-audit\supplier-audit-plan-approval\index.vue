<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="{}">
        <el-button type="primary" @click="openSupplierAuditPlanModal('审批')">{{ $t("审批") }}</el-button>
        <el-button type="primary" @click="handleTransfer()">{{ $t("转办") }}</el-button>
      </template>
    </ProTable>
    <SupplierAuditPlanModal ref="SupplierAuditPlanModalRef" />
    <TransferModal ref="TransferModalRef" />
  </div>
</template>

<script setup lang="tsx" name="supplier-annual-audit-supplier-audit-plan-approval">
import {
  createSupplierAuditPlan,
  editSupplierAuditPlan,
  getSupplierAuditPlanDetail,
  getSupplierAuditPlanApproveList,
  reassignAuditor
} from "@/api/modules/supplier-annual-audit/supplier_audit_plan";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import SupplierAuditPlanModal from "../supplier-audit-plan/components/SupplierAuditPlanModal.vue";
import { ElMessage } from "element-plus";
import { useAdminDict, useDict } from "@/hooks/useDict";
import { SupplierAuditPlan } from "@/typings/supplier-annual-audit/supplier_audit_plan";
import { ref, reactive, watch, getCurrentInstance } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import DateRange from "../../components/DateRange.vue";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { useRoute } from "vue-router";
import { ApprovalStatus, PendingApproval } from "@/enums/statusSupplierAnnualAudit";
import TransferModal from "../../components/TransferModal.vue";
import LinkFile from "../../components/LinkFile.vue";
import { openTransferModal } from "@/utils/transfer";
import RemoteSearchJobNum from "../../components/RemoteSearchJobNum.vue";
import LinkModal from "../../components/LinkModal.vue";
const instance = getCurrentInstance();
const componentName = instance?.type.name;
const route = useRoute();

const { t } = useI18n();

// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
let queryParams = reactive<SupplierAuditPlan.IQueryParams>({} as SupplierAuditPlan.IQueryParams);
const TransferModalRef = ref<InstanceType<typeof TransferModal> | null>(null);

// 打开 drawer(新增、查看、编辑)
const SupplierAuditPlanModalRef = ref<InstanceType<typeof SupplierAuditPlanModal> | null>(null);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

const { srm_audit_type, srm_audit_scope, srm_audit_result, srm_supplier_type, srm_material_team, srm_supplier } = useDict(
  "common_status",
  "srm_audit_type",
  "srm_audit_scope",
  "srm_audit_result",
  "srm_supplier_type",
  "srm_material_team",
  "srm_supplier"
);

// 表格配置项
const columns = reactive<ColumnProps<SupplierAuditPlan.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "orderNo",
    label: "审核计划编号",
    width: 200,
    search: { el: "input", order: 20 },
    render({ row }) {
      return <LinkModal name={row.orderNo} row={row} onOpenDialog={openPreview} />;
    }
  },
  {
    prop: "auditStatus",
    label: "状态",
    width: 120,
    tag: true,
    enum: ApprovalStatus,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "plant",
    label: "工厂"
  },
  {
    prop: "supplierType",
    label: "供应商类型",
    width: 120,
    enum: srm_supplier_type,
    fieldNames: { label: "label", value: "label" },
    search: { order: 30, el: "select", props: { filterable: true } }
  },

  {
    prop: "sqeName",
    label: "SQE",
    width: 120
  },
  {
    prop: "sqeNo",
    label: "SQE",
    width: 120,
    isShow: false,
    search: {
      el: "select",
      order: 40,
      render: () => {
        return <RemoteSearchJobNum resetSignal={resetCounter.value}></RemoteSearchJobNum>;
      }
    }
  },
  { prop: "materialTeam", label: "物料组", width: 120 },
  { prop: "materialName", label: "物料名称", width: 120, search: { el: "input", order: 50 } },
  {
    prop: "supplierName",
    label: "供应商名称",
    width: 120,
    search: { order: 60, el: "select", props: { filterable: true } },
    enum: srm_supplier,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "materialTeam", label: "物料组", width: 120, enum: srm_material_team, fieldNames: { label: "label", value: "label" } },
  { prop: "supplierAddr", label: "供应商地址", width: 120 },
  { prop: "planAuditDate", label: "计划审核月份", width: 120 },
  { prop: "route", label: "省外行程路线", width: 120 },
  {
    prop: "actualAuditDate",
    label: "实际审核日期",
    width: 120
  },
  {
    prop: "auditType",
    label: "审核类型",
    width: 120,
    enum: srm_audit_type,
    fieldNames: { label: "label", value: "label" },
    search: { order: 70, el: "select", props: { filterable: true } }
  },
  { prop: "purpose", label: "审核目的", width: 120 },
  {
    prop: "auditScope",
    label: "审核范围",
    width: 120,
    enum: srm_audit_scope.value
  },
  { prop: "auditSite", label: "审核场所", width: 120 },
  {
    prop: "refFileName",
    label: "审核参考文件",
    width: 240,
    render: ({ row }) => {
      return <LinkFile url={row.refFileUrl} name={row.refFileName} />;
    }
  },
  {
    prop: "auditResult",
    label: "审核结果",
    width: 120,
    enum: srm_audit_result,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "auditScore", label: "审核分值", width: 120 },
  {
    prop: "otherAuditStaffs",
    label: "其他审核人员",
    width: 120,
    render: ({ row }) => {
      return row.otherAuditStaffList?.map(item => item.staffName).join(",");
    },
    search: { el: "input", order: 100 }
  },

  { prop: "currentAuditor", label: "当前审批人", width: 120 },
  { prop: "remarks", label: "备注", width: 120 },
  { prop: "createBy", label: "创建者", width: 120 },
  {
    prop: "createAt",
    label: "创建时间",
    width: 180,
    search: {
      order: 10,
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  { prop: "updateBy", label: "更新者", width: 120 },
  { prop: "updateAt", label: "更新时间", width: 165 }
]);

const getTableList = async (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }
  if (!isEmpty(filterAuditDate.value) && filterAuditDate.value.length > 0) {
    condition.startAuditDate = filterAuditDate.value[0];
    condition.endAuditDate = filterAuditDate.value[1];
  }
  queryParams = reactive(condition);

  const response = await getSupplierAuditPlanApproveList({
    condition,
    pageNum,
    pageSize
  });

  // 为每个list项添加currentAuditor字段
  if (response.data && response.data.list) {
    response.data.list = response.data.list.map((item: any) => {
      // 计算currentAuditor: auditStaffList中按照sort字段排序，第一个isAudited为未审核的staffNo的值
      let currentAuditor = "";
      if (item.auditStaffList && item.auditStaffList.length > 0) {
        // 按sort字段排序
        const sortedStaffList = [...item.auditStaffList].sort((a, b) => a.sort - b.sort);
        // 找到第一个isAudited为"未审核"的记录
        const firstUnauditedStaff = sortedStaffList.find(staff => staff.isAudited === "未审核");
        if (firstUnauditedStaff) {
          currentAuditor = firstUnauditedStaff.staffNo;
        }
      }

      return {
        ...item,
        currentAuditor
      };
    });
  }

  return response;
};

const resetCounter = ref(0);
const filterDate = ref([]);
const filterAuditDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const handleSelectedAuditDates = (date: []) => {
  filterAuditDate.value = date;
};
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const openPreview = (row: Partial<SupplierAuditPlan.Item> = {}) => {
  openSupplierAuditPlanModal("审批", row);
};

const openSupplierAuditPlanModal = (title: string, row: Partial<SupplierAuditPlan.Item> = {}) => {
  if (isEmptyObj(row)) {
    check();
  }
  const form = !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isApproval: true,
    isView: true,
    srm_audit_scope: srm_audit_scope.value,
    srm_audit_result: srm_audit_result.value,
    srm_supplier_type: srm_supplier_type.value,
    srm_audit_type: srm_audit_type.value,
    srm_material_team: srm_material_team.value,
    srm_supplier: srm_supplier.value,
    factory: factory.value,
    form,
    api: title === "新增" ? createSupplierAuditPlan : title === "编辑" ? editSupplierAuditPlan : undefined,
    getTableList: proTable.value?.getTableList
  };
  SupplierAuditPlanModalRef.value?.acceptParams(params);
};

const handleTransfer = () => {
  openTransferModal(currentRow, TransferModalRef, proTable, check, reassignAuditor, PendingApproval);
};

const getDetail = async () => {
  if (route.name !== componentName) return;
  const id = Number(route?.query?.id) || 0;
  if (!isEmpty(id)) {
    try {
      const { success, data } = await getSupplierAuditPlanDetail(id);
      if (!success) {
        ElMessage.error(t("审批记录不存在"));
        return;
      }
      openSupplierAuditPlanModal(t("审批"), data);
    } catch (err) {}
  } else {
    SupplierAuditPlanModalRef.value?.setVisible(false);
  }
};
watch(() => route.query?.id, getDetail, { deep: true, immediate: true });
</script>
