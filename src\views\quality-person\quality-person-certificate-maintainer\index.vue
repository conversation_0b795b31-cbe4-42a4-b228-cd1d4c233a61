<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam">
      <template #tableHeader="{}">
        <el-button
          v-auth="'quality-person-quality-person-certificate-maintainer:edit'"
          type="primary"
          @click="openQualityPersonCertificateMaintainerModal('编辑')"
          >{{ $t("编辑") }}
        </el-button>
      </template>
    </ProTable>
    <QualityPersonCertificateMaintainerModal ref="QualityPersonCertificateMaintainerModalRef" />
  </div>
</template>

<script setup lang="tsx" name="quality-person-certificate-maintainer">
import {
  getQualityPersonCertificateMaintainerList,
  createQualityPersonCertificateMaintainer,
  editQualityPersonCertificateMaintainer
} from "@/api/modules/quality-person/quality_person_certificate_maintainer";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import QualityPersonCertificateMaintainerModal from "./components/QualityPersonCertificateMaintainerModal.vue";
import { QualityPersonCertificateMaintainer } from "@/typings/quality-person/quality_person_certificate_maintainer";
import { isEmptyObj } from "@/utils/is";
import { ref, reactive } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";

import { useI18n } from "vue-i18n";

// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const QualityPersonCertificateMaintainerModalRef = ref<InstanceType<typeof QualityPersonCertificateMaintainerModal> | null>(null);
let queryParams = reactive<QualityPersonCertificateMaintainer.IQueryParams>(
  {} as QualityPersonCertificateMaintainer.IQueryParams
);

const initParam = reactive({});

// 表格配置项
const columns = reactive<ColumnProps<QualityPersonCertificateMaintainer.Item>[]>([
  { type: "selection", fixed: "left", width: 60 },
  { type: "index", label: "序号", width: 80 },
  { prop: "workNo", label: "工号" },
  { prop: "workName", label: "姓名" },
  { prop: "workEmail", label: "邮箱" },
  { prop: "updateBy", label: "更新者" },
  { prop: "updateAt", label: "更新时间" }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  queryParams = reactive(condition);
  return getQualityPersonCertificateMaintainerList({
    condition,
    pageNum,
    pageSize
  });
};

const openQualityPersonCertificateMaintainerModal = (
  title: string,
  row: Partial<QualityPersonCertificateMaintainer.Item> = {}
) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };

  const params = {
    title,
    isView: title === "查看",
    form,
    api:
      title === "新增"
        ? createQualityPersonCertificateMaintainer
        : title === "编辑"
          ? editQualityPersonCertificateMaintainer
          : undefined,
    getTableList: proTable.value?.getTableList
  };

  QualityPersonCertificateMaintainerModalRef.value?.acceptParams(params);
};
</script>
