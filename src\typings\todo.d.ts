export interface TodoItem {
  id: number;
  isDelete: string;
  createdBy: string | null;
  createdTime: string;
  updatedBy: string | null;
  updatedTime: string;
  userId: number;
  title: string;
  url: string;
  status: string;
  appCode: string;
  path: string;
  menuId: number;
}

export interface TodoData {
  pending: number;
  processed: number;
  rejected: number;
  list: {
    list: TodoItem[];
  };
}
