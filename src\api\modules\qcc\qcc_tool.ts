import { ReqPage, ResPage } from "@/api/interface/index";
import { QccTool } from "@/typings/qcc/qcc_tool";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/tool`;

// 列表
export const getQccToolList = (params?: ReqPage) => {
  return http.post<ResPage<QccTool.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQccToolDetail = (id: number) => {
  return http.post<QccTool.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQccTool = (data: QccTool.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQccTool = (data: QccTool.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQccTool = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQccTool = (params?: QccTool.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQccTool = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQccToolTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
