import { ReqPage, ResPage } from "@/api/interface/index";
import { FaSmtInspection } from "@/typings/fa/fa_smt_inspection";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/faSMTInspection`;

// 列表
export const getFaSmtInspectionList = (params?: ReqPage) => {
  return http.post<ResPage<FaSmtInspection.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getFaSmtInspectionDetail = (id: number) => {
  return http.post<FaSmtInspection.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createFaSmtInspection = (data: FaSmtInspection.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editFaSmtInspection = (data: FaSmtInspection.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteFaSmtInspection = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportFaSmtInspection = (params?: FaSmtInspection.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importFaSmtInspection = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importFaSmtInspectionTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
