import { API_PREFIX, zxQualityActivityBaseUrl } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}${zxQualityActivityBaseUrl}/task`;

import http from "@/api";
import { ReqPage, ResPage } from "@/api/interface";

export const getList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/list`, params);
};

export const create = (data: any) => {
  return http.post(`${baseUrl}/add`, data);
};

export const update = (data: any) => {
  return http.post(`${baseUrl}/edit`, data);
};

export const submit = (data: any) => {
  return http.post(`${baseUrl}/submit`, data);
};
export const save = (data: any) => {
  return http.post(`${baseUrl}/save`, data);
};

export const remove = (id: number | number[]) => {
  const ids = Array.isArray(id) ? id.join(",") : id;
  return http.post(`${baseUrl}/del/${ids}`);
};

// executeTask
export const executeTask = (data: ZxQualityActivityTask.Item) => {
  return http.post(`${baseUrl}/execute-task`, {
    taskId: data.id,
    taskExecuteDetail: data.taskExecuteDetail,
    file: data.file
  });
};
