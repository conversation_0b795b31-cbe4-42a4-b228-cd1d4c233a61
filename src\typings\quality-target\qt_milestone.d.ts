export namespace QtMilestone {
  export interface Item {
    id: number;
    draftId: number;
    kpiId: number;
    targetName: string;
    targetNo: string;
    target: string;
    milestone: string;
    startDate: string;
    endDate: string;
    numeratorValue: string;
    denominatorValue: string;
    numeratorName: string;
    denominatorName: string;
    reportResult: string;
    reportScore: number;
    reportAt: date;
    improveType: string;
    improveLink: string;
    improveFileUrl: string;
    improveFileName: string;
    auditorNo: string;
    auditorName: string;
    auditorEmail: string;
    auditAt: date;
    reportFreq: string;
    isKpi: string;
    isPercent: string;
    operator: string;
    isCalc: string;
    aviator: string;
    rejectReason: string;
    reportStatus: string;
    year: string;
    quarter: string;
    dept: string;
    responsibleStaffNo: string;
    responsibleStaffName: string;
    responsibleStaffEmail: string;
    remainingDays: string;
    createNo: string;
    createEmail: string;
    createBy: string;
    createAt: date;
    updateBy: string;
    updateAt: date;
  }

  interface IQueryParams {
    year?: string;
    dept?: string;
    targetName?: string;
    reportStatus?: string;
    reportResult?: string;
    reportFreq?: string;
    responsibleStaffNo?: string;
    onlyShowMine?: string;
  }
}
