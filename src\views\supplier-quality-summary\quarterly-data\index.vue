<!-- eslint-disable prettier/prettier -->
<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :search-col="{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }" :init-param="initParam" @reset="resetForm()">
      <template #tableHeader="{ selectedListIds }">
        <el-button v-auth="'supplier-quality-summary-quarterly-data:edit'" type="primary" @click="openQuarterlyDataModal('编辑')">{{ $t("编辑") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-quarterly-data:export'"
          type="primary"
          @click="downloadFile(selectedListIds as number[])"
          >{{ $t("导出") }}
        </el-button>
      </template>
    </ProTable>
    <QuarterlyDataModal ref="QuarterlyDataModalRef" />
  </div>
</template>

<script setup lang="tsx" name="supplier-quality-summary-quarterly-data">
import {
  getQuarterlyData,
  editQuarterData,
  exportQuarterlyData,
  getSupplierData
} from "@/api/modules/supplier-quality-summary/supplier_quality_summary_quarterly_data";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import QuarterlyDataModal from "@/views/supplier-quality-summary/quarterly-data/components/QuarterlyDataModal.vue";
import { useAdminDict, useDict } from "@/hooks/useDict";
import { QuarterlyData } from "@/typings/supplier-quality-summary/quarterly_data";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { download } from "@/api/modules/common";
import { ref, reactive, computed, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { ElMessage, ElMessageBox } from "element-plus";
import DateRange from "@/views/components/DateRange.vue";

const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

const QuarterlyDataModalRef = ref<InstanceType<typeof QuarterlyDataModal> | null>(null);
let queryParams = reactive<QuarterlyData.IQueryParams>({} as QuarterlyData.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");
const { supplier_quality_summary_quality_level } = useDict("supplier_quality_summary_quality_level");

const columns = reactive<ColumnProps<QuarterlyData.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "quarter",
    label: "季度",
    width: 120,
    search: {
      order: 1,
      render: () => {
        return <DateRange type="quarter" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  {
    prop: "plant",
    label: "工厂",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 2 },
    enum: factory,
    fieldNames: { label: "label", value: "label" }
  },
  { prop: "materialType", label: "物料类别", width: 150, search: { el: "input", order: 3 } },
  { prop: "supplierName", label: "供应商", width: 200, search: { el: "input", order: 4 } },
  { prop: "lar", label: "LAR(%)", width: 100 },
  { prop: "majorQualityIssue", label: "制程重大品质问题发生次数(不良率≥2%)", width: 180 },
  { prop: "minorQualityIssue", label: "制程品质问题发生次数(不良率<2%)", width: 160 },
  { prop: "dppm", label: "制程物料下线率(DPPM)", width: 160 },
  { prop: "clientComplaint", label: "客户投诉次数", width: 120 },
  { prop: "qualityImprovementCooperation", label: "品质改善配合度", width: 140 },
  { prop: "totalScore", label: "品质考核总得分", width: 130 },
  { prop: "qualityGrade", label: "品质等级", width: 100 },
  { prop: "redBlackList", label: "红黑榜", width: 100 },
  { prop: "createBy", label: "创建人", width: 120 },
  { prop: "createAt", label: "创建时间", width: 165 },
  { prop: "updateBy", label: "修改人", width: 120 },
  { prop: "updateAt", label: "修改时间", width: 165 }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  // 处理日期区间参数
  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }

  queryParams = reactive(condition);
  return getQuarterlyData({
    condition,
    pageNum,
    pageSize
  });
};

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
  queryParams = reactive<QuarterlyData.IQueryParams>({} as QuarterlyData.IQueryParams);
};

// 导出
const downloadFile = async (ids?: number[]) => {
  if (!isEmpty(ids)) {
    // 选中数据导出
    ElMessageBox.confirm(t("确认导出数据?"), t("提示"), { type: "warning" }).then(() => download(exportQuarterlyData(ids!)));
  } else {
    // 全部数据导出 - 需要先获取所有数据的IDs
    ElMessage.warning(t("请选择要导出的数据"));
  }
};

const openQuarterlyDataModal = (title: string, row: Partial<QuarterlyData.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    factory,
    supplier_quality_summary_quality_level: supplier_quality_summary_quality_level.value,
    form,
    api: editQuarterData,
    getTableList: proTable.value?.getTableList
  };
  QuarterlyDataModalRef.value?.acceptParams(params as any);
};
</script>

<style scoped lang="scss">
.table-box {
  width: 100%;
  height: 100%;
}
</style>
