export namespace MonthlyData {
  export interface Item {
    id: number;
    plant: string;
    supplierName: string;
    materialType: string;
    iqcInspectionBatchCount: number;
    iqcQualifiedBatchCount: number;
    monthly: string;
    majorQualityIssue: number;
    minorQualityIssue: number;
    dppm: number;
    totalScore: number;
    clientComplaint: number;
    supplierOnsiteArrangement: string;
    qualityImprovementCooperation: string;
    lar: number;
    deleted: number;
    creatEmail: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface IQueryParams {
    startDate: string;
    endDate: string;
    plant: string;
    supplierName: string;
    materialType: string;
  }
}
