import { ReqPage, ResPage } from "@/api/interface/index";
import { NgReview } from "@/typings/ng-review/ng_review";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/ng-review/statistics";

const baseUrl = `${API_PREFIX}/ngReview`;

// 列表
export const getNgReviewList = (params?: ReqPage) => {
  return http.post<ResPage<NgReview.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getNgReviewDetail = (id: number) => {
  return http.post<NgReview.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createNgReview = (data: NgReview.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editNgReview = (data: NgReview.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteNgReview = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportNgReview = (params?: NgReview.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importNgReview = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importNgReviewTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

export const statsOrderQtyOverview = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsOrderQtyOverview`, params);
};

export const statsNgQtyOverview = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsNgQtyOverview`, params);
};
