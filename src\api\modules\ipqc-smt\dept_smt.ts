import { ReqPage, ResPage } from "@/api/interface/index";
import { DeptSmt } from "@/typings/ipqc-smt/dept_smt";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ipqcSMTDept`;

// 列表
export const getDeptList = (params?: ReqPage) => {
  return http.post<ResPage<DeptSmt.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getDeptDetail = (id: number) => {
  return http.post<DeptSmt.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createDept = (data: DeptSmt.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editDept = (data: DeptSmt.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteDept = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportDept = (params?: DeptSmt.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importDept = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importDeptTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
