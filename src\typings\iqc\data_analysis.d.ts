export namespace DataAnalysis {
  // 统计查询参数
  export interface IQueryParams {
    statsType: number; // 0=日,1=周,2=月,3=季度,4=年
    statsDateStart: string;
    statsDateEnd: string;
    checkStatus?: string;
    orderNo?: string;
    checkResult?: string;
    materialName?: string;
    partNo?: string;
    supplierName?: string[];
    urgency?: string;
    checkMethod?: string;
    plant?: string;
    materialType?: string[];
  }

  // 检验批次合格率By物料大类 - 单个物料类型数据
  export interface BatchQualifyRateByMaterialItem {
    dateLabel: string;
    materialType: string;
    totalCount: number;
    passCount: number;
    passRate: number;
    target: string;
  }

  // 检验批次合格率By物料大类 - 按日期分组
  export interface BatchQualifyRateByMaterialGroup {
    dateLabel: string;
    itemList: BatchQualifyRateByMaterialItem[];
  }

  // 检验批次合格率By供应商 - 单个供应商数据
  export interface BatchQualifyRateBySupplierItem {
    dateLabel: string;
    supplierName: string;
    totalCount: number;
    passCount: number;
    passRate: number;
    target: string;
  }

  // 检验批次合格率By供应商 - 按日期分组
  export interface BatchQualifyRateBySupplierGroup {
    dateLabel: string;
    itemList: BatchQualifyRateBySupplierItem[];
  }

  // DPPM By物料类别 - 单个物料类型数据
  export interface DppmByMaterialItem {
    dateLabel: string;
    materialType: string;
    defectCount: number;
    sampleQty: number;
    dppm: number;
    target: string;
  }

  // DPPM By物料类别 - 按日期分组
  export interface DppmByMaterialGroup {
    dateLabel: string;
    itemList: DppmByMaterialItem[];
  }

  // DPPM By供应商 - 单个供应商数据
  export interface DppmBySupplierItem {
    dateLabel: string;
    supplierName: string;
    defectCount: number;
    sampleQty: number;
    dppm: number;
    target: string;
  }

  // DPPM By供应商 - 按日期分组
  export interface DppmBySupplierGroup {
    dateLabel: string;
    itemList: DppmBySupplierItem[];
  }

  // 检验及时率By物料类别 - 单个物料类型数据
  export interface TimelinessRateByMaterialItem {
    dateLabel: string;
    materialType: string;
    timelyCount: number;
    totalCount: number;
    timelyRate: number;
    target: string;
  }

  // 检验及时率By物料类别 - 按日期分组
  export interface TimelinessRateByMaterialGroup {
    dateLabel: string;
    itemList: TimelinessRateByMaterialItem[];
  }

  // 检验及时率By供应商 - 单个供应商数据
  export interface TimelinessRateBySupplierItem {
    dateLabel: string;
    supplierName: string;
    timelyCount: number;
    totalCount: number;
    timelyRate: number;
    target: string;
  }

  // 检验及时率By供应商 - 按日期分组
  export interface TimelinessRateBySupplierGroup {
    dateLabel: string;
    itemList: TimelinessRateBySupplierItem[];
  }

  // 不良物料类别(TOP5)
  export interface BatchFailRateByMaterial {
    materialType: string;
    totalCount: number;
    defectCount: number;
    defectRate: number;
  }

  // 不良供应商(TOP5)
  export interface BatchFailRateBySupplier {
    supplierName: string;
    totalCount: number;
    defectCount: number;
    defectRate: number;
  }
}
