import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityCostParam } from "@/typings/quality-cost/quality_cost_param";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qualityCostParam`;

// 列表
export const getQualityCostParamList = (params?: ReqPage) => {
  return http.post<ResPage<QualityCostParam.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityCostParamByCode = (code: string) => {
  return http.post<QualityCostParam.Item>(`${baseUrl}/getParam/${code}`);
};

// 修改
export const editQualityCostParam = (data: QualityCostParam.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};
