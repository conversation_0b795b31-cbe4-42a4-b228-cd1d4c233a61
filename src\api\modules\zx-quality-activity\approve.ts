import { API_PREFIX, yqjBaseUrl, zxQualityActivityBaseUrl } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}${zxQualityActivityBaseUrl}/approve`;

import http from "@/api";
import { ReqPage, ResPage } from "@/api/interface";

export const getPlanList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/plan/list`, params);
};

export const getActivityList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/activity/list`, params);
};

export const getTaskList = (params: ReqPage) => {
  return http.post<ResPage<any>>(`${baseUrl}/task/list`, params);
};

export const create = (data: any) => {
  return http.post(`${baseUrl}/add`, data);
};

export const update = (data: any) => {
  return http.post(`${baseUrl}/edit`, data);
};

export const submit = (data: any) => {
  return http.post(`${baseUrl}/submit`, data);
};

export const remove = (id: number | number[]) => {
  const ids = Array.isArray(id) ? id.join(",") : id;
  return http.post(`${baseUrl}/del/${ids}`);
};

export const planPass = (data: ZxQualityActivityPlan.Item) => {
  return http.post(`${baseUrl}/plan/pass/${data.id}`);
};

export const planReject = (data: ZxQualityActivityPlan.Item) => {
  return http.post(`${baseUrl}/plan/reject/${data.id}`, { comment: data.comment });
};

export const activityPass = (data: ZxQualityActivityActivity.Item) => {
  return http.post(`${baseUrl}/activity/pass/${data.id}`);
};

export const activityReject = (data: ZxQualityActivityActivity.Item) => {
  return http.post(`${baseUrl}/activity/reject/${data.id}`, { comment: data.comment });
};

export const taskPass = (data: ZxQualityActivityTask.Item) => {
  return http.post(`${baseUrl}/task/pass/${data.id}`);
};

export const taskReject = (data: ZxQualityActivityTask.Item) => {
  return http.post(`${baseUrl}/task/reject/${data.id}`, { comment: data.comment });
};
