import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Group } from "@/typings/group";

import { ADMIN_API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/sys_notice_group`;

export const getGroupList = (params?: ReqPage) => {
  return http.get<ResPage<Group.Item>>(`${baseUrl}/list`, params);
};

export const getGroupDetail = (id: number) => {
  return http.get<Group.Item>(`${baseUrl}/${id}`);
};

export const createGroup = (data: Group.Item) => {
  return http.post(`${baseUrl}`, data);
};

export const editGroup = (data: Group.Item) => {
  return http.put(`${baseUrl}`, data);
};

export const deleteGroup = (data: { ids: number[] }) => {
  return http.delete(`${baseUrl}`, data);
};
export const getGroupMemberList = () => {
  return http.get<Group.IMember[]>(`${baseUrl}/members`);
};

export const getGroupMemberListV2 = () => {
  return http.get<Group.IMember[]>(`${baseUrl}/v2/members`, {}, { loading: false });
};
