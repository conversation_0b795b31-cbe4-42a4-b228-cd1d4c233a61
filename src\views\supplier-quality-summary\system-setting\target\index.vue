<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="{ selectedListIds }">
        <el-button v-auth="'supplier-quality-summary-system-setting-target:add'" type="primary" @click="openTargetModal('新增')">
          {{ $t("新增") }}
        </el-button>
        <el-button v-auth="'supplier-quality-summary-system-setting-target:edit'" type="primary" @click="openTargetModal('编辑')">
          {{ $t("编辑") }}
        </el-button>
        <el-button v-auth="'supplier-quality-summary-system-setting-target:import'" type="primary" @click="batchAdd">
          {{ $t("导入") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-system-setting-target:delete'"
          type="danger"
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("删除") }}
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link @click="openTargetModal('编辑', scope.row)">{{ $t("编辑") }}</el-button>
        <el-button type="danger" link @click="deleteAccount(scope.row)">{{ $t("删除") }}</el-button>
      </template>
    </ProTable>

    <!-- 新增/编辑 -->
    <TargetModal ref="TargetModalRef" />
    <!-- 导入 -->
    <TargetImportModal ref="importModalRef" />
  </div>
</template>

<script setup lang="tsx" name="target">
import {
  getTargetData,
  createTarget,
  editTarget,
  deleteTargetData,
  importTargetData,
  importTargetTpl,
  getTargetDetail
} from "@/api/modules/supplier-quality-summary/supplier_quality_summary_target";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import TargetImportModal from "./components/TargetImportModal.vue";
import TargetModal from "./components/TargetModal.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import { useAdminDict } from "@/hooks/useDict";
import { TargetData } from "@/typings/supplier-quality-summary/target";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { reactive, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { isEmpty, isEmptyObj } from "@/utils/is";
import ElQuarterPicker from "@/views/components/ElQuarterPicker.vue";
import DateRange from "@/views/components/DateRange.vue";

const route = useRoute();
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const TargetModalRef = ref<InstanceType<typeof TargetModal> | null>(null);
const importModalRef = ref<InstanceType<typeof TargetImportModal>>();
let queryParams = reactive<TargetData.IQueryParams>({} as TargetData.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

// 季度搜索
const quarterSearchValue = ref("");

// 表格配置项
const columns: ColumnProps<TargetData.Item>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "plant",
    label: "工厂",
    search: { el: "select", props: { filterable: true }, order: 2 },
    fieldNames: { label: "label", value: "label" },
    enum: factory
  },
  {
    prop: "materialType",
    label: "物料类别",
    search: { el: "input", order: 3 }
  },
  {
    prop: "supplierName",
    label: "供应商",
    search: { el: "input", order: 4 }
  },
  {
    prop: "quarter",
    label: "季度",
    width: 120,
    search: {
      order: 1,
      render: () => {
        return <ElQuarterPicker v-model={quarterSearchValue.value} placeholder={t("请选择季度")} />;
      }
    }
  },
  {
    prop: "majorQualityIssueTarget",
    label: "制程重大品质问题发生次数(不良率≥2%)目标值",
    width: 200
  },
  {
    prop: "clientComplaintTarget",
    label: "客户端投诉次数目标值",
    width: 150
  },
  {
    prop: "larTarge",
    label: "LAR目标值",
    width: 120,
    render: ({ row }) => {
      return `${row.larTarge}%`;
    }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

// 获取表格数据
const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  // 处理季度搜索
  if (quarterSearchValue.value) {
    condition.quarter = quarterSearchValue.value;
  }

  queryParams = reactive(condition);
  return getTargetData({
    condition,
    pageNum,
    pageSize
  });
};

// 重置搜索表单
const resetForm = () => {
  queryParams = reactive<TargetData.IQueryParams>({} as TargetData.IQueryParams);
  quarterSearchValue.value = "";
  proTable.value?.getTableList();
};

// 删除信息
const deleteAccount = async (params: TargetData.Item) => {
  await useHandleData(deleteTargetData, [params.id], `删除【${params.supplierName}】目标数据`);
  proTable.value?.getTableList();
};

// 批量删除信息
const batchDelete = async (ids: number[]) => {
  if (!ids.length) return ElMessage.warning(t("请选择要删除的数据"));
  await useHandleData(deleteTargetData, ids, "删除所选目标数据");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 导入
const batchAdd = () => {
  const params = {
    title: t("导入"),
    importApi: importTargetData,
    tempApi: importTargetTpl,
    getTableList: proTable.value?.getTableList
  };
  importModalRef.value?.acceptParams(params);
};

const getDetail = async () => {
  const id = Number(route?.query?.id) || 0;
  if (!isEmpty(id)) {
    const { success, data } = await getTargetDetail(id);
    if (!success) {
      ElMessage.error(t("记录不存在"));
      return;
    }
    openTargetModal(t("编辑"), data);
  }
};
watch(() => route.query?.id, getDetail, { deep: true, immediate: true });

const openTargetModal = (title: string, row: Partial<TargetData.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }

  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    form,
    api: title === "新增" ? createTarget : title === "编辑" ? editTarget : undefined,
    getTableList: proTable.value?.getTableList
  };
  TargetModalRef.value?.acceptParams(params);
};
</script>
