import { ReqPage, ResPage } from "@/api/interface/index";
import { Line } from "@/typings/line";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/obaSMTLine`;

// 列表
export const getLineList = (params?: ReqPage) => {
  return http.post<ResPage<Line.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getLineDetail = (id: number) => {
  return http.post<Line.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createLine = (data: Line.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editLine = (data: Line.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteLine = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportLine = (params?: Line.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importLine = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importLineTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
