import { API_PREFIX, yqjBaseUrl } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}${yqjBaseUrl}/statistical`;

import http from "@/api";

export type Item = {
  group: string;
  x: string;
  y: number;
};

export const getMaterialBadRate = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/material-bad-rate`, data);
};

export const getSupplierBadRate = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/supplier-bad-rate`, data);
};

// exception-bad-number
export const getExceptionBadNumber = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/exception-bad-number`, data);
};

// reason-bad-number
export const getReasonTypeBadNumber = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/reason-bad-number`, data);
};
// suppler-reply-time
export const getSupplierReplyTime = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/supplier-reply-time`, data);
};
// sqe-reply-time
export const getSqeReplyTime = (data: any) => {
  return http.post<Item[]>(`${baseUrl}/sqe-reply-time`, data);
};
