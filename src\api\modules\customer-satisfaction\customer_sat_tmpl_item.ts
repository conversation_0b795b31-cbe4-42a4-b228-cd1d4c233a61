import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerSatTmplItem } from "@/typings/customer-satisfaction/customer_sat_tmpl_item";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ccsTmplItem`;

// 列表
export const getCustomerSatTmplItemList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerSatTmplItem.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerSatTmplItemDetail = (id: number) => {
  return http.post<CustomerSatTmplItem.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerSatTmplItem = (data: CustomerSatTmplItem.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerSatTmplItem = (data: CustomerSatTmplItem.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerSatTmplItem = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerSatTmplItem = (params?: CustomerSatTmplItem.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerSatTmplItem = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerSatTmplItemTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
