import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityCostRecord } from "@/typings/quality-cost/quality_cost_record";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qualityCostOrder`;

// 列表
export const getQualityCostRecordList = (params?: ReqPage) => {
  return http.post<ResPage<QualityCostRecord.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityCostRecordDetail = (id: number) => {
  return http.get<QualityCostRecord.Item>(`${baseUrl}/${id}`);
};

// 新增
export const createQualityCostRecord = (data: QualityCostRecord.Item) => {
  return http.post(`${baseUrl}`, data);
};

// 修改
export const editQualityCostRecord = (data: QualityCostRecord.Item) => {
  return http.put(`${baseUrl}`, data);
};

// 删除
export const deleteQualityCostRecord = (ids: number[]) => {
  return http.delete(`${baseUrl}`, { ids });
};

// 导出
export const exportQualityCostRecord = (ids?: number[]) => {
  return http.post(`${baseUrl}/export`, { ids });
};

// 导入
export const importQualityCostRecord = (formData: FormData) => {
  return http.post(`${baseUrl}/import`, formData);
};
