export namespace SupplierData {
  export interface Item {
    id: number;
    supplierNo: string;
    supplierName: string;
    isKeySupplier: string;
    description: string;
    deleted: number;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string | null;
  }
  export interface IQueryParams {
    supplierNo: string;
    supplierName: string;
  }
  export interface NewParams {
    supplierNo: string;
    supplierName: string;
    isKeySupplier: string;
    description: string;
  }
  export interface EditParams {
    id: number;
    supplierNo: string;
    supplierName: string;
    isKeySupplier: string;
    description: string;
  }
}
