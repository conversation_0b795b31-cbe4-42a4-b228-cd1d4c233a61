import { ReqPage, ResPage } from "@/api/interface/index";
import { ExternalAuditPlan } from "@/typings/external-audit/external_audit_plan";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ExternalAuditPlan`;
// 列表
export const getExternalAuditPlanList = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditPlan.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getExternalAuditPlanDetail = (id: number) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createExternalAuditPlan = (data: ExternalAuditPlan.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editExternalAuditPlan = (data: ExternalAuditPlan.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteExternalAuditPlan = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportExternalAuditPlan = (params?: ExternalAuditPlan.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importExternalAuditPlan = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importExternalAuditPlanTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 取消
export const cancelAuditPlan = (params: ExternalAuditPlan.Item) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/cancelRequest`, params);
};

// 退回
export const backAuditPlan = (params: { id: number; rejectReason: string }) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/reject`, params);
};

// 延期
export const postponeAuditPlan = (params: ExternalAuditPlan.Item) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/postponeRequest`, params);
};

// 审批通过
export const auditAuditPlan = (id: number) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getAuditPlanApproveList = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditPlan.Item>>(`${baseUrl}/listToBeAudited`, params);
};
//延期列表
export const getListToBePostponed = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditPlan.Item>>(`${baseUrl}/listToBePostponed`, params);
};
//取消列表
export const getListToBeCanceled = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditPlan.Item>>(`${baseUrl}/listToBeCanceled`, params);
};

// 延期通过
export const postpone = (id: number) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/postpone/${id}`);
};

// 取消通过
export const cancel = (id: number) => {
  return http.post<ExternalAuditPlan.Item>(`${baseUrl}/cancel/${id}`);
};
