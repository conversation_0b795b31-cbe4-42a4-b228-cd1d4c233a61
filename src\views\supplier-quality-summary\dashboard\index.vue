<template>
  <div class="dashboard-container">
    <!-- 顶部筛选栏 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="工厂/部门">
          <el-select v-model="filterForm.plant" placeholder="请选择工厂/部门" style="width: 200px" @change="onPlantChange">
            <el-option v-for="item in factoryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料类别">
          <el-select
            v-model="filterForm.materialType"
            placeholder="请选择物料类别"
            style="width: 200px"
            @change="onMaterialTypeChange"
          >
            <el-option v-for="item in materialTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 供应商汇总数据展示 -->
    <div class="summary-section">
      <SummaryCards :summary-data="summaryData" />
    </div>

    <!-- 数据展示区域 -->
    <div class="data-section">
      <!-- 加载状态 -->
      <div v-if="!dataLoaded" class="loading-container" v-loading="true" element-loading-text="正在加载数据...">
        <div style="height: 200px"></div>
      </div>

      <!-- 标签页 -->
      <el-tabs v-else v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="质量数据" name="quality">
          <el-tabs v-model="qualitySubTab" @tab-change="handleQualitySubTabChange">
            <el-tab-pane label="月度" name="monthly">
              <MonthlyDataView
                :filter-form="filterForm"
                :material-type-options="materialTypeOptions"
                :supplier-options="supplierOptions"
              />
            </el-tab-pane>
            <el-tab-pane label="季度" name="quarterly">
              <QuarterlyDataView
                :filter-form="filterForm"
                :material-type-options="materialTypeOptions"
                :supplier-options="supplierOptions"
              />
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="QCC专案" name="qcc">
          <QccDataView :filter-form="filterForm" />
        </el-tab-pane>
        <el-tab-pane label="JQE+黑榜" name="jqe">
          <JqeDataView :filter-form="filterForm" />
        </el-tab-pane>
        <el-tab-pane label="年度审核" name="annual-audit">
          <AnnualAuditView :filter-form="filterForm" />
        </el-tab-pane>
        <el-tab-pane label="专项跟踪审核" name="special-audit">
          <SpecialAuditView :filter-form="filterForm" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="tsx" name="supplier-quality-summary-dashboard">
import { ref, reactive, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { statsSummaryData, getSupplierData } from "@/api/modules/supplier-quality-summary/supplier_quality_summary_dashboard";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";
import { useAdminDict } from "@/hooks/useDict";
import SummaryCards from "./components/SummaryCards.vue";
import MonthlyDataView from "./components/MonthlyDataView.vue";
import QuarterlyDataView from "./components/QuarterlyDataView.vue";
import QccDataView from "./components/QccDataView.vue";
import JqeDataView from "./components/JqeDataView.vue";
import AnnualAuditView from "./components/AnnualAuditView.vue";
import SpecialAuditView from "./components/SpecialAuditView.vue";

const { t } = useI18n();
const { factory } = useAdminDict("factory");

// 筛选表单
const filterForm = reactive({
  plant: "深圳", // 默认深圳
  materialType: ""
});

// 下拉选项
const factoryOptions = computed(() => factory.value.map(item => ({ label: item.label, value: item.value })));
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const supplierOptions = ref<{ label: string; value: string }[]>([]);

// 汇总数据
const summaryData = ref<Dashboard.SummaryData>({
  majorQualityIssueSum: 0,
  clientComplaintSum: 0,
  supplierCount: 0,
  iqcLarAvg: 0,
  minorQualityIssue: 0
});

// 标签页状态
const activeTab = ref("quality");
const qualitySubTab = ref("monthly");

// 数据加载状态
const dataLoaded = ref(false);

// 工厂变更处理
const onPlantChange = async () => {
  await loadMaterialTypes();
};

// 物料类别变更处理
const onMaterialTypeChange = async () => {
  await loadSummaryData();
};

// 标签页变更处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
};

// 质量数据子标签页变更处理
const handleQualitySubTabChange = (tabName: string) => {
  qualitySubTab.value = tabName;
};

// 加载物料类别选项
const loadMaterialTypes = async () => {
  dataLoaded.value = false;
  try {
    const response = await getSupplierData({
      condition: { plant: filterForm.plant },
      pageNum: 1,
      pageSize: 1000
    });

    if (response.data && response.data.list) {
      const materialTypes = [...new Set(response.data.list.map(item => item.materialType))];
      materialTypeOptions.value = materialTypes.map(type => ({ label: type, value: type }));

      // 默认选择第一个物料类别
      if (materialTypeOptions.value.length > 0) {
        filterForm.materialType = materialTypeOptions.value[0].value;
        // 更新供应商选项
        const suppliers = [...new Set(response.data.list.map(item => item.supplierName))];
        supplierOptions.value = suppliers.map(supplier => ({ label: supplier, value: supplier }));
        // 加载汇总数据
        await loadSummaryData();
        // 数据加载完成
        dataLoaded.value = true;
      } else {
        ElMessage.warning("当前工厂没有可用的物料类别数据");
        materialTypeOptions.value = [];
        supplierOptions.value = [];
        filterForm.materialType = "";
        dataLoaded.value = true;
      }
    }
  } catch (error) {
    console.error("加载物料类别失败:", error);
    dataLoaded.value = true;
    //ElMessage.error("加载物料类别失败");
  }
};

// 加载汇总数据
const loadSummaryData = async () => {
  // 确保materialType不为空
  if (!filterForm.materialType) {
    console.warn("materialType为空，跳过加载汇总数据");
    summaryData.value = {
      majorQualityIssueSum: 0,
      clientComplaintSum: 0,
      supplierCount: 0,
      iqcLarAvg: 0,
      minorQualityIssue: 0
    };
    return;
  }

  try {
    const response = await statsSummaryData({
      plant: filterForm.plant,
      materialType: filterForm.materialType
    });

    if (response.data) {
      summaryData.value = response.data;
    }
  } catch (error) {
    console.error("加载汇总数据失败:", error);
    //ElMessage.error("加载汇总数据失败");
  }
};

// 初始化
onMounted(async () => {
  await loadMaterialTypes();
});
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.filter-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-section {
  margin-bottom: 20px;
}

.data-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container {
  position: relative;
  min-height: 200px;
}
</style>
