import { ReqPage, ResPage } from "@/api/interface/index";
import { Param } from "@/typings/internal-audit/param";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/supplierAuditParam`;

// 列表
export const getParamList = (params?: ReqPage) => {
  return http.post<ResPage<Param.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getParamByCode = (code: string) => {
  return http.post<Param.Item>(`${baseUrl}/getParam/${code}`);
};

// 修改
export const editParam = (data: Param.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};
