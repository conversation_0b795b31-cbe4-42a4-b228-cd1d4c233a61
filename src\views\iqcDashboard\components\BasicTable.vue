<template>
  <div class="table-box" ref="tableBoxRef">
    <el-table
      :data="displayData"
      highlight-current-row
      :border="false"
      :show-header="false"
      row-key="orderNo"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column prop="orderNo" label="编号" />
      <el-table-column prop="materialName" label="名称" />
      <el-table-column prop="produceDate" label="创建时间" />
    </el-table>
  </div>
</template>

<script setup lang="ts" name="basicTable">
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";

const props = defineProps({
  data: {
    type: Array as () => any[],
    default: () => []
  }
});

// 表格高度
const tableHeight = ref(300);
const tableBoxRef = ref<HTMLElement | null>(null);

// 用于自动滚动的数据
const displayData = computed(() => {
  if (!props.data || props.data.length === 0) return [];
  // 复制数据以实现无缝滚动效果
  return [...props.data];
});

// 滚动相关变量
let scrollInterval: number | null = null;
let currentScrollTop = 0;
let scrollDirection = "down";
let scrollSpeed = 1; // 滚动速度，可以调整
let maxScrollTop = 0;

// 开始自动滚动
const startAutoScroll = () => {
  if (scrollInterval) return;

  const tableBody = tableBoxRef.value?.querySelector(".el-scrollbar__wrap");
  if (!tableBody) return;

  maxScrollTop = tableBody.scrollHeight - tableBody.clientHeight;

  // 如果内容不足以滚动，则不启动滚动
  if (maxScrollTop <= 0) return;

  scrollInterval = window.setInterval(() => {
    if (scrollDirection === "down") {
      currentScrollTop += scrollSpeed;
      if (currentScrollTop >= maxScrollTop) {
        scrollDirection = "up";
      }
    } else {
      currentScrollTop -= scrollSpeed;
      if (currentScrollTop <= 0) {
        scrollDirection = "down";
      }
    }

    if (tableBody) {
      tableBody.scrollTop = currentScrollTop;
    }
  }, 50);
};

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = null;
  }
};

// 监听数据变化，重新启动滚动
watch(
  () => props.data,
  () => {
    stopAutoScroll();
    // 短暂延迟以确保DOM已更新
    setTimeout(() => {
      startAutoScroll();
    }, 500);
  },
  { deep: true }
);

onMounted(() => {
  // 组件挂载后启动自动滚动
  setTimeout(() => {
    startAutoScroll();
  }, 1000);
});

onBeforeUnmount(() => {
  // 组件卸载前停止滚动
  stopAutoScroll();
});
</script>

<style lang="scss">
.table-box {
  background: transparent;
  border-radius: 8px;
  height: 300px;
  overflow: hidden;
}

/* 自定义表格样式 */
.el-table {
  --el-table-text-color: #ffffff;
  --el-table-header-text-color: #ffffff;
  background: transparent !important;

  /* 确保表格内所有元素背景透明 */
  th,
  tr,
  td {
    background: transparent !important;
    /* 更改为浅色细线，与其他组件边框一致 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* 确保表格展开单元格背景透明 */
  .el-table__expanded-cell {
    background: transparent !important;
  }

  /* 删除表格底部横线 */
  &::before {
    height: 0px;
  }

  /* 确保表头背景透明 */
  thead {
    background: transparent !important;
  }

  /* 确保鼠标悬停行的背景色半透明 */
  tbody tr:hover > td {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* 自定义滚动条样式 */
  .el-scrollbar__bar {
    opacity: 0.3;
  }

  .el-scrollbar__thumb {
    background-color: rgba(255, 255, 255, 0.3);
  }
}
</style>
