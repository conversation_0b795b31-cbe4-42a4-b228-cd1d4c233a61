import { ReqPage, ResPage, StatisticsFour } from "@/api/interface/index";
import { QualityCostOrder } from "@/typings/quality-cost/quality_cost_order";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { QualityCostRecord } from "@/typings/quality-cost/quality_cost_record";
import { Statistics } from "@/typings/quality-cost/statistics";

const baseUrl = `${API_PREFIX}/qualityCostOrder`;

// 列表
export const getQualityCostOrderList = (params?: ReqPage) => {
  return http.post<ResPage<QualityCostOrder.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityCostOrderDetail = (id: number) => {
  return http.post<QualityCostOrder.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityCostOrder = (data: QualityCostOrder.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityCostOrder = (data: QualityCostOrder.Item) => {
  return http.put(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityCostOrder = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQualityCostOrder = (params?: ReqPage) => {
  return http.post(`${baseUrl}/export`, params);
};

export const importQualityCostOrderTpl = () => {
  return http.post(`${baseUrl}/downloadTmpl`, undefined);
};

// 导入
export const importQualityCostOrder = (formData: FormData) => {
  return http.post(`${baseUrl}/import`, formData);
};

// 修改
export const editQualityCostOrderRecord = (data: QualityCostRecord.Item) => {
  return http.post(`${baseUrl}/submitRecord`, data);
};

//统计
export const getQualityCostOrderSummary = (params?: QualityCostOrder.Item) => {
  return http.post<ResPage<QualityCostOrder.Item>>(`${baseUrl}/summary`, params);
};

//导出统计
export const exportQualityCostOrderSummary = (ids?: number[]) => {
  return http.post(`${baseUrl}/export`, { ids });
};

// 四类质量
export const statsFourClassesDistribution = (params?: StatisticsFour) => {
  return http.post<Statistics.FourData[]>(`${baseUrl}/statsFourClassesDistribution`, params);
};

// 品质失败成本
export const statsQualityFailedDistribution = (params?: StatisticsFour) => {
  return http.post<Statistics.costData[]>(`${baseUrl}/statsQualityFailedDistribution`, params);
};

// 质量成本比例/四类质量成本
export const statsQualityCostDistribution = (params?: StatisticsFour) => {
  return http.post<Statistics.FourData[]>(`${baseUrl}/statsQualityCostDistribution`, params);
};

// 工资
export const statsSalaryVsQualityCostRatio = (params?: StatisticsFour) => {
  return http.post<Statistics.wagesData[]>(`${baseUrl}/statsSalaryVsQualityCostRatio`, params);
};

// 质量成本分布-工厂
export const statsQualityCostPieByPlant = (params?: StatisticsFour) => {
  return http.post<Statistics.PieFactoryData[]>(`${baseUrl}/statsQualityCostPieByPlant`, params);
};

// 质量成本分布-成本类型
export const statsQualityCostPieByType = (params?: StatisticsFour) => {
  return http.post<Statistics.PieTypeData>(`${baseUrl}/statsQualityCostPieByType`, params);
};
