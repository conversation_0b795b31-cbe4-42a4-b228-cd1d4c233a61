<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="{ selectedListIds }">
        <el-button
          v-auth="'supplier-quality-summary-system-setting-supplier-information:add'"
          type="primary"
          @click="openSupplierInformationModal('新增')"
        >
          {{ $t("新增") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-system-setting-supplier-information:edit'"
          type="primary"
          @click="openSupplierInformationModal('编辑')"
        >
          {{ $t("编辑") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-system-setting-supplier-information:import'"
          type="primary"
          @click="batchAdd"
        >
          {{ $t("导入") }}
        </el-button>
        <el-button
          v-auth="'supplier-quality-summary-system-setting-supplier-information:delete'"
          type="danger"
          @click="batchDelete(selectedListIds as number[])"
        >
          {{ $t("删除") }}
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link @click="openSupplierInformationModal('编辑', scope.row)">{{ $t("编辑") }}</el-button>
        <el-button type="danger" link @click="deleteAccount(scope.row)">{{ $t("删除") }}</el-button>
      </template>
    </ProTable>

    <!-- 新增/编辑 -->
    <SupplierInformationModal ref="SupplierInformationModalRef" />
    <!-- 导入 -->
    <SupplierInformationImportModal ref="importModalRef" />
  </div>
</template>

<script setup lang="tsx" name="supplier-information">
import {
  getSupplierInformation,
  createSupplierInformation,
  editSupplierInformation,
  deleteSupplierInformation,
  importSupplierInformation,
  importSupplierInformationTpl,
  getSupplierInformationDetail
} from "@/api/modules/supplier-quality-summary/supplier_quality_summary_supplier_information";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import SupplierInformationImportModal from "./components/SupplierInformationImportModal.vue";
import SupplierInformationModal from "./components/SupplierInformationModal.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";
import { useAdminDict } from "@/hooks/useDict";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { reactive, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { isEmpty, isEmptyObj } from "@/utils/is";

const route = useRoute();
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const SupplierInformationModalRef = ref<InstanceType<typeof SupplierInformationModal> | null>(null);
const importModalRef = ref<InstanceType<typeof SupplierInformationImportModal>>();
let queryParams = reactive<SupplierInformation.IQueryParams>({} as SupplierInformation.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

// 表格配置项
const columns: ColumnProps<SupplierInformation.Item>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "plant",
    label: "工厂",
    search: { el: "select", props: { filterable: true } },
    fieldNames: { label: "label", value: "label" },
    enum: factory
  },
  {
    prop: "materialType",
    label: "物料类别",
    search: { el: "input" }
  },
  {
    prop: "supplierName",
    label: "供应商",
    search: { el: "input" }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

// 获取表格数据
const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  queryParams = reactive(condition);
  return getSupplierInformation({
    condition,
    pageNum,
    pageSize
  });
};

// 重置搜索表单
const resetForm = () => {
  queryParams = reactive<SupplierInformation.IQueryParams>({} as SupplierInformation.IQueryParams);
};

// 删除信息
const deleteAccount = async (params: SupplierInformation.Item) => {
  await useHandleData(deleteSupplierInformation, [params.id], `删除【${params.supplierName}】供应商信息`);
  proTable.value?.getTableList();
};

// 批量删除信息
const batchDelete = async (ids: number[]) => {
  if (!ids.length) return ElMessage.warning(t("请选择要删除的数据"));
  await useHandleData(deleteSupplierInformation, ids, "删除所选供应商信息");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 导入
const batchAdd = () => {
  const params = {
    title: t("导入"),
    importApi: importSupplierInformation,
    tempApi: importSupplierInformationTpl,
    getTableList: proTable.value?.getTableList
  };
  importModalRef.value?.acceptParams(params);
};

const getDetail = async () => {
  const id = Number(route?.query?.id) || 0;
  if (!isEmpty(id)) {
    const { success, data } = await getSupplierInformationDetail(id);
    if (!success) {
      ElMessage.error(t("记录不存在"));
      return;
    }
    openSupplierInformationModal(t("编辑"), data);
  }
};
watch(() => route.query?.id, getDetail, { deep: true, immediate: true });

const openSupplierInformationModal = (title: string, row: Partial<SupplierInformation.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }

  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    factory: factory.value,
    form,
    api: title === "新增" ? createSupplierInformation : title === "编辑" ? editSupplierInformation : undefined,
    getTableList: proTable.value?.getTableList
  };
  SupplierInformationModalRef.value?.acceptParams(params);
};
</script>
