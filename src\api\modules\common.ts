import { API_PREFIX } from "@/api/config/servicePort";
import { ElNotification } from "element-plus";
import { getMessage } from "@/utils";
import { ReqPage, ResPage, Upload } from "../interface";
import http from "@/api";
import { Formula } from "@/typings/formula";

const baseUrl = `${API_PREFIX}/common`;

// 下载
export const download = async (downloadUrl: any, isDownloadTemplate?: boolean) => {
  if (!downloadUrl) return;
  try {
    const { success, message } = isDownloadTemplate ? await downloadUrl() : await downloadUrl;
    if (!success) {
      return ElNotification({
        title: getMessage("温馨提示"),
        message: getMessage("下载链接获取失败"),
        type: "warning"
      });
    }
    window.location.href = baseUrl + "/download?fileName=" + encodeURI(message) + "&delete=" + true;
  } catch (error) {
    ElNotification({
      title: getMessage("温馨提示"),
      message: getMessage("下载过程中发生错误"),
      type: "error"
    });
  }
};

//公式描述
export const getFormulaExplanationList = (params?: ReqPage) => {
  return http.post<ResPage<Formula.FormulaExplanation[]>>(`${API_PREFIX}/statsMethod/list`, params);
};

//上传
export const upload = (params: FormData) => {
  return http.post<Upload.ResFileUrl>(`${baseUrl}/upload`, params);
};
//打开OA链接
export const openOAUrl = (params: { appCode: string; requestid: string }) => {
  return http.post<Upload.ResFileUrl>(`${baseUrl}/getOALink`, params);
};
