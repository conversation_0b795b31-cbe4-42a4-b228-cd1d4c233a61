<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'yqj-fa-fx-zz-apply:audit'" type="primary" @click="openModal('审批')">{{ $t("审批") }}</el-button>
      </template>
    </ProTable>
    <Modal ref="modalRef" />
  </div>
</template>

<script setup lang="tsx" name="zx-quality-activity-plan-approve">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";

import Modal from "@/views/zx-quality-activity/plan/approve/components/Modal.vue";

import { <PERSON><PERSON><PERSON><PERSON>, ElMessage } from "element-plus";
import { useDict } from "@/hooks/useDict";

import { isEmpty } from "@/utils/is";

import { ref, reactive } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";

import { getPlanList as getList } from "@/api/modules/zx-quality-activity/approve";

import { computed } from "vue";
import { cloneDeep, isArray, isNil } from "lodash-es";
import useUserStore from "@/stores/modules/user";
import { getDeptAll } from "@/api/modules/dept";
import { Dept } from "@/typings/dept";

import dayjs from "dayjs";
import LinkFile from "@/views/components/LinkFile.vue";
import { getUploadFileName } from "@/utils";

// ProTable 实例
const proTable = ref<ProTableInstance>();

const { check, currentRow } = useCheckSelectId();

const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const modalRef = ref<InstanceType<typeof Modal> | null>(null);

const userStore = useUserStore();

const userInfo = computed(() => userStore.userInfo);

const initParam = reactive({});

const resetCounter = ref(0);
const filterDate = ref([]);

const deptListAll = ref<Dept.Item[]>([]);

const {
  zx_quality_activity_activity_periods,
  zx_quality_activity_activity_type,
  zx_quality_activity_plan_status,
  zx_quality_activity_activity_status
} = useDict(
  "zx_quality_activity_activity_periods",
  "zx_quality_activity_plan_status",
  "zx_quality_activity_activity_type",
  "zx_quality_activity_activity_status"
);

// 表格配置项
const columns = computed<ColumnProps<ZxQualityActivityPlan.Item>[]>(() => [
  { type: "selection", fixed: "left", width: 40 },
  {
    prop: "number",
    label: "计划编号",
    width: 180,
    fixed: "left",
    search: {
      el: "input",
      order: 1
    },
    render: ({ row }) => {
      return (
        <ElButton link type="primary" onClick={() => openModal("审批", row)}>
          {row.number}
        </ElButton>
      );
    }
  },
  {
    prop: "status",
    label: "状态",
    fixed: "left",
    width: 130,
    enum: zx_quality_activity_plan_status.value,
    search: { order: 2, el: "select", props: { filterable: true } }
  },
  {
    prop: "approver",
    label: "审批人",
    width: 180,
    fixed: "left"
  },
  {
    prop: "approverWorkNo",
    label: "审批人工号",
    width: 180,
    fixed: "left"
  },
  {
    prop: "file",
    label: "附件",
    width: 230,
    render: ({ row }) => {
      return <LinkFile url={row.file} name={getUploadFileName(row.file)} />;
    }
  },
  {
    prop: "activityType",
    label: "活动类型",
    width: 180,
    enum: zx_quality_activity_activity_type.value,

    search: {
      order: 4,
      el: "select"
    }
  },

  {
    prop: "periods",
    label: "期数",
    width: 180,
    enum: zx_quality_activity_activity_periods.value,
    search: { order: 5, el: "select", props: { filterable: true } }
  },
  { prop: "activityPurpose", label: "活动目的", width: 200, search: { order: 6, el: "input", props: { filterable: true } } },
  {
    prop: "createBy",
    label: "创建人",
    width: 180,
    search: {
      el: "input",
      order: 3
    }
  },
  {
    prop: "createByWorkNo",
    label: "创建人工号",
    width: 150
  },
  {
    prop: "createAt",
    label: "创建时间",
    width: 180,
    search: {
      el: "date-picker",
      props: {
        type: "daterange"
      },
      order: 7
    },
    render: ({ row }) => {
      return dayjs(row.createAt).format("YYYY-MM-DD HH:mm:ss");
    }
  }
]);

const getDeptListAll = async () => {
  const { data } = await getDeptAll();
  deptListAll.value = data;
};

const getTableList = (params: any) => {
  const { pageNum, pageSize, createAt, ...condition } = params;

  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.applyStartDate = filterDate.value[0];
    condition.applyEndDate = filterDate.value[1];
  }
  if (!isNil(createAt)) {
    if (isArray(createAt)) {
      const [startTime, endTime] = createAt;
      condition.startTime = dayjs(startTime).format("YYYY-MM-DD") + " 00:00:00";
      condition.endTime = dayjs(endTime).format("YYYY-MM-DD") + " 23:59:59";
    } else {
      condition.startTime = createAt;
    }
    delete condition.createAt;
  }
  if (!isNil(condition.viewRange)) {
    if (condition.viewRange === "my") {
      condition.isViewMy = true;
    } else {
      condition.isViewMy = false;
    }
    delete condition.viewRange;
  } else {
    condition.isViewMy = false;
  }
  return getList({
    ...condition,
    pageNum,
    pageSize
  });
};

const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const openModal = async (title: string, row: Partial<ZxQualityActivityPlan.Item> = {}) => {
  check(row?.id);

  const form = cloneDeep(currentRow.value);

  const params = {
    title,
    isView: true,
    disabled: true,
    zx_quality_activity_activity_type: zx_quality_activity_activity_type.value,
    zx_quality_activity_activity_periods: zx_quality_activity_activity_periods.value,
    form,
    // api: submit,
    getTableList: proTable.value?.getTableList
  };
  modalRef.value?.acceptParams(params);
};
</script>
