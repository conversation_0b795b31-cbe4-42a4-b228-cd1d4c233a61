import { ReqPage, ResPage } from "@/api/interface/index";
import { SigmaNode } from "@/typings/6sigma/sigma_node";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/6sigmaNode`;

// 列表
export const getSigmaNodeList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaNode.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSigmaNodeDetail = (id: number) => {
  return http.post<SigmaNode.Item>(`${baseUrl}/get/${id}`);
};

export const getSigmaNodeKeys = (params: { topicType: string; nodeName: string }) => {
  return http.post<SigmaNode.Item>(`${baseUrl}/getKeyList`, params);
};
// 新增
export const createSigmaNode = (data: SigmaNode.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSigmaNode = (data: SigmaNode.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSigmaNode = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSigmaNode = (params?: SigmaNode.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSigmaNode = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSigmaNodeTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
