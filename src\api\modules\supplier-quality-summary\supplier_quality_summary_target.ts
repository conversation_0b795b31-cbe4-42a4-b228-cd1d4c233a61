import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { TargetData } from "@/typings/supplier-quality-summary/target";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/target`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;

// 获取目标数据列表
export const getTargetData = (params?: ReqPage) => {
  return http.post<ResPage<TargetData.Item>>(`${baseUrl}/list`, params);
};

// 获取目标数据详情
export const getTargetDetail = (id: number) => {
  return http.get<Partial<TargetData.Item>>(`${baseUrl}/get/${id}`);
};

// 创建目标数据
export const createTarget = (data: TargetData.NewParams) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改目标数据
export const editTarget = (data: TargetData.EditParams) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 导入
export const importTargetData = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importTargetTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 删除
export const deleteTargetData = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
