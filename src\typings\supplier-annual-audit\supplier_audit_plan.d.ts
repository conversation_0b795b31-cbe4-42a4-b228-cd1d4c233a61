import { SupplierAuditStaff } from "./supplier_audit_staff";

export namespace SupplierAuditPlan {
  export interface Item {
    id: number;
    orderNo: string;
    auditType: string;
    planAuditDate: string;
    actualAuditDate: string;
    supplierType: string;
    supplierName: string;
    materialTeam: string;
    materialName: string;
    supplierAddr: string;
    route: string;
    plant: string;
    auditScope: string;
    auditSite: string;
    auditResult: string;
    auditScore: string;
    refFileName: string;
    refFileUrl: string;
    isReportArchived: string;
    auditorNo: string;
    auditorName: string;
    auditorEmail: string;
    currentAuditor: string;
    scmNo: string;
    scmName: string;
    scmEmail: string;
    sqeNo: string;
    sqeName: string;
    sqeEmail: string;
    purpose: string;
    remarks: string;
    otherAuditStaffs: string;
    otherAuditStaffList: otherAuditStaffs[];
    auditStatus: string;
    rejectReason: string;
    auditStaffList: SupplierAuditStaff.Item[];
    deleted: number;
    createBy: string;
    createNo: number;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface otherAuditStaffs {
    deptName: string;
    staffEmail: string;
    index: number;
    orderId: number;
    deptId: number;
    postName: string;
    staffNo: string;
    staffName: string;
  }
  export interface ExtraRow {
    index: number;
    staffName: string;
    staffNo: string;
    deptName: string;
    postName: string;
    deptId: number;
    staffEmail: string;
  }

  interface IQueryParams {
    startDate: string;
    endDate: string;
    orderNo: string;
    supplierType: string;
    materialName: string;
    auditType: string;
    auditResult: string;
    actualAuditDate: string;
    otherAuditStaffs: string;
  }

  export interface Summary {
    dateLabelList: string[];
    rowDataList: rowData[];
  }

  interface rowData {
    plant: string;
    auditStatus: string;
    cellDataList: cellData[];
  }

  interface cellData {
    dateLabel: string;
    totalQty: number;
  }

  interface PlantInfo {
    count: number;
    firstAppearance: number;
  }

  export type PlantSummary = {
    [plantName: string]: PlantInfo;
  };
}
