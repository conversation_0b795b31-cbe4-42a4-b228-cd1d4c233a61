<script setup lang="tsx" name="yqj-fa-fx-zz-statistics">
import { computed, ref } from "vue";

import SearchHeader from "@/views/components/SearchHeader.vue";
import { useDict } from "@/hooks/useDict";
import { ColumnProps } from "@/components/ProTable/interface";
import { getMaterialBadRate } from "@/api/modules/yqj-fa-fx-zz/statistical";
import MaterialBadRate from "./components/MaterialBadRate.vue";
import SupplierBadRate from "./components/SupplierBadRate.vue";
import { isNil } from "lodash-es";
import dayjs from "dayjs";
import ExceptionBadNumber from "./components/ExceptionBadNumber.vue";
import ReasonTypeBadNumber from "./components/ReasonTypeBadNumber.vue";
import SupplierReplyDate from "./components/SupplierReplyDate.vue";
import SqeReplyDate from "./components/SqeReplyDate.vue";
export type BaseParams = {
  beginTime?: string;
  endTime?: string;
  timeUnit?: string;
};

const filterDate = ref<string[]>([]);

const activeName = ref("bad-rate");

const baseParams = ref<BaseParams>({
  beginTime: dayjs().startOf("month").format("YYYY-MM-DD"),
  endTime: dayjs().endOf("month").format("YYYY-MM-DD")
});

const {
  yqj_fa_fx_zz_material_type,

  yqj_fa_fx_zz_exception_type,
  yqj_fa_fx_zz_apply_status,

  yqj_fa_fx_zz_root_reason_type
} = useDict(
  "quality_person_training_institutions",
  "sys_user_sex",
  "yqj_fa_fx_zz_material_type",
  "yqj_fa_fx_zz_root_reason_type",
  "sys_yes_no",
  "yqj_fa_fx_zz_exception_type",
  "yqj_fa_fx_zz_apply_status",
  "yqj_fa_fx_zz_apply_node"
);

const get = (params: any) => {
  if (isNil(params.createAt)) {
    // 取当月
    const beginTime = dayjs().startOf("month").format("YYYY-MM-DD");
    const endTime = dayjs().endOf("month").format("YYYY-MM-DD");

    params.beginTime = beginTime;
    params.endTime = endTime;
  } else {
    const [beginTime, endTime] = params.createAt;
    params.beginTime = dayjs(beginTime).format("YYYY-MM-DD");
    params.endTime = dayjs(endTime).format("YYYY-MM-DD");
  }
  baseParams.value = params;
  // _getMaterialBadRate(params);
};

const _getMaterialBadRate = async (params: any) => {
  getMaterialBadRate({ timeUnit: "day", ...params });
};

// 表格配置项
const columns = computed<ColumnProps<YqjFaFxZzApply.Item>[]>(() => [
  {
    prop: "applicant",
    label: "申请人",
    width: 120,
    search: {
      order: 1,
      el: "input"
    }
  },
  {
    prop: "exceptionType",
    label: "异常类别",
    width: 120,
    enum: yqj_fa_fx_zz_exception_type.value,

    search: { order: 2, el: "select", props: { filterable: true } }
  },
  {
    prop: "reasonType",
    label: "根本原因类别",
    width: 120,
    enum: yqj_fa_fx_zz_root_reason_type.value,

    search: { order: 3, el: "select", props: { filterable: true } }
  },
  {
    prop: "supplier",
    label: "供应商",
    width: 120,

    search: { order: 4, el: "input", props: { filterable: true } }
  },
  {
    prop: "sampleSender",
    label: "送样人",
    width: 120,

    search: { order: 5, el: "input", props: { filterable: true } }
  },
  {
    prop: "specification",
    label: "规格",
    width: 120,

    search: { order: 5, el: "input", props: { filterable: true } }
  },
  {
    prop: "specification",
    label: "规格",
    width: 120,

    search: { order: 5, el: "input", props: { filterable: true } }
  },
  {
    prop: "materialType",
    label: "物料类别",
    width: 120,
    enum: yqj_fa_fx_zz_material_type.value,

    search: { order: 6, el: "select", props: { filterable: true } }
  },
  {
    prop: "materialNumber",
    label: "物料编号",
    width: 120,

    search: { order: 5, el: "input", props: { filterable: true } }
  },
  {
    prop: "exceptionType",
    label: "异常类别",
    width: 120,
    enum: yqj_fa_fx_zz_exception_type.value,
    search: { order: 3, el: "select", props: { filterable: true } }
  },

  {
    prop: "status",
    label: "状态",
    width: 120,
    enum: yqj_fa_fx_zz_apply_status.value,
    search: { order: 4, el: "select", props: { filterable: true } }
  },

  {
    prop: "createAt",
    label: "申请日期",
    width: 150,

    search: {
      order: 1,
      el: "date-picker",
      props: {
        type: "daterange"
      }
      // render: () => {
      //   return <DateRange type="date" onDateRangeValue={handleSelectedDates}></DateRange>;
      // }
    }
  }
]);
</script>

<template>
  <div class="table-box flex flex-col">
    <SearchHeader :columns="columns" @search="get" />
    <el-tabs v-model="activeName" type="card" class="yqj-fa-fx-zz-statistics-tabs flex-1">
      <el-tab-pane label="不良率" name="bad-rate" class="h-full flex flex-col">
        <MaterialBadRate :base-params="baseParams" />
        <SupplierBadRate :base-params="baseParams" />
      </el-tab-pane>
      <el-tab-pane label="不良数量" name="bad-rate-number" class="h-full flex flex-col">
        <ExceptionBadNumber :base-params="baseParams" />
        <ReasonTypeBadNumber :base-params="baseParams" />
      </el-tab-pane>
      <el-tab-pane label="回复时间" name="reply-time" class="h-full flex flex-col">
        <SupplierReplyDate :base-params="baseParams" />
        <SqeReplyDate :base-params="baseParams" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
