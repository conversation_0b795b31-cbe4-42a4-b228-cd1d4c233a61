import { ReqPage, ResPage } from "@/api/interface/index";
import { SigmaActivity } from "@/typings/6sigma/sigma_activity";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/6sigmaActivity`;

// 列表
export const getSigmaActivityList = (params?: ReqPage) => {
  return http.post<ResPage<SigmaActivity.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSigmaActivityDetail = (id: number) => {
  return http.post<SigmaActivity.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSigmaActivity = (data: SigmaActivity.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSigmaActivity = (data: SigmaActivity.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSigmaActivity = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSigmaActivity = (params?: SigmaActivity.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSigmaActivity = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSigmaActivityTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
