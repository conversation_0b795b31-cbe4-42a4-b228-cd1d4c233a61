<template>
  <el-dialog v-model="visible" :title="$t('选择供应商信息')" width="900px" :destroy-on-close="true">
    <div style="margin-bottom: 16px; padding: 12px; background-color: #f0f9ff; border: 1px solid #bfdbfe; border-radius: 6px">
      <span style="color: #1e40af; font-size: 14px">{{ $t("点击行选择供应商信息，将自动填充工厂、物料类别、供应商字段") }}</span>
    </div>
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :pagination="true"
      :tool-button="false"
      :search-col="{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }"
      @reset="resetForm"
    >
      <template #operation="{ row }">
        <el-button type="primary" size="small" @click="handleRowSelect(row)">{{ $t("选择") }}</el-button>
      </template>
    </ProTable>
    <template #footer>
      <el-button @click="handleCancel">{{ $t("取消") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="SupplierSelector">
import { ref, reactive, computed } from "vue";
import { getSupplierData } from "@/api/modules/supplier-quality-summary/supplier_quality_summary_monthly_data";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";

const { t } = useI18n();

interface Props {
  showPlant?: boolean;
  showMaterialType?: boolean;
  showSupplierName?: boolean;
  customColumns?: ColumnProps<SupplierInformation.Item>[];
}

const props = withDefaults(defineProps<Props>(), {
  showPlant: true,
  showMaterialType: true,
  showSupplierName: true,
  customColumns: () => []
});

interface Emits {
  (e: "select", data: SupplierInformation.Item): void;
  (e: "cancel"): void;
}

const emit = defineEmits<Emits>();

const visible = ref(false);
const proTable = ref<ProTableInstance>();
const initParam = reactive({});

const defaultColumns = computed<ColumnProps<SupplierInformation.Item>[]>(() => {
  const cols: ColumnProps<SupplierInformation.Item>[] = [{ type: "index", label: "序号", width: 80 }];

  if (props.showPlant) {
    cols.push({
      prop: "plant",
      label: "工厂",
      width: 150,
      search: { el: "input", order: 1 }
    });
  }

  if (props.showMaterialType) {
    cols.push({
      prop: "materialType",
      label: "物料类别",
      width: 200,
      search: { el: "input", order: 2 }
    });
  }

  if (props.showSupplierName) {
    cols.push({
      prop: "supplierName",
      label: "供应商",
      search: { el: "input", order: 3 }
    });
  }

  return cols;
});

const columns = computed<ColumnProps<SupplierInformation.Item>[]>(() => {
  let cols: ColumnProps<SupplierInformation.Item>[] = [];

  if (props.customColumns.length > 0) {
    cols = [...props.customColumns];
  } else {
    cols = [...defaultColumns.value];
  }

  cols.push({
    prop: "operation",
    label: "操作",
    width: 100,
    fixed: "right"
  });

  return cols;
});

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;
  return getSupplierData({
    condition,
    pageNum,
    pageSize
  });
};

const resetForm = () => {
  // 重置逻辑
};

const handleRowSelect = (row: SupplierInformation.Item) => {
  emit("select", row);
  visible.value = false;
};

const handleCancel = () => {
  emit("cancel");
  visible.value = false;
};

const open = () => {
  visible.value = true;
};

const close = () => {
  visible.value = false;
};

defineExpose({
  open,
  close
});
</script>

<style scoped>
/* 组件样式 */
</style>
