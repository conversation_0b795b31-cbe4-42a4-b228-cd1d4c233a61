import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerDemandDetail } from "@/typings/customer-demand/customer_demand_detail";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/demandExecute`;

// 列表
export const getCustomerDemandDetailList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerDemandDetail.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerDemandDetailDetail = (id: number) => {
  return http.post<CustomerDemandDetail.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerDemandDetail = (data: CustomerDemandDetail.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerDemandDetail = (data: CustomerDemandDetail.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerDemandDetail = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerDemandDetail = (params?: CustomerDemandDetail.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerDemandDetail = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerDemandDetailTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
// 审批
export const submitToAudit = (id: number) => {
  return http.post<CustomerDemandDetail.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backAudit = (params: { id: number; rejectReason: string }) => {
  return http.post<CustomerDemandDetail.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const audit = (id: number) => {
  return http.post<CustomerDemandDetail.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getApproveList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerDemandDetail.Item>>(`${baseUrl}/listToBeAudited`, params);
};

export const changeExecutor = (data: CustomerDemandDetail.Item) => {
  return http.post<ResPage<CustomerDemandDetail.Item>>(`${baseUrl}/changeExecutor`, data);
};
export const listByDemandNo = (data: { demandNo: string }) => {
  return http.post<ResPage<CustomerDemandDetail.Item>>(`${baseUrl}/listByDemandNo`, data);
};
