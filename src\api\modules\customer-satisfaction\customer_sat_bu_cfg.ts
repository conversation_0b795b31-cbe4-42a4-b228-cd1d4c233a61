import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerSatBuCfg } from "@/typings/customer-satisfaction/customer_sat_bu_cfg";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/customerSatBuCfg`;

// 列表
export const getCustomerSatBuCfgList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerSatBuCfg.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerSatBuCfgDetail = (id: number) => {
  return http.post<CustomerSatBuCfg.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerSatBuCfg = (data: CustomerSatBuCfg.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerSatBuCfg = (data: CustomerSatBuCfg.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerSatBuCfg = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerSatBuCfg = (params?: CustomerSatBuCfg.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerSatBuCfg = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerSatBuCfgTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
