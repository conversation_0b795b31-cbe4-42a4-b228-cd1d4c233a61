<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList">
      <template #tableHeader="{}">
        <el-button v-auth="'iqc-system-setting-target:edit'" type="primary" @click="openTargetModal()">{{
          $t("编辑")
        }}</el-button>
      </template>
    </ProTable>
    <TargetModal ref="TargetModalRef" />
  </div>
</template>

<script setup lang="tsx" name="oba-dip-target">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import TargetModal from "@/views/components/TargetModal.vue";
import { Target } from "@/typings/target";
import { ref, reactive } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";

// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const TargetModalRef = ref<InstanceType<typeof TargetModal> | null>(null);

const columns = reactive<ColumnProps<Target.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { prop: "id", label: "序号", width: 80 },
  { prop: "name", label: "类型" },
  { prop: "desc", label: "说明" }
]);

const getTableList = (params: any) => {
  return {
    data: {
      list: [
        {
          id: 1,
          name: "DPPM",
          desc: "设置各工厂来料检验DPPM目标",
          isPercent: false,
          types: ["annual", "quarterly", "monthly", "weekly"]
        },
        {
          id: 2,
          name: "批次合格率",
          desc: "设置各工厂检验批次合格率目标",
          isPercent: true,
          types: ["annual", "quarterly", "monthly", "weekly"]
        },
        {
          id: 3,
          name: "检验及时率",
          desc: "设置各工厂检验及时率目标",
          isPercent: true,
          types: ["annual", "quarterly", "monthly", "weekly"]
        }
      ]
    }
  };
};

const openTargetModal = async () => {
  check();
  const row = currentRow.value;
  let apiModule;

  if (row.id === 1) {
    apiModule = await import("@/api/modules/iqc/system_setting_dppm_target");
    TargetModalRef.value?.acceptParams({
      isPercent: row.isPercent,
      types: row.types,
      ...apiModule
    });
  } else if (row.id === 2) {
    apiModule = await import("@/api/modules/iqc/system_setting_lar_target");
    TargetModalRef.value?.acceptParams({
      isPercent: row.isPercent,
      types: row.types,
      ...apiModule
    });
  } else if (row.id === 3) {
    apiModule = await import("@/api/modules/iqc/system_setting_timely_target");
    TargetModalRef.value?.acceptParams({
      isPercent: row.isPercent,
      types: row.types,
      ...apiModule
    });
  }
};
</script>
