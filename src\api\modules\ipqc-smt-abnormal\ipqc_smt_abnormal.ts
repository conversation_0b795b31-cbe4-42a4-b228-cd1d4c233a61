import { ReqPage, ResPage } from "@/api/interface/index";
import { IpqcSmtAbnormal } from "@/typings/ipqc-smt-abnormal/ipqc_smt_abnormal";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ipqcSMTAbnormal`;

// 列表
export const getIpqcSmtAbnormalList = (params?: ReqPage) => {
  return http.post<ResPage<IpqcSmtAbnormal.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getIpqcSmtAbnormalDetail = (id: number) => {
  return http.post<IpqcSmtAbnormal.Item>(`${baseUrl}/get/${id}`);
};

// 提醒
export const remind = (id: number) => {
  return http.post<IpqcSmtAbnormal.Item>(`${baseUrl}/remind/${id}`);
};

// 我的任务
export const listByCQE = (params?: ReqPage) => {
  return http.post<ResPage<IpqcSmtAbnormal.Item>>(`${baseUrl}/listByCQE`, params);
};

// 填写改善措施
export const modifyByCQE = (data: IpqcSmtAbnormal.Item) => {
  return http.post(`${baseUrl}/modifyByCQE`, data);
};

// 新增
export const createIpqcSmtAbnormal = (data: IpqcSmtAbnormal.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editIpqcSmtAbnormal = (data: IpqcSmtAbnormal.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 提交
export const submit = (data: IpqcSmtAbnormal.Item) => {
  return http.post(`${baseUrl}/submit`, data);
};

// 删除
export const deleteIpqcSmtAbnormal = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportIpqcSmtAbnormal = (params?: IpqcSmtAbnormal.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importIpqcSmtAbnormal = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importIpqcSmtAbnormalTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
