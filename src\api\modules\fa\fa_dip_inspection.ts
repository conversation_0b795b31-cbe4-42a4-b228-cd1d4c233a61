import { ReqPage, ResPage } from "@/api/interface/index";
import { FaDipInspection } from "@/typings/fa/fa_dip_inspection";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/faDIPInspection`;

// 列表
export const getFaDipInspectionList = (params?: ReqPage) => {
  return http.post<ResPage<FaDipInspection.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getFaDipInspectionDetail = (id: number) => {
  return http.post<FaDipInspection.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createFaDipInspection = (data: FaDipInspection.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editFaDipInspection = (data: FaDipInspection.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteFaDipInspection = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportFaDipInspection = (params?: FaDipInspection.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importFaDipInspection = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importFaDipInspectionTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
