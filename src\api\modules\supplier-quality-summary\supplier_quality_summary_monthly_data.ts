import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { MonthlyData } from "@/typings/supplier-quality-summary/monthly_data";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/monthly`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;
// 获取月度质量数据列表
export const getMonthlyData = (params?: ReqPage) => {
  return http.post<ResPage<MonthlyData.Item>>(`${baseUrl}/list`, params);
};

// 获取质量数据详情
export const getMonthlyDataDetail = (id: number) => {
  return http.get<Partial<MonthlyData.Item>>(`${baseUrl}/get/${id}`);
};

// 修改质量数据
export const editMonthData = (data: MonthlyData.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 创建质量数据
export const createMonthData = (data: MonthlyData.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 删除
export const deleteMonthlyData = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportMonthlyData = (ids: number[]) => {
  return http.post(`${baseUrl}/exportListData`, { ids });
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
