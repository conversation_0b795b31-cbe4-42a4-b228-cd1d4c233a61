<template>
  <el-dialog v-model="visible" width="600px" :destroy-on-close="true" :title="`${t(title)}`">
    <el-form
      ref="formRef"
      label-width="120px"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      :show-message="isZh"
    >
      <el-form-item :label="$t('供应商编号')" prop="supplierNo">
        <el-input v-model="form.supplierNo" :placeholder="$t('请填写')" :readonly="isView" clearable />
      </el-form-item>

      <el-form-item :label="$t('供应商名称')" prop="supplierName">
        <el-input v-model="form.supplierName" :placeholder="$t('请填写')" :readonly="isView" clearable />
      </el-form-item>

      <el-form-item :label="$t('?关键供应商')" prop="isKeySupplier">
        <el-switch
          v-model="form.isKeySupplier"
          :disabled="isView"
          active-text="是"
          inactive-text="否"
          active-value="是"
          inactive-value="否"
        />
      </el-form-item>

      <el-form-item :label="$t('说明')" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" :placeholder="$t('请填写')" :readonly="isView" clearable />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="SupplierInformationModal">
import { ref, reactive, toRefs } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import { SupplierData } from "@/typings/iqc/supplier_information";

const { isZh } = useLanguageCode();
const { t } = useI18n();

interface SupplierInformationModalProps {
  title: string;
  isView: boolean;
  form: Partial<SupplierData.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const visible = ref(false);
const formRef = ref<FormInstance>();

const state = reactive<SupplierInformationModalProps>({
  title: "",
  isView: false,
  form: {},
  api: undefined,
  getTableList: undefined
});

const { form, title, isView } = toRefs(state);

// 表单验证规则
const rules = reactive({
  supplierNo: [{ required: true, message: t("请填写供应商编号"), trigger: "blur" }],
  supplierName: [{ required: true, message: t("请填写供应商名称"), trigger: "blur" }],
  isKeySupplier: [{ required: true, message: t("请选择是否为关键供应商"), trigger: "change" }]
});

// 设置弹窗显示状态
const setVisible = (val: boolean) => {
  visible.value = val;
};

// 接收父组件参数
const acceptParams = (params: SupplierInformationModalProps) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!(form.value);
      ElMessage.success({ message: t(`保存成功`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
