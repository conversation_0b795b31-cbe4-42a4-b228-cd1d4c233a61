import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerSatTmpl } from "@/typings/customer-satisfaction/customer_sat_tmpl";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ccsTmpl`;

// 列表
export const getCustomerSatTmplList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerSatTmpl.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerSatTmplDetail = (id: number) => {
  return http.post<CustomerSatTmpl.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerSatTmpl = (data: CustomerSatTmpl.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerSatTmpl = (data: CustomerSatTmpl.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerSatTmpl = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerSatTmpl = (params?: CustomerSatTmpl.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerSatTmpl = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerSatTmplTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
