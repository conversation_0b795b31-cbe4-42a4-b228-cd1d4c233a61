import { ReqPage, ResPage } from "@/api/interface/index";
import { App } from "@/typings/app";
import http from "@/api";
import { ADMIN_API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/sys_application`;

// 列表
export const getAppList = (params?: ReqPage) => {
  return http.get<ResPage<App.Item>>(`${baseUrl}/list`, params);
};

export const getAppAllList = (params?: Partial<App.Item>) => {
  return http.get<App.Item[]>(`${baseUrl}/all`, params);
};

// 详情
export const getAppDetail = (params: Partial<App.Item>) => {
  return http.get<App.Item>(`${ADMIN_API_PREFIX}/sys_application_public`, params);
};

// 新增
export const createApp = (data: App.Item) => {
  return http.post(`${baseUrl}`, data);
};

// 修改
export const editApp = (data: App.Item) => {
  return http.put(`${baseUrl}`, data);
};

// 删除
export const deleteApp = (ids: number[]) => {
  return http.delete(`${baseUrl}`, { ids });
};
