<template>
  <div class="table-box bg-white py-2 flex-1 flex flex-col">
    <div class="w-full text-center text-1xl font-bold">DPPM</div>
    <!-- 明确设置了高度的Echarts容器 -->
    <div class="grid w-full gap-6 mb-[30px]" style="height: 350px">
      <ECharts :option="option" autoresize />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { DataAnalysis } from "@/typings/iqc/data_analysis";
import { ECOption } from "@/components/ECharts/config";

const colorMap = {
  targetLineColor: "#EC808D",
  axisColor: "#639FD2",
  colors: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"]
};

type Props = {
  chartData: DataAnalysis.DppmByMaterialGroup[];
  searchParams: DataAnalysis.IQueryParams;
};

const props = defineProps<Props>();

// 将API返回的数据转换为echarts需要的格式
const seriesData = computed(() => {
  if (!props.chartData || props.chartData.length === 0) return [];

  // 获取所有物料类型
  const allMaterialTypes = new Set<string>();
  props.chartData.forEach(item => {
    item.itemList.forEach(material => {
      allMaterialTypes.add(material.materialType);
    });
  });

  // 为每个物料类型创建一个series
  const materialSeries = Array.from(allMaterialTypes).map((materialType, index) => {
    // 收集该物料类型在每个时间点的数据
    const data = props.chartData.map(timeItem => {
      const materialData = timeItem.itemList.find(item => item.materialType === materialType);
      return materialData ? materialData.dppm : 0;
    });

    return {
      name: materialType,
      type: "bar",
      emphasis: {
        focus: "series"
      },
      itemStyle: {
        color: colorMap.colors[index % colorMap.colors.length]
      },
      data
    };
  });

  // 添加目标线
  const targetData = props.chartData.map(timeItem => {
    const firstItem = timeItem.itemList[0];
    return firstItem ? parseFloat(firstItem.target) : 10;
  });

  const targetSeries = {
    name: "Target",
    type: "line",
    smooth: true,
    lineStyle: {
      color: colorMap.targetLineColor,
      type: "dashed"
    },
    itemStyle: {
      color: colorMap.targetLineColor
    },
    yAxisIndex: 1,
    data: targetData
  };

  return [...materialSeries, targetSeries];
});

// 提取时间数据作为X轴
const xData = computed(() => {
  if (!props.chartData) return [];
  return props.chartData.map(item => item.dateLabel);
});

const option = computed<ECOption>(() => {
  return {
    backgroundColor: "#fff",
    tooltip: {
      showContent: true,
      trigger: "axis",
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          if (param.seriesName === "Target") {
            result += `${param.seriesName}: ${param.value}<br/>`;
          } else {
            result += `${param.seriesName}: ${param.value} DPPM<br/>`;
          }
        });
        return result;
      }
    },
    textStyle: {
      color: "#c0c3cd",
      fontSize: 14
    },
    legend: {
      bottom: 0,
      type: "scroll"
    },
    xAxis: {
      nameTextStyle: {
        color: "#c0c3cd",
        padding: [0, 0, -10, 0],
        fontSize: 14
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      data: xData.value,
      type: "category"
    },
    yAxis: [
      {
        type: "value",
        name: "DPPM",
        position: "left",
        axisLabel: {
          formatter: (value: number) => value.toString(),
          textStyle: {
            color: colorMap.axisColor
          }
        }
      },
      {
        type: "value",
        name: "Target",
        position: "right",
        axisLabel: {
          formatter: (value: number) => value.toString(),
          textStyle: {
            color: colorMap.targetLineColor
          }
        }
      }
    ],
    series: seriesData.value
  } as ECOption;
});
</script>
