<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">品质等级分布</h3>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="QualityGradeChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";

interface Props {
  chartData: Dashboard.GradeDistributionData[];
}

const props = defineProps<Props>();

// 检查是否有数据
const hasData = computed(() => {
  return props.chartData && props.chartData.length > 0;
});

// 图表配置
const chartOption = computed<ECOption>(() => {
  if (!hasData.value) return {};

  return {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left",
      data: props.chartData.map(item => item.name)
    },
    series: [
      {
        name: "品质等级",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["60%", "50%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "18",
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: props.chartData.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: getGradeColor(item.name, index)
          }
        }))
      }
    ]
  };
});

// 根据等级获取颜色
const getGradeColor = (grade: string, index: number) => {
  const colors = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4"];

  // 根据等级名称设置特定颜色
  switch (grade) {
    case "A":
    case "优秀":
      return "#91cc75"; // 绿色
    case "B":
    case "良好":
      return "#5470c6"; // 蓝色
    case "C":
    case "一般":
      return "#fac858"; // 黄色
    case "D":
    case "差":
      return "#ee6666"; // 红色
    default:
      return colors[index % colors.length];
  }
};
</script>

<style scoped>
.echarts-container {
  width: 100%;
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.echarts-content {
  height: 350px;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
