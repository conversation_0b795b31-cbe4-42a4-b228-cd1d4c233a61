import { ReqPage, ResPage } from "@/api/interface/index";
import { QtMilestone } from "@/typings/quality-target/qt_milestone";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/quality-target/statistics";

const baseUrl = `${API_PREFIX}/qtMilestone`;

// 列表
export const getQtMilestoneList = (params?: ReqPage) => {
  return http.post<ResPage<QtMilestone.Item>>(`${baseUrl}/list`, params, { loading: false });
};

// 待填写列表
export const listToBeSubmitted = (params?: ReqPage) => {
  return http.post<ResPage<QtMilestone.Item>>(`${baseUrl}/listToBeSubmitted`, params);
};

// 待审批列表
export const listToBeAudited = (params?: ReqPage) => {
  return http.post<ResPage<QtMilestone.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 详情
export const getQtMilestoneDetail = (id: number) => {
  return http.post<QtMilestone.Item>(`${baseUrl}/get/${id}`);
};

// 取消
export const cancelQtMilestone = (ids: number[]) => {
  return http.post(`${baseUrl}/cancel`, { ids });
};

//催促汇报
export const reminder = (ids: number[]) => {
  return http.post(`${baseUrl}/reminder`, { ids });
};

// 新增
export const createQtMilestone = (data: QtMilestone.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQtMilestone = (data: QtMilestone.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

//自动计算结果
export const autoCalc = (data: QtMilestone.Item) => {
  return http.post<number>(`${baseUrl}/autoCalc`, data);
};

// 删除
export const deleteQtMilestone = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQtMilestone = (params?: QtMilestone.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQtMilestone = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQtMilestoneTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
// 审批
export const submitToAudit = (data: QtMilestone.Item) => {
  return http.post<QtMilestone.Item>(`${baseUrl}/submit`, data);
};

// 审批列表
export const getApproveList = (params?: ReqPage) => {
  return http.post<ResPage<QtMilestone.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 退回
export const dismiss = (params: { id: number; rejectReason: string }) => {
  return http.post<QtMilestone.Item>(`${baseUrl}/dismiss`, params);
};

// 审批通过
export const pass = (data: QtMilestone.Item) => {
  return http.post(`${baseUrl}/pass`, data);
};
// 统计
export const stats = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.Home>(`${API_PREFIX}/qtStats/stats`, params);
};

// 导出
export const exportList = (params?: QtMilestone.IQueryParams) => {
  return http.post(`${API_PREFIX}/qtStats/exportList`, params);
};

//任务列表
export const statsTaskList = (params?: ReqPage) => {
  return http.post<ResPage<QtMilestone.Item>>(`${API_PREFIX}/qtStats/statsTaskList`, params);
};
//统计目标KPI分布
export const getStatsKpiRadar = (params: Statistics.IQueryRadarParams) => {
  return http.post<Statistics.kpiRadarDataRespList>(`${API_PREFIX}/qtStats/statsKpiRadar`, params);
};
//统计目标KPI趋势图
export const getStatsKpiTrend = (params: Statistics.IQueryParams) => {
  return http.post<Statistics.TargetData>(`${API_PREFIX}/qtStats/statsKpiTrend`, params);
};

//首页图表
export const statsHome = (params: Statistics.IQueryHomeParams) => {
  return http.post<Statistics.Home>(`${API_PREFIX}/qtStatsHome/statsHome`, params);
};
//首页table
export const getStatsHomeList = (params?: ReqPage) => {
  return http.post<ResPage<Statistics.Home>>(`${API_PREFIX}/qtStatsHome/statsHomeTaskList`, params);
};
