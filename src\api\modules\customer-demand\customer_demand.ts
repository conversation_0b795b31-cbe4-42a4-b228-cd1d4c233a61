import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerDemand } from "@/typings/customer-demand/customer_demand";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/demandManagement`;

// 列表
export const getCustomerDemandList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerDemand.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerDemandDetail = (id: number) => {
  return http.post<CustomerDemand.Item>(`${baseUrl}/get/${id}`);
};

// 左侧树
export const statsPerformanceTree = (data: { search?: string }) => {
  return http.post<CustomerDemand.TreeList>(`${baseUrl}/statsPerformanceTree`, data);
};

export const pullCustomer = () => {
  return http.post<CustomerDemand.CustomerItem[]>(`${baseUrl}/pullCustomer`);
};

// 新增
export const createCustomerDemand = (data: CustomerDemand.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerDemand = (data: CustomerDemand.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerDemand = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerDemand = (params?: CustomerDemand.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerDemand = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerDemandTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 提交
export const submitToAudit = (id: number) => {
  return http.post<CustomerDemand.Item>(`${baseUrl}/submitToAudit/${id}`);
};
