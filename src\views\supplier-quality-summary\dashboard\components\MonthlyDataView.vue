<template>
  <div class="monthly-data-view">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="月份">
          <el-date-picker
            v-model="searchForm.monthly"
            type="month"
            placeholder="请选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 200px"
            @change="handleMonthChange"
          />
        </el-form-item>
        <el-form-item label="供应商">
          <el-select
            v-model="searchForm.supplierName"
            placeholder="请选择供应商"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
            style="width: 400px"
            @change="handleSupplierChange"
          >
            <template #header>
              <div class="select-header">
                <el-button type="primary" size="small" @click="selectAllSuppliers">全选</el-button>
                <el-button size="small" @click="clearAllSuppliers">取消全选</el-button>
              </div>
            </template>
            <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-container">
      <!-- 供应商总得分趋势 -->
      <div class="chart-card">
        <TotalScoreChart :chart-data="chartData.totalScoreTrend" />
      </div>

      <!-- LAR趋势 -->
      <div class="chart-card">
        <LarChart :chart-data="chartData.larTrend" />
      </div>

      <!-- 重大质量问题趋势 -->
      <div class="chart-card">
        <MajorQualityIssueChart :chart-data="chartData.majorQualityIssueTrend" />
      </div>

      <!-- 客户投诉趋势 -->
      <div class="chart-card">
        <ClientComplaintChart :chart-data="chartData.clientComplaintTrend" />
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="table-section">
      <div class="table-header">
        <h3>月度数据详情</h3>
        <el-button type="primary" @click="exportData">导出</el-button>
      </div>
      <el-table :data="tableData" border stripe max-height="400" style="width: 100%">
        <el-table-column prop="monthly" label="月份" width="120" />
        <el-table-column prop="plant" label="工厂" width="120" />
        <el-table-column prop="supplier" label="供应商" width="150" />
        <el-table-column prop="materialType" label="物料类别" width="120" />
        <el-table-column prop="iqcLar" label="IQC LAR" width="100" />
        <el-table-column prop="majorQualityIssue" label="重大质量问题" width="120" />
        <el-table-column prop="clientComplaint" label="客户投诉" width="100" />
        <el-table-column prop="totalScore" label="总得分" width="100" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="tsx" name="MonthlyDataView">
import { ref, reactive, onMounted, watch } from "vue";

import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { statsMonthlyData } from "@/api/modules/supplier-quality-summary/supplier_quality_summary_dashboard";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";
import TotalScoreChart from "./charts/TotalScoreChart.vue";
import LarChart from "./charts/LarChart.vue";
import MajorQualityIssueChart from "./charts/MajorQualityIssueChart.vue";
import ClientComplaintChart from "./charts/ClientComplaintChart.vue";

interface Props {
  filterForm: {
    plant: string;
    materialType: string;
  };
  materialTypeOptions: { label: string; value: string }[];
  supplierOptions: { label: string; value: string }[];
}

const props = defineProps<Props>();

// 搜索表单
const searchForm = reactive({
  monthly: dayjs().format("YYYY-MM"), // 默认当前月份
  supplierName: [] as string[]
});

// 图表数据
const chartData = ref<Dashboard.MonthlyData>({
  totalScoreTrend: [],
  larTrend: [],
  majorQualityIssueTrend: [],
  clientComplaintTrend: [],
  list: []
});

// 表格数据
const tableData = ref<Dashboard.MonthlyData["list"]>([]);

// 月份变更处理
const handleMonthChange = () => {
  if (props.filterForm.materialType) {
    loadData();
  }
};

// 供应商变更处理
const handleSupplierChange = () => {
  if (props.filterForm.materialType) {
    loadData();
  }
};

// 全选供应商
const selectAllSuppliers = () => {
  searchForm.supplierName = props.supplierOptions.map(item => item.value);
  if (props.filterForm.materialType) {
    loadData();
  }
};

// 取消全选供应商
const clearAllSuppliers = () => {
  searchForm.supplierName = [];
  if (props.filterForm.materialType) {
    loadData();
  }
};

// 加载数据
const loadData = async () => {
  // 确保materialType不为空
  if (!props.filterForm.materialType) {
    console.warn("materialType为空，跳过加载月度数据");
    return;
  }

  try {
    const params: Dashboard.IQueryParamsMonthlyData = {
      monthly: searchForm.monthly,
      plant: props.filterForm.plant,
      materialType: props.filterForm.materialType,
      supplierName: searchForm.supplierName
    };

    const response = await statsMonthlyData(params);

    if (response.data) {
      // API返回的数据结构直接包含各个字段
      chartData.value = response.data;
      // 直接使用API返回的表格数据
      tableData.value = response.data.list || [];
    }
  } catch (error) {
    console.error("加载月度数据失败:", error);
    //ElMessage.error("加载月度数据失败");
  }
};

// 导出数据
const exportData = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning("暂无数据可导出");
    return;
  }

  const headers = ["月份", "工厂", "供应商", "物料类别", "IQC LAR", "重大质量问题", "客户投诉", "总得分"];
  const csvContent = [
    headers.join(","),
    ...tableData.value.map(row =>
      [
        row.monthly,
        row.plant,
        row.supplier,
        row.materialType,
        row.iqcLar,
        row.majorQualityIssue,
        row.clientComplaint,
        row.totalScore
      ].join(",")
    )
  ].join("\n");

  const blob = new Blob(["\uFEFF" + csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = `月度数据_${dayjs().format("YYYY-MM-DD")}.csv`;
  link.click();
};

// 监听筛选条件变化
watch(
  () => [props.filterForm.plant, props.filterForm.materialType],
  () => {
    // 只有当materialType不为空时才加载数据
    if (props.filterForm.materialType) {
      loadData();
    }
  },
  { deep: true }
);

// 初始化
onMounted(() => {
  // 默认选择所有供应商（如果有）
  if (props.supplierOptions.length > 0) {
    searchForm.supplierName = props.supplierOptions.map(item => item.value);
  }
  // 只有当materialType不为空时才加载数据
  if (props.filterForm.materialType) {
    loadData();
  }
});
</script>

<style scoped>
.monthly-data-view {
  padding: 20px 0;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #333;
}

.select-header {
  padding: 1px 12px;
  display: flex;
  gap: 1px;
  justify-content: space-between;
}
</style>
