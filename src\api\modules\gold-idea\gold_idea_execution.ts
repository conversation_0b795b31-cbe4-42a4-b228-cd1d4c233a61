import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { ProposalList } from "@/typings/gold-idea/gold_idea_proposal";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/proposal`;

// 执行提案列表
export const getMyExecutionList = (params?: ReqPage) => {
  return http.post<ResPage<ProposalList.Item>>(`${baseUrl}/executionList`, params);
};

// 执行编辑
export const editExecution = (data: ProposalList.excutionDetail) => {
  return http.post(`${baseUrl}/editExecution`, data);
};

// 执行提交
export const submitExecution = (data: ProposalList.excutionDetail) => {
  return http.post(`${baseUrl}/submitExecution`, data);
};
