import { ReqPage, ResPage } from "@/api/interface/index";
import { WpGroup } from "@/typings/quality-work-plan/wp_group";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/quality-work-plan/statistics";

const baseUrl = `${API_PREFIX}/wpGroup`;

// 列表
export const getWpList = (params?: ReqPage) => {
  return http.post<ResPage<WpGroup.Item>>(`${baseUrl}/list`, params);
};
//草稿
export const getWpListDraft = (params?: ReqPage) => {
  return http.post<ResPage<WpGroup.Item>>(`${baseUrl}/listDraft`, params);
};

// 详情
export const getWpDetail = (id: number) => {
  return http.post<WpGroup.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createWp = (data: WpGroup.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editWp = (data: WpGroup.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteWp = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 删除
export const deleteCommonWp = (planNos: string[]) => {
  return http.post(`${API_PREFIX}/wpCommon/del`, { planNos });
};
// 提交
export const submitWp = (ids: number[]) => {
  return http.post(`${baseUrl}/submit`, { ids });
};

// 取消
export const cancelWp = (planNos: string[]) => {
  return http.post(`${API_PREFIX}/wpCommon/cancel`, { planNos });
};

// 导出
export const exportWp = (params?: WpGroup.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importWp = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importWpTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
//集团任务达成率
export const statsGroupPassedRatio = (data: Statistics.IQueryParams) => {
  return http.post<Statistics.statsGroupPassedRatio>(`${API_PREFIX}/wpStats/statsGroupPassedRatio`, data);
};
//集团任务状态分布
export const statsGroupReportStatus = (data: Statistics.IQueryParams) => {
  return http.post<Statistics.statsGroupReportStatus>(`${API_PREFIX}/wpStats/statsGroupReportStatus`, data);
};
// 图表集团任务列表
export const statsGroupList = (params?: ReqPage) => {
  return http.post<ResPage<WpGroup.Item>>(`${API_PREFIX}/wpStats/statsGroupList`, params);
};
