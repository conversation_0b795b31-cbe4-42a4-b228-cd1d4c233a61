import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Dict } from "@/typings/dict";
import { ADMIN_API_PREFIX, API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/sysDictType`;
const dictDataBaseUrl = `${API_PREFIX}/sysDictData`;

export const getDictTypeList = (params: ReqPage) => {
  return http.post<ResPage<Dict.ITypeItem>>(`${baseUrl}/list`, params);
};

export const getDictDataList = (type: string) => {
  return http.post<Dict.Detail>(`${baseUrl}/get/${type}`);
};

export const delDictType = (data: DelParams) => {
  return http.post(`${baseUrl}/type`, data);
};

export const createDict = (data: Dict.Detail) => {
  return http.post(`${baseUrl}/create`, data);
};

export const editDict = (data: Dict.Detail) => {
  return http.post(`${baseUrl}/modify`, data);
};

export const editDictData = (data: Dict.Detail) => {
  return http.post(`${baseUrl}/batchSaveDictData`, data);
};

export const delDictData = (data: DelParams) => {
  return http.post(`${dictDataBaseUrl}/data`, data);
};

export const getImportTemplate = () => {
  return http.post(`${baseUrl}/exportTmpl`, {});
};
export const getAdminDictDataList = (params: Partial<{ type: string; status: string }>) => {
  return http.get<Dict.IDataItem[]>(`${ADMIN_API_PREFIX}/sys_dict/data/list`, params);
};
