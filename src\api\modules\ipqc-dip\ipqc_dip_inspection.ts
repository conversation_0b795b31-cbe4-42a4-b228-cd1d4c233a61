import { ReqPage, ResPage } from "@/api/interface/index";
import { IpqcDipInspection } from "@/typings/ipqc-dip/ipqc_dip_inspection";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/ipqc-dip/statistics";

const baseUrl = `${API_PREFIX}/ipqcDIPInspection`;

// 列表
export const getIpqcDipInspectionList = (params?: ReqPage) => {
  return http.post<ResPage<IpqcDipInspection.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getIpqcDipInspectionDetail = (id: number) => {
  return http.post<IpqcDipInspection.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createIpqcDipInspection = (data: IpqcDipInspection.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editIpqcDipInspection = (data: IpqcDipInspection.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteIpqcDipInspection = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportIpqcDipInspection = (params?: IpqcDipInspection.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importIpqcDipInspection = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importIpqcDipInspectionTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

//各楼层巡检问题点统计
export const statsBarByFloor = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsBarByFloor`, params);
};

//问题点分类BY类型
export const statsBarByCategory = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsBarByCategory`, params);
};

// 问题点分布BY责任人
export const statsPieByDept = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsPieByDept`, params);
};

//关闭率趋势
export const statsClosedRatioTrend = (params?: Statistics.IQueryParamsReportStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsClosedRatioTrend`, params);
};

//问题点趋势图
export const statsFindingTrend = (params?: Statistics.IQueryParamsReportStats) => {
  return http.post<Statistics.stats>(`${baseUrl}/statsFindingTrend`, params);
};

//问题点分布
export const statsFindingOverview = (params?: Statistics.IQueryParamsStats) => {
  return http.post<Statistics.statsFindingOverview>(`${baseUrl}/statsFindingOverview`, params);
};
