import { ReqPage, ResPage } from "@/api/interface/index";
import { QtDraftLine } from "@/typings/quality-target/qt_draft_line";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qtDraftLine`;

// 列表
export const getQtDraftLineList = (params?: ReqPage) => {
  return http.post<ResPage<QtDraftLine.Item>>(`${baseUrl}/list`, params, { loading: false });
};

// 详情
export const getQtDraftLineDetail = (id: number) => {
  return http.post<QtDraftLine.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQtDraftLine = (data: QtDraftLine.Item) => {
  return http.post<QtDraftLine.Item>(`${baseUrl}/create`, data);
};

// 修改
export const editQtDraftLine = (data: QtDraftLine.Item) => {
  return http.post<QtDraftLine.Item>(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQtDraftLine = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQtDraftLine = (params?: QtDraftLine.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQtDraftLine = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQtDraftLineTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
