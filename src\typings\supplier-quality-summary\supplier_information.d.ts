export namespace SupplierInformation {
  export interface Item {
    id: number;
    plant: string;
    supplierName: string;
    materialType: string;
    deleted: number;
    creatEmail: string;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface IQueryParams {
    plant: string;
    supplierName: string;
    materialType: string | string[];
  }
  export interface NewParams {
    plant: string;
    supplierName: string;
    materialType: string;
  }
  export interface EditParams {
    id: number;
    plant: string;
    supplierName: string;
    materialType: string;
  }
}
