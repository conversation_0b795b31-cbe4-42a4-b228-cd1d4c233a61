<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :search-col="{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }"
      :init-param="initParam"
      @reset="resetForm()"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="{ selectedList }">
        <el-button v-auth="'iqc-inspection-data:import'" type="primary" @click="batchAdd">
          {{ $t("导入") }}
        </el-button>
        <el-button v-auth="'iqc-inspection-data:delete'" type="danger" @click="batchDelete(selectedList)">
          {{ $t("删除") }}
        </el-button>
      </template>
    </ProTable>
    <!-- 导入 -->
    <InspectionDataImportModal ref="importModalRef" />
  </div>
</template>

<script setup lang="tsx" name="iqc-inspection-data">
import {
  getInspectionList,
  importInspectionData,
  importInspectionDataTpl,
  deleteInspectionData
} from "@/api/modules/iqc/inspection_data";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { InspectionData } from "@/typings/iqc/inspection_data";
import InspectionDataImportModal from "./components/InspectionDataImportModal.vue";
import { useAdminDict, useDict } from "@/hooks/useDict";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { download } from "@/api/modules/common";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import DateRange from "@/views/components/DateRange.vue";

const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

const importModalRef = ref<InstanceType<typeof InspectionDataImportModal>>();
let queryParams = reactive<InspectionData.IQueryParams>({} as InspectionData.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

// 字典项配置
const { iqc_material_level, iqc_inspection_type, iqc_inspection_result, iqc_inspection_status } = useDict(
  "iqc_material_level",
  "iqc_inspection_type",
  "iqc_inspection_result",
  "iqc_inspection_status"
);

const columns = reactive<ColumnProps<InspectionData.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "plant",
    label: "工厂/部门",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 1 },
    enum: factory,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "checkStatus",
    label: "检验状态",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 2 },
    enum: iqc_inspection_status,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "materialType",
    label: "物料大类",
    width: 120,
    search: { el: "input", order: 3 }
  },
  {
    prop: "orderNo",
    label: "质检单号",
    width: 150,
    search: { el: "input", order: 4 }
  },
  {
    prop: "checkResult",
    label: "检验结果",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 5 },
    enum: iqc_inspection_result,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "materialName",
    label: "物料名称",
    width: 150,
    search: { el: "input", order: 6 }
  },
  {
    prop: "partNo",
    label: "物料编码",
    width: 150,
    search: { el: "input", order: 7 }
  },
  {
    prop: "supplierName",
    label: "供应商名称",
    width: 150,
    search: { el: "input", order: 8 }
  },
  {
    prop: "checkMethod",
    label: "检验方式",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 9 },
    enum: iqc_inspection_type,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "urgency",
    label: "物料紧急度",
    width: 120,
    search: { el: "select", props: { filterable: true }, order: 10 },
    enum: iqc_material_level,
    fieldNames: { label: "label", value: "label" }
  },
  {
    prop: "checkAt",
    label: "检验日期",
    width: 180,
    search: {
      order: 11,
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  { prop: "pkey", label: "PKEY", width: 120 },
  { prop: "produceDate", label: "生产日期", width: 120 },
  { prop: "supplierNo", label: "供应商编码", width: 150 },
  { prop: "unit", label: "单位", width: 80 },
  { prop: "receivedQty", label: "收货数量", width: 120 },
  { prop: "inboundOrderNo", label: "返工订单号", width: 150 },
  { prop: "description", label: "合格入库单号", width: 150 },
  { prop: "yearWeek", label: "物料周次", width: 120 },
  { prop: "responseBelong", label: "责任归属", width: 120 },
  { prop: "receivedAt", label: "收货时间", width: 165 },
  { prop: "mrb", label: "MRB结论", width: 120 },
  { prop: "ngType", label: "不良类型", width: 120 },
  { prop: "criVal", label: "CR", width: 80 },
  { prop: "majVal", label: "MA", width: 80 },
  { prop: "minVal", label: "MI", width: 80 },
  { prop: "sqe", label: "SQE", width: 120 },
  { prop: "plmOrderNo", label: "PLM单号", width: 150 },
  { prop: "documentNo", label: "责任归属", width: 120 },
  { prop: "sampleQty", label: "申请单号", width: 120 },
  { prop: "editTime", label: "编辑时间", width: 165 },
  { prop: "diffValue", label: "差值", width: 80 },
  { prop: "dayNum", label: "天数", width: 80 }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;

  // 处理日期区间参数
  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }

  queryParams = reactive(condition);
  return getInspectionList({
    condition,
    pageNum,
    pageSize
  });
};

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};

const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};
// 导入
const batchAdd = () => {
  const params = {
    title: t("导入"),
    importApi: importInspectionData,
    tempApi: importInspectionDataTpl,
    getTableList: proTable.value?.getTableList
  };
  importModalRef.value?.acceptParams(params);
};
// 批量删除信息
const batchDelete = async (selectedRows: InspectionData.Item[]) => {
  if (!selectedRows.length) return ElMessage.warning(t("请选择要删除的数据"));

  // 从选中的行数据中提取pkey字段
  const pkeys = selectedRows.map(row => row.pkey).filter(pkey => pkey !== undefined && pkey !== null);

  if (!pkeys.length) return ElMessage.warning(t("选中的数据中没有有效的PKEY"));

  await useHandleData(deleteInspectionData, pkeys, t("删除"));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
</script>
