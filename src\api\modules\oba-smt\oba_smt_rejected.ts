import { ReqPage, ResPage } from "@/api/interface/index";
import { ObaSmtRejected } from "@/typings/oba-smt/oba_smt_rejected";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/obaSMTRejected`;

// 列表
export const getObaSmtRejectedList = (params?: ReqPage) => {
  return http.post<ResPage<ObaSmtRejected.Item>>(`${baseUrl}/list`, params);
};

//操作记录
export const getChangeLogList = (params?: ReqPage) => {
  return http.post<ResPage<ObaSmtRejected.Log>>(`${baseUrl}/getChangeLogList`, params);
};

// 详情
export const getObaSmtRejectedDetail = (id: number) => {
  return http.post<ObaSmtRejected.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createObaSmtRejected = (data: ObaSmtRejected.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editObaSmtRejected = (data: ObaSmtRejected.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteObaSmtRejected = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportObaSmtRejected = (params?: ObaSmtRejected.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importObaSmtRejected = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importObaSmtRejectedTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

export const statsObaSmtPareto = (params?: ObaSmtRejected.IQueryParamsStats) => {
  return http.post<ObaSmtRejected.statsObaSmtPareto>(`${baseUrl}/statsObaSmtPareto`, params);
};
