export namespace Dashboard {
  // 汇总数据类型定义
  export interface SummaryData {
    majorQualityIssueSum: number;
    clientComplaintSum: number;
    supplierCount: number;
    iqcLarAvg: number;
    minorQualityIssue: number;
  }
  export interface IQueryParamsSummary {
    plant: string;
    materialType: string;
  }
  // 图表数据类型定义
  interface TotalScoreData {
    supplier: string;
    score: number;
  }
  interface ClientComplaintData {
    supplier: string;
    clientComplaint: number;
    target: number;
  }
  interface LarData {
    supplier: string;
    lar: number;
    target: number;
  }
  interface GradeDistributionData {
    name: string;
    value: number;
  }
  interface MajorQualityIssueData {
    supplier: string;
    actual: number;
    target: number;
  }

  // 月度数据类型定义
  interface MonthlyTableData {
    monthly: string;
    plant: string;
    supplier: string;
    materialType: string;
    iqcLar: string;
    totalScore: string;
    dppm: string;
    clientComplaint: string;
    iqcInspectionBatchCount: string;
    iqcQualifiedBatchCount: string;
    majorQualityIssue: string;
  }
  export interface MonthlyData {
    totalScoreTrend: TotalScoreData[];
    larTrend: LarData[];
    majorQualityIssueTrend: MajorQualityIssueData[];
    clientComplaintTrend: ClientComplaintData[];
    list: MonthlyTableData[];
  }
  export interface IQueryParamsMonthlyData {
    monthly: string;
    plant: string;
    materialType: string;
    supplierName: string[];
  }
  // 季度数据类型定义
  interface QuarterlyTableData {
    quarter: string;
    plant: string;
    supplier: string;
    materialType: string;
    dppm: string;
    iqcLar: number;
    majorQualityIssue: number;
    clientComplaint: number;
    totalScore: number;
    qualityGrade: string;
    redBlackList: string;
    iqcInspectionBatchCount: string;
    iqcQualifiedBatchCount: string;
  }
  export interface QuarterlyData {
    totalScoreTrend: TotalScoreData[];
    larTrend: LarData[];
    majorQualityIssueTrend: MajorQualityIssueData[];
    clientComplaintTrend: ClientComplaintData[];
    qualityGradeDistribution: GradeDistributionData[];
    list: QuarterlyTableData[];
  }
  export interface IQueryParamsQuarterlyData {
    quarter: string;
    plant: string;
    materialType: string;
    supplierName: string[];
  }

  // QCC数据类型定义
  interface QccPlotData {
    supplier: string;
    count: number;
  }
  interface QccTableData {
    caseNo: string;
    plant: string;
    supplier: string;
    materialType: string;
    projectContent: string;
    revenueSummary: string;
    status: string;
    planCompleteDate: string;
    reportName: string;
    reportUrl: string;
    progressUpdate: string;
    progressUpdateList: any;
  }
  export interface QccData {
    qccProjectCount: QccPlotData[];
    list: QccTableData[];
  }
  export interface IQueryParamsQccData {
    statsDateStart: string;
    statsDateEnd: string;
    plant: string;
    materialType: string;
  }
  // JQE数据类型定义
  interface JqeInspectionData {
    supplier: string;
    data: inspectionDetail[];
  }
  interface inspectionDetail {
    week: string;
    dppm: number;
  }
  interface JqeTableData {
    monthly: string;
    plant: string;
    supplier: string;
    materialType: string;
    isJqeAssigned: number;
    wasBlacklistedLastQuarter: number;
    createBy: string;
    createAt: string;
    updateBy: string;
    updateAt: string;
  }
  export interface JqeData {
    reinspectionDefectRateTrend: JqeInspectionData[];
    oqcDefectRateTrend: JqeInspectionData[];
    list: JqeTableData[];
  }
  export interface IQueryParamsJqeData {
    statsMonthStart: string;
    statsMonthEnd: string;
    plant: string;
    materialType: string;
  }

  // 供应商审核数据类型定义
  export interface SupplierAuditTableData {
    plant: string;
    supplierName: string;
    materialTeam: string;
    planAuditDate: string;
    actualAuditDate: string;
    auditResult: string;
    auditScore: string;
    reportFileName: string;
    reportFileUrl: string;
  }
  export interface SupplierAnnualAuditData {
    scoreDistribution: TotalScoreData[];
    planList: SupplierAuditTableData[];
  }
  export interface IQueryParamsSupplierAuditData {
    year: string;
    plant: string;
    materialType: string;
  }
}
