import { ReqPage, ResPage } from "@/api/interface/index";
import { QtDraft } from "@/typings/quality-target/qt_draft";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { QtDraftLine } from "@/typings/quality-target/qt_draft_line";

const baseUrl = `${API_PREFIX}/qtDraft`;

// 列表
export const getQtDraftList = (params?: ReqPage) => {
  return http.post<ResPage<QtDraft.Item>>(`${baseUrl}/list`, params, { loading: false });
};

// 详情
export const getQtDraftDetail = (id: number) => {
  return http.post<QtDraft.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQtDraft = (data: QtDraft.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

export const batchAdd = (data: QtDraftLine.Item) => {
  return http.post(`${baseUrl}/batchAdd`, data);
};

// 修改
export const editQtDraft = (data: QtDraft.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQtDraft = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQtDraft = (params?: QtDraft.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQtDraft = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQtDraftTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 提交
export const submitQtDraft = (id: number) => {
  return http.post(`${baseUrl}/submit/${id}`);
};
