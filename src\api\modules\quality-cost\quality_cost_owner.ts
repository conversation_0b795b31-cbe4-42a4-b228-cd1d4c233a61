import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityCostOwner } from "@/typings/quality-cost/quality_cost_owner";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qualityCostOwner`;

// 列表
export const getQualityCostOwnerList = (params?: ReqPage) => {
  return http.post<ResPage<QualityCostOwner.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityCostOwnerDetail = (id: number) => {
  return http.post<QualityCostOwner.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityCostOwner = (data: QualityCostOwner.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityCostOwner = (data: QualityCostOwner.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityCostOwner = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};
