<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">LAR分析</h3>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="LarChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";

interface Props {
  chartData: Dashboard.LarData[];
}

const props = defineProps<Props>();

// 检查是否有数据
const hasData = computed(() => {
  return props.chartData && props.chartData.length > 0;
});

// 图表配置
const chartOption = computed<ECOption>(() => {
  if (!hasData.value) return {};

  const suppliers = props.chartData.map(item => item.supplier);
  const larValues = props.chartData.map(item => item.lar);
  const targetValues = props.chartData.map(item => item.target);

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross"
      }
    },
    legend: {
      data: ["LAR实际值", "目标值"]
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: suppliers,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: "value",
      name: "LAR (%)"
    },
    series: [
      {
        name: "LAR实际值",
        type: "bar",
        data: larValues,
        itemStyle: {
          color: "#91cc75"
        }
      },
      {
        name: "目标值",
        type: "line",
        data: targetValues,
        itemStyle: {
          color: "#fac858"
        },
        lineStyle: {
          color: "#fac858"
        }
      }
    ]
  };
});
</script>

<style scoped>
.echarts-container {
  width: 100%;
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.echarts-content {
  height: 350px;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
