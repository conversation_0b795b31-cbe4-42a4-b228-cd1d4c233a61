import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { SupplierAuditReport } from "@/typings/supplier-special-audit/supplier_audit_report";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/supplier-special-audit/statistics";

const baseUrl = `${API_PREFIX}/supplierSpecialAudit`;

// 列表
export const getSupplierAuditReportList = (params?: ReqPage) => {
  return http.post<ResPage<SupplierAuditReport.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSupplierAuditReportDetail = (id: number) => {
  return http.post<SupplierAuditReport.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSupplierAuditReport = (data: SupplierAuditReport.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSupplierAuditReport = (data: SupplierAuditReport.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSupplierAuditReport = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSupplierAuditReport = (params?: SupplierAuditReport.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSupplierAuditReport = (formData: FormData) => {
  return http.post(`${baseUrl}/import`, formData);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<SupplierAuditReport.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backSupplierAuditReport = (params: { id: number; rejectReason: string }) => {
  return http.post<SupplierAuditReport.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const auditSupplierAuditReport = (id: number) => {
  return http.post<SupplierAuditReport.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getSupplierAuditReportApproveList = (params?: ReqPage) => {
  return http.post<ResPage<SupplierAuditReport.Item>>(`${baseUrl}/listToBeAudited`, params);
};
