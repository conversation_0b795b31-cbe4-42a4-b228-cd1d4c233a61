import { ReqPage, ResPage } from "@/api/interface/index";
import { ExternalAuditQuestion } from "@/typings/external-audit/external_audit_question";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ExternalAuditQuestion`;

// 列表
export const getExternalAuditQuestionList = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditQuestion.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getExternalAuditQuestionDetail = (id: number) => {
  return http.post<ExternalAuditQuestion.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createExternalAuditQuestion = (data: ExternalAuditQuestion.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editExternalAuditQuestion = (data: ExternalAuditQuestion.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteExternalAuditQuestion = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportExternalAuditQuestion = (params?: ExternalAuditQuestion.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importExternalAuditQuestion = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importExternalAuditQuestionTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<ExternalAuditQuestion.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backAuditQuestion = (params: { id: number; rejectReason: string }) => {
  return http.post<ExternalAuditQuestion.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const auditAuditQuestion = (id: number) => {
  return http.post<ExternalAuditQuestion.Item>(`${baseUrl}/audit/${id}`);
};

// 审批列表
export const getAuditQuestionApproveList = (params?: ReqPage) => {
  return http.post<ResPage<ExternalAuditQuestion.Item>>(`${baseUrl}/listToBeAudited`, params);
};
