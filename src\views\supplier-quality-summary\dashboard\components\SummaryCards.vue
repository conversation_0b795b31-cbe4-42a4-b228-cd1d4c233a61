<template>
  <div class="summary-cards">
    <div class="card-item blue">
      <div class="card-content">
        <div class="card-icon">
          <el-icon><DataBoard /></el-icon>
        </div>
        <div class="card-info">
          <div class="card-title">供应商数量</div>
          <div class="card-value">{{ summaryData.supplierCount }}</div>
        </div>
      </div>
    </div>

    <div class="card-item green">
      <div class="card-content">
        <div class="card-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="card-info">
          <div class="card-title">IQC LAR</div>
          <div class="card-value">{{ summaryData.iqcLarAvg }}</div>
        </div>
      </div>
    </div>

    <div class="card-item orange">
      <div class="card-content">
        <div class="card-icon">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="card-info">
          <div class="card-title">制程品质问题发生次数</div>
          <div class="card-value">{{ summaryData.majorQualityIssueSum }}</div>
        </div>
      </div>
    </div>

    <div class="card-item red">
      <div class="card-content">
        <div class="card-icon">
          <el-icon><ChatDotRound /></el-icon>
        </div>
        <div class="card-info">
          <div class="card-title">制程重大问题发生次数</div>
          <div class="card-value">{{ summaryData.clientComplaintSum }}</div>
        </div>
      </div>
    </div>

    <div class="card-item pink">
      <div class="card-content">
        <div class="card-icon">
          <el-icon><Files /></el-icon>
        </div>
        <div class="card-info">
          <div class="card-title">客户投诉次数</div>
          <div class="card-value">{{ summaryData.minorQualityIssue }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";
import { DataBoard, TrendCharts, Warning, ChatDotRound, Files } from "@element-plus/icons-vue";

interface Props {
  summaryData: Dashboard.SummaryData;
}

defineProps<Props>();
</script>

<style scoped>
.summary-cards {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.card-item {
  flex: 1;
  min-width: 200px;
  border-radius: 12px;
  padding: 20px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.card-item:hover {
  transform: translateY(-2px);
}

.card-item.blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-item.green {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-item.orange {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-item.red {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-item.pink {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-icon {
  font-size: 40px;
  opacity: 0.8;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
}

@media (max-width: 768px) {
  .summary-cards {
    flex-direction: column;
  }

  .card-item {
    min-width: auto;
  }
}
</style>
