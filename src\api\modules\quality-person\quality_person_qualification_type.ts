import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityPersonQualificationType } from "@/typings/quality-person/quality_person_qualification_type";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qualityPersonQualityType`;

// 列表
export const getQualityPersonQualificationTypeList = (params?: ReqPage) => {
  return http.post<ResPage<QualityPersonQualificationType.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityPersonQualificationTypeDetail = (id: number) => {
  return http.post<QualityPersonQualificationType.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityPersonQualificationType = (data: QualityPersonQualificationType.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityPersonQualificationType = (data: QualityPersonQualificationType.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityPersonQualificationType = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQualityPersonQualificationType = (params?: QualityPersonQualificationType.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQualityPersonQualificationType = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQualityPersonQualificationTypeTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
