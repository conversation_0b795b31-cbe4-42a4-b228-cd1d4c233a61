import { ReqPage, ResPage } from "@/api/interface/index";
import { WpMilestone } from "@/typings/quality-work-plan/wp_milestone";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/wpMilestone`;

// 列表
export const getWpMilestoneList = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/list`, params);
};
//汇报记录
export const listByPlanId = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/listByPlanId`, params);
};

//根据groupPlanId列出汇报记录
export const listByGroupPlanId = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/listByGroupPlanId`, params);
};

//根据deptPlanId列出汇报记录
export const listByDeptPlanId = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/listByDeptPlanId`, params);
};

//根据staffPlanId列出汇报记录
export const listByStaffPlanId = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/listByStaffPlanId`, params);
};

// 待填写列表
export const listToBeSubmitted = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/listToBeSubmitted`, params);
};

// 待审批列表
export const listToBeAudited = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 详情
export const getWpMilestoneDetail = (id: number) => {
  return http.post<WpMilestone.Item>(`${baseUrl}/get/${id}`);
};

//催促汇报
export const reminder = (ids: number[]) => {
  return http.post(`${API_PREFIX}/wpCommon/reminder`, { planNos: ids });
};

// 新增
export const createWpMilestone = (data: WpMilestone.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editWpMilestone = (data: WpMilestone.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteWpMilestone = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

//批量通过
export const batchPass = (ids: number[]) => {
  return http.post(`${baseUrl}/batchPass`, { ids });
};

// 导出
export const exportWpMilestone = (params?: WpMilestone.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importWpMilestone = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importWpMilestoneTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
// 审批
export const submitToAudit = (data: WpMilestone.Item) => {
  return http.post<WpMilestone.Item>(`${baseUrl}/submit`, data);
};

// 审批列表
export const getApproveList = (params?: ReqPage) => {
  return http.post<ResPage<WpMilestone.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 退回
export const dismiss = (params: { id: number; rejectReason: string }) => {
  return http.post<WpMilestone.Item>(`${baseUrl}/dismiss`, params);
};

// 审批通过
export const pass = (data: WpMilestone.Item) => {
  return http.post(`${baseUrl}/pass`, data);
};
