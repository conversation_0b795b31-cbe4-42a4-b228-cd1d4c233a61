<template>
  <el-dialog
    v-model="visible"
    width="900px"
    draggable
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :title="title ? t(title) : ''"
    modal-class="draggable-modal"
  >
    <el-form ref="formRef" label-position="top" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <!-- 供应商信息 -->
      <div class="section-title">{{ $t("供应商信息") }}</div>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="$t('月份')" prop="monthly">
            <el-date-picker
              v-if="!isView"
              v-model="form.monthly"
              type="month"
              :placeholder="$t('请选择月份')"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 100%"
            />
            <span v-else>{{ form.monthly || "-" }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('工厂')" prop="plant">
            <el-input v-if="!isView" v-model="form.plant" :placeholder="$t('请选择工厂')" readonly @click="openSelectionDialog" />
            <span v-else>{{ form.plant || "-" }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('物料类别')" prop="materialType">
            <el-input v-if="!isView" v-model="form.materialType" :placeholder="$t('请选择物料类别')" readonly />
            <span v-else>{{ form.materialType || "-" }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="$t('供应商')" prop="supplierName">
            <el-input v-if="!isView" v-model="form.supplierName" :placeholder="$t('请选择供应商')" readonly />
            <span v-else>{{ form.supplierName || "-" }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="$t('是否安排JQE')" prop="isJqeAssigned">
            <el-radio-group v-if="!isView" v-model="form.isJqeAssigned">
              <el-radio v-for="({ label, value }, index) of IsJqeArranged" :key="index" :label="$t(label)" :value="value">
                {{ $t(label) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('上季度是否黑榜供应商')" prop="wasBlacklistedLastQuarter">
            <el-radio-group v-if="!isView" v-model="form.wasBlacklistedLastQuarter">
              <el-radio
                v-for="({ label, value }, index) of IsLastQuarterSupplierRequired"
                :key="index"
                :label="$t(label)"
                :value="value"
              >
                {{ $t(label) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 进度更新 -->
      <div class="section-title">{{ $t("进度更新") }}</div>
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane :label="$t('OQC抽检不良率(DPPM)')" name="oqc">
          <ProgressUpdateTable
            ref="oqcProgressUpdateTableRef"
            :progress-list="form.oqcInspectionDefectRateList"
            :is-view="isView"
            :field-name="'inspectionDefectRate'"
            :field-label="'终检不良率(DPPM)'"
            @update:progress-list="handleOqcListUpdate"
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('复检不良率(DPPM)')" name="reinspection">
          <ProgressUpdateTable
            ref="reinspectionProgressUpdateTableRef"
            :progress-list="form.reinspectionDefectRateList"
            :is-view="isView"
            :field-name="'reinspectionDefectRate'"
            :field-label="'复检不良率(DPPM)'"
            @update:progress-list="handleReinspectionListUpdate"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-if="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>

    <SupplierSelector ref="supplierSelectorRef" @select="handleSupplierSelect" />
  </el-dialog>
</template>

<script setup lang="tsx" name="JqeDataModal">
import { JqeData } from "@/typings/supplier-quality-summary/jqe_data";
import { IsJqeArranged, IsLastQuarterSupplierRequired } from "@/enums/statusSupplierQualitySummary";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs } from "vue";
import { Dict } from "@/typings/dict";
import { useI18n } from "vue-i18n";
import { isEmpty } from "@/utils/is";
import SupplierSelector from "../../components/SupplierSelector/index.vue";
import ProgressUpdateTable from "@/views/supplier-quality-summary/jqe-data/components/ProgressUpdateTable.vue";

const { t } = useI18n();

interface IState {
  title: string;
  isView: boolean;
  factory: Dict.IDataItem[];
  form: Partial<JqeData.Item>;
  api?: (params: any) => Promise<any>;
  updateApi?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const visible = ref(false);
const formRef = ref<FormInstance>();
const supplierSelectorRef = ref<InstanceType<typeof SupplierSelector> | null>(null);
const oqcProgressUpdateTableRef = ref<InstanceType<typeof ProgressUpdateTable> | null>(null);
const reinspectionProgressUpdateTableRef = ref<InstanceType<typeof ProgressUpdateTable> | null>(null);
const activeTab = ref("oqc");

const state = reactive<IState>({
  title: "",
  isView: false,
  factory: [],
  form: {
    oqcInspectionDefectRateList: [] as JqeData.InspectionDefectRateDetail[],
    reinspectionDefectRateList: [] as JqeData.ReInspectionDefectRateDetail[]
  },
  api: undefined,
  updateApi: undefined,
  getTableList: undefined
});

const rules = reactive({
  monthly: [{ required: true, message: t("请选择月份"), trigger: "change" }],
  plant: [{ required: true, message: t("请选择工厂"), trigger: "blur" }],
  isJqeArranged: [{ required: true, message: t("请选择是否安排JQE"), trigger: "change" }],
  isLastQuarterSupplierRequired: [{ required: true, message: t("请选择上季度是否黑榜供应商"), trigger: "change" }]
});

const { form, title, isView } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

// 打开选择弹窗
const openSelectionDialog = () => {
  supplierSelectorRef.value?.open();
};

// 处理供应商选择
const handleSupplierSelect = (data: SupplierInformation.Item) => {
  form.value.plant = data.plant;
  form.value.materialType = data.materialType;
  form.value.supplierName = data.supplierName;
};

// 处理OQC抽检不良率列表更新
const handleOqcListUpdate = (value: any[]) => {
  form.value.oqcInspectionDefectRateList = value.map(item => ({
    inspectionDefectRate: item.inspectionDefectRate || "",
    week: item.week || ""
  })) as JqeData.InspectionDefectRateDetail[];
};

// 处理复检不良率列表更新
const handleReinspectionListUpdate = (value: any[]) => {
  form.value.reinspectionDefectRateList = value.map(item => ({
    reinspectionDefectRate: item.reinspectionDefectRate || "",
    week: item.week || ""
  })) as JqeData.ReInspectionDefectRateDetail[];
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (!valid) return;
    try {
      // Ensure all table data is included in the form
      const submitData = {
        ...form.value,
        oqcInspectionDefectRateList: form.value.oqcInspectionDefectRateList || [],
        reinspectionDefectRateList: form.value.reinspectionDefectRateList || []
      };

      await state.api?.(submitData);
      ElMessage.success(t(title.value) + t("成功！"));
      state.getTableList?.();
      setVisible(false);
    } catch (error) {
      console.error(error);
    }
  });
};

// 接收参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);

  if (params.form) {
    // 初始化进度更新列表
    if (params.form.oqcInspectionDefectRateList && Array.isArray(params.form.oqcInspectionDefectRateList)) {
      form.value.oqcInspectionDefectRateList = [...params.form.oqcInspectionDefectRateList];
    } else {
      form.value.oqcInspectionDefectRateList = [];
    }

    if (params.form.reinspectionDefectRateList && Array.isArray(params.form.reinspectionDefectRateList)) {
      form.value.reinspectionDefectRateList = [...params.form.reinspectionDefectRateList];
    } else {
      form.value.reinspectionDefectRateList = [];
    }
  }

  setVisible(true);
};

defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

:deep(.el-tabs__content) {
  padding: 20px 20px;
}
</style>
