import { ReqPage, ResPage } from "@/api/interface/index";
import { WpDept } from "@/typings/quality-work-plan/wp_dept";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/quality-work-plan/statistics";
import { WpGroup } from "@/typings/quality-work-plan/wp_group";

const baseUrl = `${API_PREFIX}/wpDept`;

// 列表
export const getWpDeptList = (params?: ReqPage) => {
  return http.post<ResPage<WpDept.Item>>(`${baseUrl}/list`, params);
};
//草稿
export const getWpDeptDraft = (params?: ReqPage) => {
  return http.post<ResPage<WpDept.Item>>(`${baseUrl}/listDraft`, params);
};
// 待分解列表
export const getListToBeDecomposed = (params?: ReqPage) => {
  return http.post<ResPage<WpDept.Item>>(`${baseUrl}/listToBeDecomposed`, params);
};
export const listByGroupPlanId = (params?: ReqPage) => {
  return http.post<ResPage<WpDept.Item>>(`${baseUrl}/listByGroupPlanId`, params);
};

//待接收
export const listToBeReceived = (params?: ReqPage) => {
  return http.post<ResPage<WpDept.Item>>(`${baseUrl}/listToBeReceived`, params);
};
//接收
export const receive = (ids: number[]) => {
  return http.post(`${baseUrl}/receive`, { ids });
};

// 详情
export const getWpDeptDetail = (id: number) => {
  return http.post<WpDept.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createWpDept = (data: WpDept.Item) => {
  return http.post(`${baseUrl}/create`, data);
};
// 克隆分部计划
export const cloneDeptPlan = (data: WpDept.Item) => {
  return http.post(`${baseUrl}/cloneDeptPlan`, data);
};

//分解集团计划
export const decomposeTask = (data: WpDept.Item) => {
  return http.post(`${baseUrl}/decomposeTask`, data);
};

// 修改
export const editWpDept = (data: WpDept.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

export const modifyDecomposeTask = (data: WpDept.Item) => {
  return http.post(`${baseUrl}/modifyDecomposeTask`, data);
};

// 删除
export const deleteWpDept = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};
// 提交
export const submitWpDept = (ids: number[]) => {
  return http.post(`${baseUrl}/submit`, { ids });
};
//分解集团计划-提交
export const submitDecomposeTask = (params: { ids: number[]; groupPlanId: number }) => {
  return http.post(`${baseUrl}/submitDecomposeTask`, params);
};

// 取消
export const cancelWpDept = (planNos: string[]) => {
  return http.post(`${API_PREFIX}/wpCommon/cancel`, { planNos });
};

// 导出
export const exportWpDept = (params?: WpDept.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importWpDept = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importWpDeptTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

//集团任务达成率
export const statsDeptPassedRatio = (data: Statistics.IQueryParams) => {
  return http.post<Statistics.statsDeptPassedRatio>(`${API_PREFIX}/wpStats/statsDeptPassedRatio`, data);
};
//集团任务状态分布
export const statsDeptReportStatus = (data: Statistics.IQueryParams) => {
  return http.post<Statistics.statsGroupReportStatus>(`${API_PREFIX}/wpStats/statsDeptReportStatus`, data);
};
// 图表集团任务列表
export const statsDeptList = (params?: ReqPage) => {
  return http.post<ResPage<WpGroup.Item>>(`${API_PREFIX}/wpStats/statsDeptList`, params);
};
