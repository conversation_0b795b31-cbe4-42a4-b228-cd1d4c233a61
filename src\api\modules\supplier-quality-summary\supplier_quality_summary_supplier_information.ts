import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/summaryInfo`;

// 获取供应商数据列表
export const getSupplierInformation = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl}/list`, params);
};

// 获取供应商数据详情
export const getSupplierInformationDetail = (id: number) => {
  return http.get<Partial<SupplierInformation.Item>>(`${baseUrl}/get/${id}`);
};

// 创建供应商数据
export const createSupplierInformation = (data: SupplierInformation.NewParams) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改供应商数据
export const editSupplierInformation = (data: SupplierInformation.EditParams) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 导入
export const importSupplierInformation = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSupplierInformationTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 删除
export const deleteSupplierInformation = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl}/list`, params);
};
