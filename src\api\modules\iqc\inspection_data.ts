import { ReqPage, ResPage } from "@/api/interface/index";
import { InspectionData } from "@/typings/iqc/inspection_data";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/iqcOrder`;

// 查询所有检验单
export const getInspectionList = (params?: ReqPage) => {
  return http.post<ResPage<InspectionData.Item>>(`${baseUrl}/list`, params);
};

// 导入
export const importInspectionData = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importInspectionDataTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 删除
export const deleteInspectionData = (pkeys: string[]) => {
  return http.post(`${baseUrl}/del`, { pkeys });
};
