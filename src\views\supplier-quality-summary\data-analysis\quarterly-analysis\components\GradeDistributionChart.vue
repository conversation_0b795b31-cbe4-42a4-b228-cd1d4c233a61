<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">品质等级分布</h3>
      <el-button type="primary" size="small" @click="handleViewDetail">查看明细数据</el-button>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="GradeDistributionChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Statistics } from "@/typings/supplier-quality-summary/statistics_quarterly";

interface Props {
  chartData: Statistics.GradeDistributionData[];
  searchParams: Statistics.IQueryParams;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  viewDetail: [chartType: string, data: Statistics.GradeDistributionData[]];
}>();

// 检查是否有数据
const hasData = computed(() => props.chartData && props.chartData.length > 0);

// 图表配置
const chartOption = computed<ECOption>(() => {
  const pieData = props.chartData.map(item => ({
    name: item.name,
    value: item.value
  }));

  const colors = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4"];

  return {
    title: {
      text: "品质等级分布",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold"
      }
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left",
      data: pieData.map(item => item.name)
    },
    color: colors,
    series: [
      {
        name: "品质等级",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["60%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: pieData
      }
    ]
  };
});

// 查看明细数据
const handleViewDetail = () => {
  emit("viewDetail", "gradeDistribution", props.chartData);
};
</script>

<style lang="scss" scoped>
.echarts-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.echarts-content {
  flex: 1;
  width: 100%;
  min-height: 400px;
}

.no-data {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
