import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityPersonCertificateMaintainer } from "@/typings/quality-person/quality_person_certificate_maintainer";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qualityPersonCM`;

// 列表
export const getQualityPersonCertificateMaintainerList = (params?: ReqPage) => {
  return http.post<ResPage<QualityPersonCertificateMaintainer.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityPersonCertificateMaintainerDetail = (id: number) => {
  return http.post<QualityPersonCertificateMaintainer.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityPersonCertificateMaintainer = (data: QualityPersonCertificateMaintainer.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityPersonCertificateMaintainer = (data: QualityPersonCertificateMaintainer.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityPersonCertificateMaintainer = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQualityPersonCertificateMaintainer = (params?: QualityPersonCertificateMaintainer.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQualityPersonCertificateMaintainer = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQualityPersonCertificateMaintainerTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
