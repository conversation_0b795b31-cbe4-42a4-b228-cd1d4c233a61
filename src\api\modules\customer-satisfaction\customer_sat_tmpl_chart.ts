import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerSatTmplChart } from "@/typings/customer-satisfaction/customer_sat_tmpl_chart";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ccsTmplChart`;

// 列表
export const getCustomerSatTmplChartList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerSatTmplChart.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerSatTmplChartDetail = (id: number) => {
  return http.post<CustomerSatTmplChart.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerSatTmplChart = (data: CustomerSatTmplChart.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerSatTmplChart = (data: CustomerSatTmplChart.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerSatTmplChart = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerSatTmplChart = (params?: CustomerSatTmplChart.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerSatTmplChart = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerSatTmplChartTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
