<template>
  <el-dialog v-model="visible" width="90%" :destroy-on-close="true" :title="`${t(title)}`">
    <el-form
      ref="formRef"
      label-width="auto"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      :show-message="isZh"
    >
      <el-alert
        v-if="form.rejectReason && [`已退回`].includes(form.auditStatus ?? '')"
        class="mb20"
        :title="t('退回理由')"
        type="error"
        :description="form.rejectReason"
        show-icon
        :closable="false"
      />
      <el-row>
        <el-col :span="8">
          <el-form-item :label="$t('供应商类型')" prop="supplierType">
            <el-select v-model="form.supplierType" :placeholder="$t('请选择')" clearable filterable>
              <el-option v-for="({ label }, index) of srm_supplier_type" :key="index" :label="$t(label)" :value="label" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('供应商名称')" prop="supplierName">
            <el-select v-model="form.supplierName" :placeholder="$t('请选择')" clearable filterable>
              <el-option v-for="({ label }, index) of srm_supplier" :key="index" :label="$t(label)" :value="label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('审核计划编号')" prop="orderNo">
            <el-input v-model="form.orderNo" :placeholder="$t('自动生成')" clearable disabled />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item :label="$t('物料组')" prop="materialTeam">
            <el-select v-model="form.materialTeam" :placeholder="$t('请选择')" clearable filterable>
              <el-option v-for="({ label }, index) of srm_material_team" :key="index" :label="$t(label)" :value="label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('物料名称')" prop="materialName">
            <el-input v-model="form.materialName" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('供应商地址')" prop="supplierAddr">
            <el-input v-model="form.supplierAddr" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item :label="$t('计划审核月份')" prop="planAuditDate">
            <el-date-picker
              style="width: 100%"
              v-model="form.planAuditDate"
              type="month"
              value-format="YYYY-MM"
              :placeholder="$t('请选择')"
              :validate-event="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('省外行程路线')" prop="route">
            <el-input v-model="form.route" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('审核场所')" prop="auditSite">
            <el-input v-model="form.auditSite" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item :label="$t('审核类型')" prop="auditType">
            <el-select v-model="form.auditType" :placeholder="$t('请选择')" clearable filterable>
              <el-option v-for="({ label }, index) of srm_audit_type" :key="index" :label="$t(label)" :value="label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('审核范围')" prop="auditScope">
            <el-select v-model="form.auditScope" :placeholder="$t('请选择')" clearable filterable>
              <el-option v-for="({ label }, index) of srm_audit_scope" :key="index" :label="$t(label)" :value="label" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('工厂')" prop="plant">
            <el-select v-model="form.plant" :placeholder="$t('请选择')" clearable filterable>
              <el-option v-for="({ label }, index) of factory" :key="index" :label="$t(label)" :value="label" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="SCM" prop="scmName">
            <RemoteSearch :job-num="form.scmNo" v-model="form.scmName" @extra="scmExtra" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SQE" prop="sqeName">
            <RemoteSearch :job-num="form.sqeNo" v-model="form.sqeName" @extra="sqeExtra"></RemoteSearch>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('报告审核人')" prop="auditorName">
            <RemoteSearch :job-num="form.auditorNo" v-model="form.auditorName" @extra="auditorExtra"></RemoteSearch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item :label="$t('审核参考文件')" prop="refFileName">
            <UploadFiles
              :file-url="fileUrl"
              :multiple="false"
              :is-show-tip="true"
              :immediate="true"
              :limit="1"
              :file-type="['xls', 'xlsx']"
              btn-name="上传附件"
              :show-delete="isDraft ? true : false"
              @upload-success="refFileSuccess"
              @file-list-empty="refFileEmpty"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('审核目的')" prop="purpose">
            <el-input v-model="form.purpose" type="textarea" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('备注')" prop="remarks">
            <el-input v-model="form.remarks" type="textarea" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.id > 0">
        <el-col :span="24">
          <el-form-item :label="$t('审核人员')" prop="auditStaffList" id="plan-table">
            <SupplierAuditStaff
              :plan-id="form.id"
              :is-view="!isDraft"
              :list="form.auditStaffList"
              @update-data="updateOtherAuditStaffs"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-else>
        <el-col :span="24">
          <el-form-item :label="$t('审核人员')" prop="auditStaffList" id="plan-table">
            <SupplierAuditStaffAdd :data="form.auditStaffList" @update-data="updateOtherAuditStaffs" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("关闭") }}</el-button>
      <el-button v-show="isDraft" type="primary" @click="handleSubmit">
        {{ $t("保存") }}
      </el-button>

      <el-button v-show="!isView && [`待提交`, `已退回`].includes(form.auditStatus ?? '')" type="primary" @click="submitApproval">
        {{ $t("提交审批") }}
      </el-button>

      <span
        v-show="[`待审批`].includes(form.auditStatus ?? '') && userInfo.jobNum === form.currentAuditor && isApproval"
        class="ml-[10px]"
      >
        <el-button type="primary" @click="submitApprovalSuccess">{{ $t("通过") }}</el-button>
        <el-button type="danger" @click="openBackModal">{{ $t("退回") }}</el-button>
      </span>
    </template>
  </el-dialog>
  <BackModal ref="BackModalRef" @back-complete="reject" />
</template>

<script setup lang="tsx" name="SupplierAuditPlanModal">
import { SupplierAuditPlan } from "@/typings/supplier-annual-audit/supplier_audit_plan";
import {
  submitToAudit,
  auditSupplierAuditPlan,
  backSupplierAuditPlan
} from "@/api/modules/supplier-annual-audit/supplier_audit_plan";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { Dict } from "@/typings/dict";
import { useI18n } from "vue-i18n";
import RemoteSearch from "../../../components/RemoteSearch.vue";
import { Staff } from "@/typings/staff";
import BackModal from "./BackModal.vue";
import useUserStore from "@/stores/modules/user";
import UploadFiles from "@/components/Upload/UploadFiles.vue";
import { isEmpty } from "@/utils/is";
import { updateUserSearchExtra } from "@/utils";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import SupplierAuditStaff from "./SupplierAuditStaff.vue";
import SupplierAuditStaffAdd from "./SupplierAuditStaffAdd.vue";
const { isZh } = useLanguageCode();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

const BackModalRef = ref<InstanceType<typeof BackModal> | null>(null);

const { t } = useI18n();

interface IState {
  title: string;
  isApproval: boolean;
  isView: boolean;
  form: SupplierAuditPlan.Item;
  srm_audit_scope: Dict.IDataItem[];
  srm_audit_type: Dict.IDataItem[];
  srm_supplier_type: Dict.IDataItem[];
  srm_material_team: Dict.IDataItem[];
  factory: Dict.IDataItem[];
  srm_supplier: Dict.IDataItem[];
  api?: (params: any) => Promise<any>;
  updateApi?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  auditType: [{ required: true, message: "请填写审核类型", trigger: "blur" }],
  planAuditDate: [{ required: true, message: "请填写计划审核日期", trigger: "blur" }],
  supplierType: [{ required: true, message: "请填写供应商类型", trigger: "blur" }],
  supplierName: [{ required: true, message: "请填写供应商名称", trigger: "blur" }],
  materialTeam: [{ required: true, message: "请填写物料组", trigger: "blur" }],
  materialName: [{ required: true, message: "请填写物料名称", trigger: "blur" }],
  supplierAddr: [{ required: true, message: "请填写供应商地址", trigger: "blur" }],
  route: [{ required: true, message: "请填写省外行程路线", trigger: "blur" }],
  auditScope: [{ required: true, message: "请填写审核范围", trigger: "blur" }],
  auditSite: [{ required: true, message: "请填写审核场所", trigger: "blur" }],
  scmName: [{ required: true, message: "请填写SCM", trigger: "blur" }],
  plant: [{ required: true, message: "请选择工厂", trigger: "blur" }],
  sqeName: [{ required: true, message: "请填写SQE", trigger: "blur" }],
  // otherAuditStaffList: [{ required: true, message: "请填写其他审核人员", trigger: "blur" }],
  auditorName: [{ required: true, message: "请填写审批人", trigger: "blur" }]
};

const visible = ref(false);
const isDraft = computed(() => {
  return !isView.value && [``, `待提交`, `已退回`].includes(form.value.auditStatus ?? "");
});
const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  isApproval: false,
  title: "",
  srm_audit_type: [],
  srm_audit_scope: [],
  factory: [],
  srm_supplier_type: [],
  srm_material_team: [],
  srm_supplier: [],
  form: {} as SupplierAuditPlan.Item
});
const {
  form,
  title,
  isView,
  srm_audit_scope,
  factory,
  srm_supplier_type,
  srm_audit_type,
  srm_supplier,
  srm_material_team,
  isApproval
} = toRefs(state);

const updateOtherAuditStaffs = (data: []) => {
  form.value.auditStaffList = data;
};

const fileUrl = computed(() => {
  if (!isEmpty(form.value.refFileUrl)) {
    return [{ name: form.value.refFileName ?? "", url: form.value.refFileUrl ?? "" }];
  } else {
    return [];
  }
});

const submitApproval = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!(form.value);
      ElMessageBox.confirm(t("确定提交审批"), t("提示"), {
        confirmButtonText: t("确认"),
        cancelButtonText: t("取消"),
        type: ""
      }).then(async () => {
        const { success } = await submitToAudit(form.value.id ?? 0);
        if (!success) {
          ElMessage.error({ message: t(`提交失败`) });
          return;
        }
        ElMessage.success({ message: t(`提交成功`) });
        state.getTableList!();
        setVisible(false);
      });
    } catch (error) {
      console.log(error);
    }
  });
};

const submitApprovalSuccess = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      ElMessageBox.confirm(t("确定通过"), t("提示"), {
        confirmButtonText: t("确认"),
        cancelButtonText: t("取消"),
        type: ""
      }).then(async () => {
        const { success } = await auditSupplierAuditPlan(form.value.id ?? 0);
        if (!success) {
          ElMessage.error({ message: t(`审批失败`) });
          return;
        }
        ElMessage.success({ message: t(`审批成功`) });
        state.getTableList!();
        setVisible(false);
      });
    } catch (error) {
      console.log(error);
    }
  });
};

const reject = () => {
  state.getTableList!();
  setVisible(false);
};

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const { success, data } = await state.api!(form.value);
      if (!success) {
        ElMessage.error({ message: t(`保存失败`) });
        return;
      }
      form.value = data;
      if (!isEmpty(form.value.id) && form.value.id! > 0) {
        state.api = state.updateApi;
      }
      ElMessage.success({ message: t(`保存成功`) });
      state.getTableList!();
      // setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};
const data = computed(() => form.value.otherAuditStaffList);

const refFileSuccess = (res: { name: string; url: string }) => {
  form.value.refFileName = res.name;
  form.value.refFileUrl = res.url;
};

const refFileEmpty = () => {
  form.value.refFileName = "";
  form.value.refFileUrl = "";
};

const scmExtra = (staff: Staff.Extra) => {
  updateUserSearchExtra(form, staff, "scm", "SCM没有绑定邮箱,请更换");
};

const sqeExtra = (staff: Staff.Extra) => {
  updateUserSearchExtra(form, staff, "sqe", "SQE没有绑定邮箱,请更换");
};

const auditorExtra = (staff: Staff.Extra) => {
  updateUserSearchExtra(form, staff, "auditor", "审批人没有绑定邮箱,请更换");
};

const openBackModal = () => {
  const params = {
    form: { ...form.value },
    api: backSupplierAuditPlan
  };
  BackModalRef.value?.acceptParams(params);
};
defineExpose({
  acceptParams,
  setVisible
});
</script>
<style>
#plan-table .card {
  padding: 0;
  border: 0;
  border-radius: 0;
  box-shadow: none;
}
</style>
