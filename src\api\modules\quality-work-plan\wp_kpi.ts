import { ReqPage, ResPage } from "@/api/interface/index";
import { WpKpi } from "@/typings/quality-work-plan/wp_kpi";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/wpKpi`;

// 列表
export const getWpKpiList = (params?: ReqPage) => {
  return http.post<ResPage<WpKpi.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getWpKpiDetail = (id: number) => {
  return http.post<WpKpi.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createWpKpi = (data: WpKpi.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editWpKpi = (data: WpKpi.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteWpKpi = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportWpKpi = (params?: WpKpi.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importWpKpi = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importWpKpiTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
