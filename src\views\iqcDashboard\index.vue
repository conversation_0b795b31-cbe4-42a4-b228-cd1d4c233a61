<template>
  <div class="dataVisualize-box">
    <div class="card top-box">
      <div class="top-header">
        <div class="logo-container">
          <img src="./images/logo.png" alt="Logo" class="logo-image" />
        </div>
        <div class="top-title">IQC Dashboard</div>
        <div class="date-display">
          <div class="current-date">{{ currentDate }}</div>
          <div class="current-week">{{ currentWeek }}</div>
        </div>
      </div>
      <el-tabs v-model="tabActive" type="card" class="demo-tabs">
        <el-tab-pane v-for="item in tab" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
      </el-tabs>
      <div class="top-content">
        <el-row :gutter="20">
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <!-- <div class="item-left sle">
              <span class="left-title">访问总数</span>
              <div class="img-box">
                <img src="./images/book-sum.png" alt="" />
              </div>
              <span class="left-number">848.132w</span>
            </div> -->
            <div class="item-right">
              <div class="echarts-title">实时待检信息</div>
              <BasicTable ref="basicTable" :data="iqcOrderData" />
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right">
              <div class="echarts-title">检验批次合格率(整体)</div>
              <CommonBar
                direction="vertical"
                :x-data="dateLabelsQualify"
                :series-data="[
                  {
                    name: '合格率',
                    data: passRates
                  }
                ]"
                :key="passRates.length"
              />
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right">
              <div class="echarts-title">DPPM</div>
              <CommonBar :x-data="dateLabelsDppm" :series-data="dppmData" :key="dateLabelsDppm.length" />
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right pie-chart">
              <div class="echarts-title">所有物料</div>
              <div class="book-echarts">
                <CommonPie :data="inspectionData" :key="inspectionData.length" />
              </div>
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right">
              <div class="echarts-title">检验不合格批次数(by物料类别)</div>
              <CommonBar :x-data="dateLabelsFailNum" :series-data="failNumData" :key="dateLabelsFailNum.length" />
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right">
              <div class="echarts-title">检验批次合格率(by物料类别)</div>
              <div class="book-echarts">
                <CommonLine
                  :x-data="dateLabelsMaterialPassRate"
                  :series-data="materialPassRateData"
                  :key="dateLabelsMaterialPassRate.length"
                />
              </div>
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right pie-chart">
              <div class="echarts-title">加急物料</div>
              <div class="book-echarts">
                <CommonPie :data="inspectionTimelinessData" :key="inspectionTimelinessData.length" />
              </div>
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right">
              <div class="echarts-title">检验及时率</div>
              <div class="book-echarts">
                <CommonLine
                  :x-data="dateLabelsTimelinessRate"
                  :series-data="timelinessRateData"
                  :key="dateLabelsTimelinessRate.length"
                />
              </div>
            </div>
          </el-col>
          <el-col class="mb20" :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
            <div class="item-right">
              <div class="echarts-title">不良供应商(TOP5)</div>
              <CommonBar
                direction="horizontal"
                :x-data="dateLabelsFailSupplier"
                :series-data="[{ name: '不良率', data: failSupplierData }]"
                :label-max-length="3"
                :key="failSupplierData.length"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- <div class="card bottom-box">
      <div class="bottom-title">数据来源</div>
      <div class="bottom-tabs">
        <el-tabs v-model="tabActive" class="demo-tabs">
          <el-tab-pane v-for="item in tab" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="curve-echarts">
        <Curve ref="curveRef" />
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts" name="dataVisualize">
import { ref, onMounted, watch, onBeforeUnmount, computed } from "vue";
import {
  getStatsByAllBatchQualifyRate,
  getStatsDppmRate,
  getStatsByFailNumByType,
  getStatsBatchFailBySupplierRate,
  getStatsBatchQualifyRate,
  getStatsTimelinessRate,
  getStatsInspectionByAll,
  getStatsInspectionTimeliness,
  listIqcOrder,
  getRefreshTime
} from "@/api/modules/iqc-order/iqc-order"; // 添加API引入
import CommonPie from "./components/commonPie.vue";
import BasicTable from "./components/BasicTable.vue";
import CommonBar from "./components/commonBar.vue";
import CommonLine from "./components/commonLine.vue";
import { useRoute } from "vue-router";
// import Curve from "./components/curve.vue";

// 添加日期和星期显示
const currentDate = ref("");
const currentWeek = ref("");
let dateTimer: number | null = null;

// 更新日期和星期
const updateDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const day = now.getDate().toString().padStart(2, "0");

  // 格式化日期：yyyy-MM-dd
  currentDate.value = `${year}-${month}-${day}`;

  // 获取星期
  const weekDays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
  currentWeek.value = weekDays[now.getDay()];
};

// 在setup脚本中添加路由实例
const route = useRoute();
const plantParam = computed(() => {
  const plantCode = route.query.plant as string;
  return plantCodeToNameMap[plantCode] || "深圳"; // Default to "深圳" if the code is not found
});
const tab = [
  { label: "日", name: 0, statsType: 0 },
  { label: "周", name: 1, statsType: 1 },
  { label: "月", name: 2, statsType: 2 },
  { label: "季度", name: 3, statsType: 3 },
  { label: "年", name: 4, statsType: 4 }
];
const plantCodeToNameMap = {
  "1": "深圳",
  "2": "越南(海防)",
  "3": "越南(平阳)",
  "4": "杭州",
  "5": "青岛",
  "6": "合肥",
  "7": "罗马尼亚",
  "8": "意大利NPE",
  "9": "墨西哥",
  "10": "汽车电子",
  "11": "新材料"
};
// 初始化时设置默认值
const tabActive = ref(4); // 默认选中"年"

// 添加类型定义
interface DateRange {
  start: Date;
  end: Date;
  formatter: (d: Date) => string;
}

const getDateRange = (currentTab: any): DateRange => {
  const now = new Date();

  switch (currentTab.statsType) {
    case 4: // 年
      return {
        start: new Date(now.getFullYear() - 5, 0, 1),
        end: now,
        formatter: (d: Date) => `${d.getFullYear()}`
      };
    case 3: // 季度
      return {
        start: new Date(now.getFullYear() - 2, now.getMonth() - 8, 1),
        end: now,
        formatter: (d: Date) => `${d.getFullYear()}-Q${Math.ceil((d.getMonth() + 1) / 3)}`
      };
    case 2: // 月
      return {
        start: new Date(now.getFullYear() - 1, now.getMonth() - 11, 1),
        end: now,
        formatter: (d: Date) => `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, "0")}`
      };
    case 1: // 周
      return {
        start: new Date(now.getTime() - 4 * 7 * 24 * 3600 * 1000),
        end: now,
        formatter: (d: Date) => {
          const year = d.getFullYear();
          const week = Math.ceil(((+d - +new Date(year, 0, 1)) / 86400000 + 1) / 7);
          return `${year}-${week.toString().padStart(2, "0")}`;
        }
      };
    case 0: // 日
      return {
        start: new Date(now.getTime() - 6 * 24 * 3600 * 1000),
        end: now,
        formatter: (d: Date) =>
          `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, "0")}-${d.getDate().toString().padStart(2, "0")}`
      };
    default:
      return { start: now, end: now, formatter: () => "" };
  }
};

// 统一获取数据方法
// 修改响应式数据定义
const dateLabelsQualify = ref<string[]>([]);
const dateLabelsDppm = ref<string[]>([]);
const dateLabelsFailNum = ref<string[]>([]);
const dateLabelsFailSupplier = ref<string[]>([]);
const dateLabelsMaterialPassRate = ref<string[]>([]);
const dateLabelsTimelinessRate = ref<string[]>([]);
const passRates = ref<number[]>([]);
const dppmData = ref<any[]>([]);
const failNumData = ref<any[]>([]);
const failSupplierData = ref<any[]>([]);
const materialPassRateData = ref<any[]>([]);
const timelinessRateData = ref<any[]>([]);
const inspectionData = ref<any[]>([]);
const inspectionTimelinessData = ref<any[]>([]);
const iqcOrderData = ref<any[]>([]);
// 新增独立的数据获取方法
const fetchQualifyData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsByAllBatchQualifyRate({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: plantParam.value
    });
    dateLabelsQualify.value = res.data.map(item => item.dateLabel);
    passRates.value = res.data.map(item => item.passRate);
  } catch (error) {
    console.error("获取合格率数据失败:", error);
  }
};

const fetchDppmData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsDppmRate({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: plantParam.value
    });

    dateLabelsDppm.value = res.data.map(item => item.dateLabel);
    const materialTypes = Array.from(new Set(res.data.flatMap(month => month.itemList.map(item => item.materialType))));
    const materialMap = new Map<string, number[]>(materialTypes.map(type => [type, []]));

    res.data.forEach(monthData => {
      materialMap.forEach((values, materialType) => {
        const item = monthData.itemList.find(i => i.materialType === materialType);
        values.push(item ? item.dppm : 0);
      });
    });

    dppmData.value = Array.from(materialMap.entries()).map(([name, data]) => ({ name, data }));
  } catch (error) {
    console.error("获取DPPM数据失败:", error);
  }
};

const fetchFailNumData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsByFailNumByType({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: plantParam.value
    });

    dateLabelsFailNum.value = res.data.map(item => item.dateLabel);
    const materialTypes = Array.from(new Set(res.data.flatMap(month => month.itemList.map(item => item.materialType))));
    // 修复类型声明
    const materialMap = new Map<string, number[]>(materialTypes.map(type => [type, []]));

    res.data.forEach(monthData => {
      materialMap.forEach((values, materialType) => {
        const item = monthData.itemList.find(i => i.materialType === materialType);
        values.push(item ? item.defectCount : 0);
      });
    });

    failNumData.value = Array.from(materialMap.entries()).map(([name, data]) => ({ name, data }));
  } catch (error) {
    console.error("获取不合格数数据失败:", error);
  }
};

const fetchFailSupplierData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsBatchFailBySupplierRate({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: plantParam.value
    });

    dateLabelsFailSupplier.value = res.data.map(item => item.supplierName);
    failSupplierData.value = res.data.map(item => item.defectRate);
  } catch (error) {
    console.error("获取不合格数数据失败:", error);
  }
};
const fetchMaterialPassRateData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsBatchQualifyRate({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: plantParam.value
    });

    dateLabelsMaterialPassRate.value = res.data.map(item => item.dateLabel);
    const materialTypes = Array.from(new Set(res.data.flatMap(month => month.itemList.map(item => item.materialType))));
    const materialMap = new Map<string, number[]>(materialTypes.map(type => [type, []]));

    res.data.forEach(monthData => {
      materialMap.forEach((values, materialType) => {
        const item = monthData.itemList.find(i => i.materialType === materialType);
        values.push(item ? item.passRate : 0);
      });
    });

    materialPassRateData.value = Array.from(materialMap.entries()).map(([name, data]) => ({ name, data }));
  } catch (error) {
    console.error("获取物料合格率数据失败:", error);
  }
};
const fetchTimelinessRateData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsTimelinessRate({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: plantParam.value
    });

    dateLabelsTimelinessRate.value = res.data.map(item => item.dateLabel);
    const materialTypes = Array.from(new Set(res.data.flatMap(month => month.itemList.map(item => item.materialType))));
    const materialMap = new Map<string, number[]>(materialTypes.map(type => [type, []]));

    res.data.forEach(monthData => {
      materialMap.forEach((values, materialType) => {
        const item = monthData.itemList.find(i => i.materialType === materialType);
        values.push(item ? item.timelyRate : 0);
      });
    });

    timelinessRateData.value = Array.from(materialMap.entries()).map(([name, data]) => ({ name, data }));
  } catch (error) {
    console.error("获取物料合格率数据失败:", error);
  }
};
const fetchInspectionData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsInspectionByAll({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: plantParam.value
    });

    // 按照示例结构修改数据赋值
    inspectionData.value = [
      { value: res.data.totalBatches, name: "送检批次" },
      { value: res.data.onTimeBatches, name: "已检验批次" },
      { value: res.data.lateBatches, name: "不及时检验批次" }
    ];
  } catch (error) {
    console.error("获取检验数据失败:", error);
  }
};
const fetchInspectionTimelinessData = async (currentTab: any) => {
  try {
    const range = getDateRange(currentTab);
    const res = await getStatsInspectionTimeliness({
      statsType: currentTab.statsType,
      statsDateStart: range.formatter(range.start) as string,
      statsDateEnd: range.formatter(range.end) as string,
      plant: "深圳"
    });

    // 按照示例结构修改数据赋值
    inspectionTimelinessData.value = [
      { value: res.data.totalBatches, name: "送检批次" },
      { value: res.data.onTimeBatches, name: "已检验批次" },
      { value: res.data.lateBatches, name: "不及时检验批次" }
    ];
  } catch (error) {
    console.error("获取检验及时数据失败:", error);
  }
};
const fetchIqcOrderData = async (...params: any) => {
  // 添加参数定义
  try {
    const currentTab = tab.find(item => item.name === tabActive.value);
    const range = getDateRangeYMD(currentTab);
    const requestParams = {
      condition: {
        startDate: range.formatter(range.start),
        endDate: range.formatter(range.end),
        plant: plantParam.value,
        ...params
      },
      pageNum: 1,
      pageSize: 15
    };

    const res = await listIqcOrder(requestParams);
    iqcOrderData.value = res.data.list;
  } catch (error) {
    console.error("获取待检信息失败:", error);
  }
};

// 添加定时器引用
const refreshTimer = ref<NodeJS.Timeout>();
const tabTimer = ref<NodeJS.Timeout>();

const fetchRefreshTime = async () => {
  try {
    const res = await getRefreshTime();

    // 清除旧定时器
    if (refreshTimer.value) clearInterval(refreshTimer.value);
    if (tabTimer.value) clearInterval(tabTimer.value);

    // 数据刷新轮询
    // 修改定时器设置部分
    if (res.data.refreshValues) {
      const minutes = parseInt(res.data.refreshValues) || 0;
      refreshTimer.value = setInterval(fetchData, minutes * 60 * 1000);
    }
    if (res.data.tabChangeValues) {
      const minutes = parseInt(res.data.tabChangeValues) || 0;
      tabTimer.value = setInterval(
        () => {
          tabActive.value = tabActive.value > 0 ? tabActive.value - 1 : 4;
        },
        minutes * 60 * 1000
      );
    }
  } catch (error) {
    console.error("获取刷新时间间隔失败:", error);
  }
};

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  refreshTimer.value && clearInterval(refreshTimer.value);
  tabTimer.value && clearInterval(tabTimer.value);
  cleanupDateTimer(); // 清理日期时间定时器
});

// 修改后的统一获取数据方法
const fetchData = async () => {
  const currentTab = tab.find(item => item.name === tabActive.value);
  if (!currentTab) return;

  try {
    await Promise.all([
      fetchQualifyData(currentTab),
      fetchDppmData(currentTab),
      fetchFailNumData(currentTab),
      fetchFailSupplierData(currentTab),
      fetchMaterialPassRateData(currentTab),
      fetchTimelinessRateData(currentTab),
      fetchInspectionData(currentTab),
      fetchInspectionTimelinessData(currentTab),
      fetchIqcOrderData()
    ]);
  } catch (error) {
    console.error("数据获取失败:", error);
  }
};

// 初始化及监听tab变化
onMounted(() => {
  fetchData();
  fetchRefreshTime(); // 添加初始化调用
  initDateTimeDisplay(); // 初始化日期时间显示
});
watch(() => tabActive.value, fetchData);
watch(
  () => route.query.plant,
  () => {
    fetchData(); // 新增路由参数监听
    fetchRefreshTime();
  }
);

// 在 getDateRange 方法后添加
const formatYMD = (d: Date) =>
  `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, "0")}-${d.getDate().toString().padStart(2, "0")}`;

// 新增始终返回 yyyy-MM-dd 格式的时间范围获取方法
const getDateRangeYMD = (currentTab: any): DateRange => {
  const now = new Date();

  // 特殊处理年统计类型
  if (currentTab.statsType === 4) {
    return {
      start: new Date(now.getFullYear() - 5, 0, 1), // 固定为1月1日
      end: new Date(now.getFullYear(), 11, 31), // 固定为12月31日
      formatter: formatYMD
    };
  }

  const baseRange = getDateRange(currentTab);
  return {
    ...baseRange,
    formatter: formatYMD
  };
};

// 在 onMounted 中添加初始化日期时间的代码
const initDateTimeDisplay = () => {
  // 立即更新一次
  updateDateTime();
  // 设置定时器，每秒更新一次
  dateTimer = window.setInterval(updateDateTime, 1000);
};

// 在 onBeforeUnmount 中添加清理定时器的代码
const cleanupDateTimer = () => {
  if (dateTimer) {
    clearInterval(dateTimer);
    dateTimer = null;
  }
};
</script>

<style scoped lang="scss">
@import "./index";
.dataVisualize-box {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1a2333 0%, #0d1a2f 100%);
  &::before {
    position: absolute;
    top: -50%;
    left: -50%;
    z-index: 1;
    width: 200%;
    height: 200%;
    content: "";
    background: radial-gradient(circle at 50% 50%, rgb(0 102 204 / 10%) 0%, rgb(26 35 51 / 0%) 70%);
    filter: blur(30px);
  }
}

// 修改卡片背景色
.card {
  background: linear-gradient(to right bottom, rgb(38 48 68 / 80%) 0%, rgb(28 36 54 / 80%) 100%);
}

// 添加顶部选项卡样式
.demo-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;

  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }

  :deep(.el-tabs__nav) {
    //border: none;
  }

  :deep(.el-tabs__item) {
    color: #fff;
    &.is-active {
      color: #409eff;
    }
  }
}
</style>
