<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <template #tableHeader="{ selectedListIds }">
        <el-button v-auth="'quality-target-qt-target:add'" type="primary" @click="openQtTargetModal('新增')">
          {{ $t("新增") }}
        </el-button>
        <el-button v-auth="'quality-target-qt-target:edit'" type="primary" @click="openQtTargetModal('编辑')">
          {{ $t("编辑") }}
        </el-button>
        <el-button v-auth="'quality-target-qt-target:delete'" type="danger" @click="batchDelete(selectedListIds as number[])">{{
          $t("删除")
        }}</el-button>
      </template>
    </ProTable>
    <QtTargetModal ref="QtTargetModalRef" />
  </div>
</template>

<script setup lang="tsx" name="quality-target-qt-target">
import { getQtTargetList, createQtTarget, editQtTarget, deleteQtTarget } from "@/api/modules/quality-target/qt_target";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import QtTargetModal from "./components/QtTargetModal.vue";
import { ElMessage } from "element-plus";
import { useDict } from "@/hooks/useDict";
import { QtTarget } from "@/typings/quality-target/qt_target";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { ref, reactive } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import WordBreak from "@/views/components/WordBreak.vue";
import { yesOrNo, Frequency } from "@/enums/statusQualityTarget";

// ProTable 实例
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const QtTargetModalRef = ref<InstanceType<typeof QtTargetModal> | null>(null);
let queryParams = reactive<QtTarget.IQueryParams>({} as QtTarget.IQueryParams);

const initParam = reactive({});
const { qt_factory: factory } = useDict("qt_factory");
const my = ref(localStorage.getItem("my") === "true" ? true : false);

// 表格配置项
const columns = reactive<ColumnProps<QtTarget.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  { prop: "targetName", label: "质量目标", width: 180, search: { el: "input", order: 0 } },
  { prop: "reportFreq", label: "汇报频率", width: 100, enum: Frequency, tag: true },
  { prop: "target", label: "目标值", width: 100 },
  { prop: "operator", label: "达标规则", width: 100 },
  { prop: "numeratorName", label: "分子名称", width: 120 },
  { prop: "denominatorName", label: "分母名称", width: 120 },
  {
    prop: "aviator",
    label: "计算公式",
    width: 280,
    render: ({ row }) => {
      return <WordBreak text={row.aviator}></WordBreak>;
    }
  },
  { prop: "isKpi", label: "是否关键KPI", width: 100, enum: yesOrNo, tag: true },
  { prop: "isPercent", label: "是否百分比", width: 100, enum: yesOrNo, tag: true },
  { prop: "isCalc", label: "是否运算", width: 100, enum: yesOrNo, tag: true },
  {
    prop: "remarks",
    label: "备注",
    width: 280,
    render: ({ row }) => {
      return <WordBreak text={row.remarks}></WordBreak>;
    }
  }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;
  queryParams = reactive(condition);
  return getQtTargetList({
    condition,
    pageNum,
    pageSize
  });
};

const resetForm = () => {
  proTable.value?.getTableList();
};

const batchDelete = async (id?: number | number[]) => {
  const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(deleteQtTarget, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openQtTargetModal = (title: string, row: Partial<QtTarget.Item> = {}) => {
  if (isEmptyObj(row) && title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    factory,
    form,
    api: title === "新增" ? createQtTarget : title === "编辑" ? editQtTarget : undefined,
    getTableList: proTable.value?.getTableList
  };
  QtTargetModalRef.value?.acceptParams(params);
};
</script>
