import { ReqPage, ResPage } from "@/api/interface/index";
import { QtChange } from "@/typings/quality-target/qt_change";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/qtChange`;

// 列表
export const getQtChangeList = (params?: ReqPage) => {
  return http.post<ResPage<QtChange.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQtChangeDetail = (id: number) => {
  return http.post<QtChange.Item>(`${baseUrl}/get/${id}`);
};
//变更任务-根据质量目标编号获取最新变更任务
export const getByTargetNo = (targetNo: string) => {
  return http.post<QtChange.Item>(`${baseUrl}/getByTargetNo/${targetNo}`);
};

// 新增
export const createQtChange = (data: QtChange.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQtChange = (data: QtChange.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQtChange = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQtChange = (params?: QtChange.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQtChange = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQtChangeTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 退回
export const dismiss = (params: { id: number; rejectReason: string }) => {
  return http.post<QtChange.Item>(`${baseUrl}/dismiss`, params);
};

// 审批通过
export const pass = (data: QtChange.Item) => {
  return http.post(`${baseUrl}/pass`, data);
};

//提交
export const submit = (data: QtChange.Item) => {
  return http.post(`${baseUrl}/submit`, data);
};
