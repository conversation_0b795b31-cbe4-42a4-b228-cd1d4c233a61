import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { QuarterlyData } from "@/typings/supplier-quality-summary/quarterly_data";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/quarter`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;
// 获取季度质量数据列表
export const getQuarterlyData = (params?: ReqPage) => {
  return http.post<ResPage<QuarterlyData.Item>>(`${baseUrl}/list`, params);
};

// 获取质量数据详情
export const getQuarterlyDataDetail = (id: number) => {
  return http.get<Partial<QuarterlyData.Item>>(`${baseUrl}/get/${id}`);
};

// 修改质量数据
export const editQuarterData = (data: QuarterlyData.EditParams) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 导出
export const exportQuarterlyData = (ids: number[]) => {
  return http.post(`${baseUrl}/exportListData`, { ids });
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
