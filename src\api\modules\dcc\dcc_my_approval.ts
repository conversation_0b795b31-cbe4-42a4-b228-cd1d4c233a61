import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { dccApprovalList } from "@/typings/dcc/dcc_my_approval";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/documentMy`;

// 获取审批列表
export const getDccApprovalList = (params?: ReqPage) => {
  return http.post<ResPage<dccApprovalList.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 获取审批详情
export const getDccApprovalDetail = (id: number) => {
  return http.post<Partial<dccApprovalList.DocumentDetail>>(`${baseUrl}/get/${id}`);
};

// 审批
export const auditDccApproval = (id: number) => {
  return http.post(`${baseUrl}/audit/${id}`);
};
// 驳回
export const rejectDccApproval = (id: number, rejectReason: string) => {
  return http.post(`${baseUrl}/reject/${id}`, { rejectReason });
};
