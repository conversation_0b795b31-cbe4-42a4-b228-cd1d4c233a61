<template>
  <el-dialog id="import-het" v-model="dialogVisible" :title="parameter.title" :destroy-on-close="true" width="780px" draggable>
    <el-form
      class="drawer-multiColumn-form"
      label-suffix=" :"
      :rules="rules"
      :validate-on-rule-change="false"
      label-width="120"
      ref="formRef"
      :show-message="isZh"
      :model="form"
    >
      <el-form-item>
        <el-button type="primary" :icon="Download" @click="downloadTemp"> {{ $t("下载模板") }} </el-button>
        &nbsp;&nbsp;&nbsp;&nbsp;
        {{ $t("下载模板后，请勿修改表头，按要求填写数据") }}
      </el-form-item>

      <el-form-item :label="$t('上传文件')" prop="file">
        <el-upload
          v-model:file-list="form.file"
          ref="uploadRef"
          action="#"
          class="upload"
          :drag="true"
          :limit="excelLimit"
          :multiple="false"
          :show-file-list="true"
          :http-request="uploadExcel"
          :before-upload="beforeExcelUpload"
          :on-exceed="handleExceed"
          :on-success="excelUploadSuccess"
          :on-error="excelUploadError"
          :auto-upload="false"
          :accept="parameter.fileType!.join(',')"
        >
          <slot name="empty">
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              {{ $t("将文件拖到此处，或点击上传") }}
            </div>
          </slot>
          <template #tip>
            <slot name="tip">
              <div class="el-upload__tip">{{ $t("请上传 .xls , .xlsx 标准格式文件，文件最大为") }} {{ parameter.fileSize }}M</div>
            </slot>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog">{{ $t("取消") }}</el-button>
      <el-button type="primary" @click="handleUpload">{{ $t("导入") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="SupplierInformationImportModal">
import { ref } from "vue";
import { Download } from "@element-plus/icons-vue";
import {
  ElNotification,
  UploadRequestOptions,
  UploadRawFile,
  FormInstance,
  UploadProps,
  genFileId,
  ElMessageBox
} from "element-plus";
import { useI18n } from "vue-i18n";
import { download } from "@/api/modules/common";
import { useLanguageCode } from "@/hooks/useLanguageCode";

const { isZh } = useLanguageCode();

export interface ExcelParameterProps {
  title: string; // 标题
  fileSize?: number; // 上传文件的大小
  fileType?: File.ExcelMimeType[]; // 上传文件的类型
  tempApi?: (params: any) => Promise<any>; // 下载模板的Api
  importApi?: (params: any) => Promise<any>; // 批量导入的Api
  getTableList?: () => void; // 获取表格数据的Api
}

const formRef = ref<FormInstance>();
const form = ref({
  file: []
});

// 最大文件上传数
const excelLimit = ref(1);
// dialog状态
const dialogVisible = ref(false);
const { t } = useI18n();
// 父组件传过来的参数
const parameter = ref<ExcelParameterProps>({
  title: "",
  fileSize: 100,
  fileType: ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
});

// 上传组件的引用
const uploadRef = ref<InstanceType<(typeof import("element-plus"))["ElUpload"]> | null>(null);

// 接收父组件参数
const acceptParams = (params: ExcelParameterProps) => {
  parameter.value = { ...parameter.value, ...params };
  dialogVisible.value = true;
};

// Excel 导入模板下载
const downloadTemp = async () => {
  if (!parameter.value.tempApi) return;
  download(parameter.value.tempApi, true);
};

// 文件上传
const uploadExcel = async (param: UploadRequestOptions) => {
  let excelFormData = new FormData();
  excelFormData.append("file", param.file);
  try {
    const { success } = await parameter.value.importApi!(excelFormData);
    if (success) {
      ElNotification({
        title: t("温馨提示"),
        message: `${t("导入成功")}`,
        type: "success"
      });
      parameter.value.getTableList && parameter.value.getTableList();
      dialogVisible.value = false;
    }
  } catch (error) {
  } finally {
    form.value.file = [];
    uploadRef.value?.clearFiles();
  }
};

// 文件上传之前的钩子
const beforeExcelUpload: UploadProps["beforeUpload"] = (rawFile: UploadRawFile) => {
  const isExcel =
    rawFile.type === "application/vnd.ms-excel" ||
    rawFile.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  const isLt = rawFile.size / 1024 / 1024 < parameter.value.fileSize!;
  if (!isExcel) ElNotification.warning(t("上传文件只能是 xls / xlsx 格式！"));
  if (!isLt) ElNotification.warning(t(`上传文件大小不能超过 ${parameter.value.fileSize}MB！`));
  return isExcel && isLt;
};

// 文件数超出提示
const handleExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

// 上传成功之后的钩子
const excelUploadSuccess: UploadProps["onSuccess"] = () => {
  ElNotification({
    title: t("温馨提示"),
    message: t("Excel文件上传成功！"),
    type: "success"
  });
};

// 上传失败之后的钩子
const excelUploadError: UploadProps["onError"] = () => {
  ElNotification({
    title: t("温馨提示"),
    message: t("Excel文件上传失败，请您重新上传！"),
    type: "error"
  });
};

// 表单验证规则
const rules = {
  file: [{ required: true, message: t("请选择上传文件"), trigger: "change" }]
};

// 触发上传
const handleUpload = async () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      ElMessageBox.confirm(t("确定导入数据"), t("提示"), {
        confirmButtonText: t("确认"),
        cancelButtonText: t("取消"),
        type: "warning"
      }).then(async () => {
        uploadRef.value?.submit();
      });
    } catch (error) {
      console.log(error);
    }
  });
};

const closeDialog = () => {
  dialogVisible.value = false;
};

defineExpose({
  acceptParams
});
</script>

<style>
#import-het .el-radio-group {
  display: block;
}
#import-het .el-radio {
  margin-right: 8px;
}
</style>
