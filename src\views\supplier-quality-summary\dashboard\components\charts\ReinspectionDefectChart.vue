<template>
  <div class="echarts-container">
    <div class="chart-header">
      <h3 class="chart-title">复检不良率(DPPM)趋势</h3>
    </div>
    <div class="echarts-content">
      <div v-if="!hasData" class="no-data">
        <el-empty description="暂无数据" />
      </div>
      <ECharts v-else :option="chartOption" />
    </div>
  </div>
</template>

<script setup lang="tsx" name="ReinspectionDefectChart">
import { computed } from "vue";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Dashboard } from "@/typings/supplier-quality-summary/dashboard";

interface Props {
  chartData: Dashboard.JqeInspectionData[];
}

const props = defineProps<Props>();

// 检查是否有数据
const hasData = computed(() => {
  return props.chartData && props.chartData.length > 0;
});

// 图表配置
const chartOption = computed<ECOption>(() => {
  if (!hasData.value) return {};

  // 获取所有周次
  const allWeeks = new Set<string>();
  props.chartData.forEach(supplier => {
    supplier.data.forEach(item => {
      allWeeks.add(item.week);
    });
  });
  const weeks = Array.from(allWeeks).sort();

  // 构建系列数据
  const series = props.chartData.map((supplier, index) => {
    const colors = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272"];
    const data = weeks.map(week => {
      const found = supplier.data.find(item => item.week === week);
      return found ? found.dppm : null;
    });

    return {
      name: supplier.supplier,
      type: "line",
      data: data,
      itemStyle: {
        color: colors[index % colors.length]
      },
      lineStyle: {
        color: colors[index % colors.length]
      }
    };
  });

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross"
      }
    },
    legend: {
      data: props.chartData.map(item => item.supplier),
      top: 30
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: weeks,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: "value",
      name: "DPPM"
    },
    series: series
  };
});
</script>

<style scoped>
.echarts-container {
  width: 100%;
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.echarts-content {
  height: 350px;
}

.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
