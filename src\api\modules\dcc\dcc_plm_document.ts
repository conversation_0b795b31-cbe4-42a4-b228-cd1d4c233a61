import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { dccPlmList } from "@/typings/dcc/dcc_plm_document";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import axios from "axios";

const baseUrl = `${API_PREFIX}/plmDocument`;
const baseUrl2 = "/plm-api/document";

// 获取文档列表
export const getDccPlmList = (params?: ReqPage) => {
  return http.post<ResPage<dccPlmList.Item>>(`${baseUrl}/list`, params);
};

// 获取文档详情
export const getDccPlmDetail = (itemNumber: string) => {
  return axios.post(`${baseUrl2}/list`, { itemNumber });
};

// 下载文档
export const getDccPlmDownload = (itemNumber: string, attachmentName: string) => {
  return axios({
    url: `${baseUrl2}/downloadItemFile`,
    method: "POST",
    data: { itemNumber, attachmentName },
    responseType: "blob" // 关键设置：将响应类型设为 blob
  })
    .then(response => {
      // 创建 Blob 对象
      const blob = new Blob([response.data]);

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);

      // 创建临时 a 标签并触发下载
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = attachmentName; // 设置下载文件名

      // 将链接添加到文档中并模拟点击
      document.body.appendChild(link);
      link.click();

      // 点击后移除链接
      document.body.removeChild(link);

      // 释放 URL 对象
      window.URL.revokeObjectURL(downloadUrl);

      return {
        code: 0,
        msg: "下载成功"
      };
    })
    .catch(error => {
      console.error("文件下载失败:", error);
      return {
        code: 500,
        msg: "下载失败"
      };
    });
};
