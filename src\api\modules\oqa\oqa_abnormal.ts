import { ReqPage, ResPage } from "@/api/interface/index";
import { OqaAbnormal } from "@/typings/oqa/oqa_abnormal";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/oqaAbnormal`;

// 列表
export const getOqaAbnormalList = (params?: ReqPage) => {
  return http.post<ResPage<OqaAbnormal.Item>>(`${baseUrl}/list`, params);
};
//操作记录
export const getChangeLogList = (params?: ReqPage) => {
  return http.post<ResPage<OqaAbnormal.Log>>(`${baseUrl}/getChangeLogList`, params);
};
// 详情
export const getOqaAbnormalDetail = (id: number) => {
  return http.post<OqaAbnormal.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createOqaAbnormal = (data: OqaAbnormal.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editOqaAbnormal = (data: OqaAbnormal.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteOqaAbnormal = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportOqaAbnormal = (params?: OqaAbnormal.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importOqaAbnormal = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importOqaAbnormalTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
export const statsOqaAbnormalPareto = (params?: OqaAbnormal.IQueryParamsStats) => {
  return http.post<OqaAbnormal.statsOqaAbnormalPareto>(`${baseUrl}/statsOqaAbnormalPareto`, params);
};
