import { ReqPage, ResPage } from "@/api/interface/index";
import { CiProjectManager } from "@/typings/ci-project/ci_project_manager";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/ci-project/statistics";

const baseUrl = `${API_PREFIX}/project`;

// 列表
export const getCiProjectManagerList = (params?: ReqPage) => {
  return http.post<ResPage<CiProjectManager.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCiProjectManagerDetail = (id: number) => {
  return http.post<CiProjectManager.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCiProjectManager = (data: CiProjectManager.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCiProjectManager = (data: CiProjectManager.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCiProjectManager = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCiProjectManager = (params?: CiProjectManager.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCiProjectManager = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCiProjectManagerTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
// 审批
export const submitToAudit = (id: number) => {
  return http.post<CiProjectManager.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 退回
export const backAudit = (params: { id: number; rejectReason: string }) => {
  return http.post<CiProjectManager.Item>(`${baseUrl}/reject`, params);
};

// 审批通过
export const audit = (data: CiProjectManager.Item) => {
  return http.post(`${baseUrl}/audit`, data);
};

// 审批列表
export const getApproveList = (params?: ReqPage) => {
  return http.post<ResPage<CiProjectManager.Item>>(`${baseUrl}/listToBeAudited`, params);
};

// 取消列表
export const listToBeCanceled = (params?: ReqPage) => {
  return http.post<ResPage<CiProjectManager.Item>>(`${baseUrl}/listToBeCanceled`, params);
};

// 会签提交
export const sign = (data: CiProjectManager.Item) => {
  return http.post(`${baseUrl}/sign`, data);
};

// 取消
export const cancelRequest = (data: CiProjectManager.Item) => {
  return http.post(`${baseUrl}/cancelRequest`, data);
};

// 取消确认
export const cancel = (id: number) => {
  return http.post<CiProjectManager.Item>(`${baseUrl}/cancel/${id}`);
};
// 取消退回
export const cancelReject = (params: { id: number; rejectReason: string }) => {
  return http.post<CiProjectManager.Item>(`${baseUrl}/cancelReject`, params);
};

export const statsByPlant = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.IStatsByPlantData>(`${baseUrl}/statsByPlant`, params);
};

export const statsByStatus = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.IStatsByStatusData>(`${baseUrl}/statsByStatus`, params);
};

export const statsDeptTopicCount = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.IStatsByPlantData>(`${baseUrl}/statsDeptProjectNumber`, params);
};

export const statsImprovementTypeTopicCount = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.IStatsByStatusData>(`${baseUrl}/statsImprovementTypeTopicCount`, params);
};
