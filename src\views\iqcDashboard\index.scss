.dataVisualize-box {
  .top-box {
    box-sizing: border-box;
    padding: 25px 40px 0;
    margin-bottom: 10px;
    .top-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      position: relative;
    }
    .demo-tabs {
      margin-top: 20px;
      margin-bottom: 20px;
      text-align: center;
      .el-tabs__header {
        margin: 0 auto;
        display: inline-block;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 5px;
        background: rgba(38, 48, 68, 0.5);
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
      .el-tabs__item {
        color: rgba(255, 255, 255, 0.7);
        transition: all 0.3s;
        border-radius: 6px;
        padding: 0 20px;
        height: 36px;
        line-height: 36px;
        &.is-active {
          color: #fff;
          font-weight: bold;
          background: rgba(64, 158, 255, 0.1);
          box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
        }
        &:hover {
          color: #fff;
        }
      }
    }
    .logo-container {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-40%);
      z-index: 1;
      .logo-image {
        width: 200px;
        height: 200px;
        object-fit: contain;
      }
    }
    .top-title {
      font-family: DIN;
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      color: #fff;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: auto;
      white-space: nowrap;
    }
    .date-display {
      position: absolute;
      right: 20px;
      top: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      color: #fff;
      .current-date {
        font-family: DIN;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 4px;
      }
      .current-week {
        font-family: DIN;
        font-size: 18px;
      }
    }
    .top-content {
      margin-top: 10px;
      .item-left {
        box-sizing: border-box;
        height: 100%;
        padding: 40px 0 30px 30px;
        overflow: hidden;
        color: #ffffff;
        background: url("./images/book-bg.png");
        background-position: 50%;
        background-size: cover;
        border-radius: 20px;
        .left-title {
          font-family: DIN;
          font-size: 20px;
        }
        .img-box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 90px;
          height: 90px;
          margin: 40px 0 20px;
          background-color: #ffffff;
          border-radius: 20px;
          box-shadow: 0 10px 20px rgb(0 0 0 / 14%);
          img {
            width: 60px;
            height: 65px;
          }
        }
        .left-number {
          overflow: hidden;
          font-family: DIN;
          font-size: 62px;
        }
      }
      .item-center {
        display: flex;
        flex-wrap: wrap;
        place-content: space-between space-between;
        height: 100%;
        .traffic-box {
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          width: 47%;
          height: 48%;
          padding: 25px;
          border-radius: 30px;
          .traffic-img {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 70px;
            height: 70px;
            margin-bottom: 10px;
            background-color: #ffffff;
            border-radius: 19px;
          }
        }
        img {
          width: 33px;
          height: 33px;
        }
        .item-value {
          margin-bottom: 4px;
          font-family: DIN;
          font-size: 28px;
          font-weight: bold;
          color: #1a1a37;
        }
        .traffic-name {
          overflow: hidden;
          font-family: DIN;
          font-size: 15px;
          color: #1a1a37;
          white-space: nowrap;
        }
        .gitee-traffic {
          background: url("./images/1-bg.png");
          background-color: #e8faea;
          background-size: 100% 100%;
        }
        .gitHub-traffic {
          background: url("./images/2-bg.png");
          background-color: #e7e1fb;
          background-size: 100% 100%;
        }
        .today-traffic {
          background: url("./images/3-bg.png");
          background-color: #fdf3e9;
          background-size: 100% 100%;
        }
        .yesterday-traffic {
          background: url("./images/4-bg.png");
          background-color: #f0f5fb;
          background-size: 100% 100%;
        }
      }
      .item-right {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 400px;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        background: linear-gradient(to right bottom, rgba(38, 48, 68, 0.9) 0%, rgba(28, 36, 54, 0.9) 100%);
        .echarts-title {
          height: 40px;
          padding: 0 20px;
          font-family: DIN;
          font-size: 16px;
          font-weight: bold;
          line-height: 40px;
          color: #fff;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .book-echarts {
          flex: 1;
          width: 100%;
        }
        .table-box {
          min-height: 100px;
        }
      }
    }
  }
  .bottom-box {
    position: relative;
    padding: 20px 0 0;
    .bottom-title {
      position: absolute;
      top: 75px;
      left: 50px;
      font-family: DIN;
      font-size: 18px;
      font-weight: bold;
    }
    .bottom-tabs {
      padding: 0 50px;
    }
    .curve-echarts {
      box-sizing: border-box;
      height: 400px;
      padding: 0 50px;
    }
  }
}
.el-tabs {
  align-items: center;
}

