# 线上环境
VITE_USER_NODE_ENV = production

# 公共基础路径
VITE_PUBLIC_PATH = /

# 路由模式
# Optional: hash | history
VITE_ROUTER_MODE = hash

# 是否启用 gzip 或 brotli 压缩打包，如果需要多个压缩规则，可以使用 “,” 分隔
# Optional: gzip | brotli | none
VITE_BUILD_COMPRESS = none

# 打包压缩后是否删除源文件
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 打包时是否删除 console
VITE_DROP_CONSOLE = true

VITE_PRIVATE_KEY = 'F5934C14C0F0193CA1D0344AF9C99739'

# 跨域配置
VITE_PROXY = [["/plm-api","http://***********:80/plmApi"],["/epros-api","http://**********:8088/openService"]]

# 是否开启 VitePWA
VITE_PWA = true

# 线上环境接口地址
VITE_API_URL = "http://***********:8080"

# carNo超链接URL前缀
VITE_CAR_NO_URL = "http://***********:8503"

# 8D超链接URL前缀
VITE_8D_NO_URL = "http://***********:8502"