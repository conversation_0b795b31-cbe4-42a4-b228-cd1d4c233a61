<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-if="isMy" v-auth="'yqj-fa-fx-zz-apply:add'" type="primary" @click="openModal('分配任务')">{{
          $t("分配任务")
        }}</el-button>
        <el-button v-auth="'yqj-fa-fx-zz-apply:add'" type="primary" @click="openModal('查看明细')">{{
          $t("查看明细")
        }}</el-button>
        <el-button v-if="!isMy" v-auth="'yqj-fa-fx-zz-apply:edit'" type="primary" @click="openModal('提醒')">{{
          $t("提醒")
        }}</el-button>
        <el-button v-if="isMy" v-auth="'yqj-fa-fx-zz-apply:edit'" type="primary" @click="openModal('费用总结')">{{
          $t("费用总结")
        }}</el-button>
      </template>
    </ProTable>
    <Modal ref="modalRef" />
  </div>
</template>

<script setup lang="tsx" name="zx-quality-activity-activity-execute-activity-my-list">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";

import Modal from "@/views/zx-quality-activity/activity-execute/activity/components/Modal.vue";

import { ElButton, ElMessage } from "element-plus";
import { useDict } from "@/hooks/useDict";

import { isEmpty } from "@/utils/is";
import DateRange from "@/views/components/DateRange.vue";
import { ref, reactive, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";

import { getList, submit, remove, assignTask, updateCostSummary } from "@/api/modules/zx-quality-activity/activity";

import { computed } from "vue";
import { cloneDeep, isArray, isNil } from "lodash-es";
import useUserStore from "@/stores/modules/user";
import { getDeptAll } from "@/api/modules/dept";
import { Dept } from "@/typings/dept";
import { PlanStatus } from "@/enums/zx-quality-activity/planStatus";

import dayjs from "dayjs";
import LinkFile from "@/views/components/LinkFile.vue";
import { getUploadFileName } from "@/utils";
import { useRouter } from "vue-router";
import { ActivityStatus } from "@/enums/zx-quality-activity/activityStatus";

// ProTable 实例
const proTable = ref<ProTableInstance>();

const { check, currentRow } = useCheckSelectId<ZxQualityActivityActivity.Item>();

const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const modalRef = ref<InstanceType<typeof Modal> | null>(null);

const userStore = useUserStore();

const userInfo = computed(() => userStore.userInfo);

const router = useRouter();

const routePath = computed(() => router.currentRoute.value.path);

const isMy = computed(() => routePath.value.includes("my-list"));

const initParam = reactive({});

const resetCounter = ref(0);
const filterDate = ref([]);

const deptListAll = ref<Dept.Item[]>([]);

const {
  zx_quality_activity_activity_periods,
  zx_quality_activity_activity_type,
  zx_quality_activity_plan_status,
  zx_quality_activity_activity_status,
  zx_quality_activity_shape,
  zx_quality_activity_approval_nodes
} = useDict(
  "zx_quality_activity_activity_periods",
  "zx_quality_activity_plan_status",
  "zx_quality_activity_activity_type",
  "zx_quality_activity_activity_status",
  "zx_quality_activity_shape",
  "zx_quality_activity_approval_nodes"
);

// 表格配置项
const columns = computed<ColumnProps<ZxQualityActivityActivity.Item>[]>(() => [
  { type: "selection", fixed: "left", width: 40 },
  {
    prop: "number",
    label: "活动编号",
    width: 180,
    fixed: "left",
    search: {
      el: "input",
      order: 1
    },
    render: ({ row }) => {
      return (
        <ElButton link type="primary" onClick={() => openModal("查看明细", row)}>
          {row.number}
        </ElButton>
      );
    }
  },
  {
    prop: "status",
    label: "状态",
    fixed: "left",
    width: 130,
    enum: zx_quality_activity_activity_status.value,
    search: { order: 2, el: "select", props: { filterable: true } }
  },
  {
    prop: "activeNode",
    label: "活动节点",
    width: 180,
    enum: zx_quality_activity_approval_nodes.value,
    search: { order: 4, el: "select", props: { filterable: true } }
  },
  {
    prop: "approver",
    label: "审批人",
    width: 180
  },
  {
    prop: "approverWorkNo",
    label: "审批人工号",
    width: 180
  },

  {
    prop: "responsibleStaffName",
    label: "责任人",
    width: 180
  },
  {
    prop: "responsibleWorkNo",
    label: "责任人工号",
    width: 180
  },

  {
    prop: "shape",
    label: "开展形式",
    width: 150,
    enum: zx_quality_activity_shape.value,

    search: {
      order: 4,
      el: "select"
    }
  },
  {
    prop: "theme",
    label: "活动主题",
    width: 150
  },
  {
    prop: "theme",
    label: "活动主题",
    width: 150
  },
  {
    prop: "time",
    label: "活动时间",
    width: 230,
    render: ({ row }) => {
      return `${dayjs(row.startTime).format("YYYY-MM-DD")} - ${dayjs(row.endTime).format("YYYY-MM-DD")}`;
    }
  },

  {
    prop: "periods",
    label: "期数",
    width: 180,
    isShow: false,
    enum: zx_quality_activity_activity_periods.value,
    search: { order: 5, el: "select", props: { filterable: true } }
  },
  { prop: "activityPurpose", label: "活动目的", width: 200, search: { order: 6, el: "input", props: { filterable: true } } },
  {
    prop: "activityFactoryList",
    label: "工厂",
    width: 200,
    render: ({ row }) => {
      const list = row.activityFactoryList ?? [];
      return list.map(v => v.factoryName).join(",");
    }
  },
  {
    prop: "totalBudget",
    label: "总预算",
    width: 200,
    render: ({ row }) => {
      const list = row.activityCostBudgetList ?? [];
      const total = list.reduce((acc, curr) => acc + Number(curr.estimateCost), 0);
      return total;
    }
  },
  {
    prop: "createBy",
    label: "创建人",
    width: 180,
    search: {
      el: "input",
      order: 3
    }
  },
  {
    prop: "createByWorkNo",
    label: "创建人工号",
    width: 150
  },
  {
    prop: "createAt",
    label: "创建时间",
    width: 180,
    search: {
      el: "date-picker",
      props: {
        type: "daterange"
      },
      order: 7
    },
    render: ({ row }) => {
      return dayjs(row.createAt).format("YYYY-MM-DD HH:mm:ss");
    }
  }
]);

const getDeptListAll = async () => {
  const { data } = await getDeptAll();
  deptListAll.value = data;
};

const getTableList = (params: any) => {
  const { pageNum, pageSize, createAt, ...condition } = params;
  if (!isNil(createAt)) {
    if (isArray(createAt)) {
      const [startTime, endTime] = createAt;
      condition.startTime = dayjs(startTime).format("YYYY-MM-DD") + " 00:00:00";
      condition.endTime = dayjs(endTime).format("YYYY-MM-DD") + " 23:59:59";
    } else {
      condition.startTime = createAt;
    }
    delete condition.createAt;
  }
  // if (!isNil(condition.viewRange)) {
  //   if (condition.viewRange === "my") {
  //     condition.isViewMy = true;
  //   } else {
  //     condition.isViewMy = false;
  //   }
  //   delete condition.viewRange;
  // } else {
  //   condition.isViewMy = false;
  // }
  if (isMy.value) {
    condition.responsibleWorkNo = userInfo.value.jobNum;
  }
  return getList({
    ...condition,
    pageNum,
    pageSize
  });
};

const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const batchDelete = async (list?: YqjFaFxZzApply.Item | YqjFaFxZzApply.Item[]) => {
  const _list = isArray(list) ? list : [list];
  const noAuth = _list.filter(item => item?.applicantWorkNo !== userInfo.value.jobNum).map(v => v?.number);
  if (!isEmpty(noAuth)) {
    return ElMessage.error(t(`无法删除编号`) + noAuth.join());
  }
  const ids = _list.map(item => item?.id);
  // const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(remove, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openModal = async (title: string, row: Partial<ZxQualityActivityPlan.Item> = {}) => {
  check(row?.id);
  let disabled = true;
  const form = cloneDeep(currentRow.value);

  if (title === "分配任务") {
    if ([ActivityStatus.PENDING_ASSIGNMENT, ActivityStatus.RETURN].includes(form.status as ActivityStatus)) {
      disabled = false;
    }
  }

  if (title === "费用总结") {
    if (form.status === ActivityStatus.UPDATE) {
      disabled = false;
    }
  }

  if (form.status === ActivityStatus.COMPLETE) {
    disabled = true;
  }

  const apiMap = {
    分配任务: assignTask,

    费用总结: updateCostSummary
  };

  const params = {
    title,
    isView: title === "查看",
    disabled,
    zx_quality_activity_activity_type: zx_quality_activity_activity_type.value,
    zx_quality_activity_activity_periods: zx_quality_activity_activity_periods.value,
    form,
    api: apiMap[title as keyof typeof apiMap],
    getTableList: proTable.value?.getTableList
  };
  modalRef.value?.acceptParams(params);
};

onMounted(getDeptListAll);

onMounted(() => {
  console.log("onMounted apply-list");
});
</script>
