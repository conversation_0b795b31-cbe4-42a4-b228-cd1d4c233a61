<template>
  <el-dialog v-model="visible" width="80%" :destroy-on-close="true" :title="`${t('填写对策')}`">
    <el-form
      ref="formRef"
      label-width="auto"
      label-suffix=" :"
      :rules="rules"
      :model="form"
      :hide-required-asterisk="isView"
      :show-message="isZh"
    >
      <div class="block-tip">{{ $t("基本信息") }}</div>
      <el-descriptions :column="3" border>
        <el-descriptions-item :label="$t('日期')">{{ form.feedbackDate }}</el-descriptions-item>
        <el-descriptions-item :label="$t('工厂')">{{ form.plant }}</el-descriptions-item>
        <el-descriptions-item :label="$t('线体')">{{ form.line }}</el-descriptions-item>
        <el-descriptions-item :label="$t('机型')">{{ form.model }}</el-descriptions-item>
        <el-descriptions-item :label="$t('客户')">{{ form.customerName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('不良类型')">{{ form.category }}</el-descriptions-item>
        <el-descriptions-item :label="$t('跟进IPQC')">{{ form.ipqc }}</el-descriptions-item>
        <el-descriptions-item :label="$t('CQE工程师')">{{ form.cqeName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('CQE经理')">{{ form.cqmName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('异常描述')" :span="3">{{ form.description }}</el-descriptions-item>
        <el-descriptions-item :label="$t('临时对策')" :span="3">{{ form.temporarySolutions }}</el-descriptions-item>
      </el-descriptions>
      <div class="block-tip">{{ $t("改善对策") }}</div>

      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('改善对策')" prop="solutions">
            <el-input v-model="form.solutions" type="textarea" rows="4" :placeholder="$t('请填写')" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" :disabled="form.cqeNo !== userInfo.jobNum" type="primary" @click="handleSubmit">{{
        $t("确定")
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="TaskModal">
import { IpqcDipAbnormal } from "@/typings/ipqc-dip-abnormal/ipqc_dip_abnormal";
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import useUserStore from "@/stores/modules/user";
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const { isZh } = useLanguageCode();
const { t } = useI18n();

interface IState {
  isView: Boolean;
  form: Partial<IpqcDipAbnormal.Item>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  solutions: [{ required: true, message: "请填写改善对策", trigger: "blur" }]
};

const visible = ref(false);
const formRef = ref<FormInstance>();

const state = reactive<IState>({
  form: {},
  isView: false
});
const { form, isView } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};

const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const { success, message } = await state.api!({ id: form.value.id, solutions: form.value.solutions, status: "已完成" });
      if (!success) {
        ElMessage.error({ message: t(message) });
        return;
      }
      ElMessage.success({ message: t(`提交成功`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams,
  setVisible
});
</script>
<style>
#plan-table .card {
  padding: 0;
  border: 0;
  border-radius: 0;
  box-shadow: none;
}
</style>
