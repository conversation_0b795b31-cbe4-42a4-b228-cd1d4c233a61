import { ReqPage, ResPage } from "@/api/interface/index";
import { LineSuspension } from "@/typings/stop-line/line_suspension";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/lineSuspension`;

// 列表
export const getLineSuspensionList = (params?: ReqPage) => {
  return http.post<ResPage<LineSuspension.Item>>(`${baseUrl}/list`, params);
};

//操作记录
export const getChangeLogList = (params?: ReqPage) => {
  return http.post<ResPage<LineSuspension.Log>>(`${baseUrl}/getChangeLogList`, params);
};

//确认列表
export const listToBeVerified = (params?: ReqPage) => {
  return http.post<ResPage<LineSuspension.Item>>(`${baseUrl}/listToBeVerified`, params);
};

// 详情
export const getLineSuspensionDetail = (id: number) => {
  return http.post<LineSuspension.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createLineSuspension = (data: LineSuspension.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editLineSuspension = (data: LineSuspension.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteLineSuspension = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportLineSuspension = (params?: LineSuspension.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importLineSuspension = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importLineSuspensionTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 提交审批
export const submitToVerify = (id: number) => {
  return http.post(`${baseUrl}/submitToVerify/${id}`);
};

// 退回
export const backLineSuspension = (params: { id: number; rejectReason: string }) => {
  return http.post(`${baseUrl}/reject`, params);
};

// 同意
export const verify = (data: LineSuspension.Item) => {
  return http.post(`${baseUrl}/verify`, data);
};

// 重启
export const restart = (id: number) => {
  return http.post(`${baseUrl}/restart/${id}`);
};
