import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerInfo } from "@/typings/customer-satisfaction/customer_info";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/customerInfo`;

// 列表
export const getCustomerInfoList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerInfo.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerInfoDetail = (id: number) => {
  return http.post<CustomerInfo.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerInfo = (data: CustomerInfo.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerInfo = (data: CustomerInfo.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerInfo = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerInfo = (params?: CustomerInfo.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerInfo = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerInfoTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
