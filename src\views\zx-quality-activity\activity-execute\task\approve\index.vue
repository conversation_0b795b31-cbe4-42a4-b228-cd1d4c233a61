<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'yqj-fa-fx-zz-apply:audit'" type="primary" @click="handlePass">{{ $t("通过") }}</el-button>
        <el-button v-auth="'yqj-fa-fx-zz-apply:audit'" type="danger" @click="handleReject">{{ $t("退回") }}</el-button>
      </template>
    </ProTable>
    <Modal ref="modalRef" />
  </div>
</template>

<script setup lang="tsx" name="zx-quality-activity-activity-execute-task-approve">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";

import Modal from "@/views/zx-quality-activity/activity-execute/activity/approve/components/Modal.vue";

import { ElButton, ElMessage, ElMessageBox, ElNotification } from "element-plus";
import { useDict } from "@/hooks/useDict";

import { isEmpty } from "@/utils/is";

import { ref, reactive } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";

import { getTaskList as getList, taskPass as pass, taskReject as reject } from "@/api/modules/zx-quality-activity/approve";

import { computed } from "vue";
import { cloneDeep, isArray, isNil } from "lodash-es";
import useUserStore from "@/stores/modules/user";
import { getDeptAll } from "@/api/modules/dept";
import { Dept } from "@/typings/dept";

import dayjs from "dayjs";
import LinkFile from "@/views/components/LinkFile.vue";
import { getUploadFileName } from "@/utils";
import { TaskStatus } from "@/enums/zx-quality-activity/taskStatus";

// ProTable 实例
const proTable = ref<ProTableInstance>();

const { check, currentRow } = useCheckSelectId<ZxQualityActivityTask.Item>();

const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const modalRef = ref<InstanceType<typeof Modal> | null>(null);

const userStore = useUserStore();

const userInfo = computed(() => userStore.userInfo);

const initParam = reactive({});

const resetCounter = ref(0);
const filterDate = ref([]);

const deptListAll = ref<Dept.Item[]>([]);

const {
  zx_quality_activity_activity_periods,
  zx_quality_activity_activity_type,
  zx_quality_activity_plan_status,
  zx_quality_activity_activity_status,
  zx_quality_activity_approval_nodes,
  zx_quality_activity_shape,
  zx_quality_activity_task_status
} = useDict(
  "zx_quality_activity_activity_periods",
  "zx_quality_activity_plan_status",
  "zx_quality_activity_activity_type",
  "zx_quality_activity_activity_status",
  "zx_quality_activity_approval_nodes",
  "zx_quality_activity_shape",
  "zx_quality_activity_task_status"
);

// 表格配置项
const columns = computed<ColumnProps<ZxQualityActivityTask.Item>[]>(() => [
  { type: "selection", fixed: "left", width: 40 },
  {
    prop: "id",
    label: "ID",
    fixed: "left",
    width: 180,
    render: ({ row }) => {
      return (
        <ElButton link type="primary">
          {row.id}
        </ElButton>
      );
    }
  },
  {
    prop: "activityNumber",
    label: "活动编号",
    width: 180,
    fixed: "left",
    search: {
      el: "input",
      order: 1
    },
    render: ({ row }) => {
      return (
        <ElButton link type="primary">
          {row.activity.number}
        </ElButton>
      );
    }
  },
  {
    prop: "status",
    label: "状态",
    fixed: "left",
    width: 130,
    enum: zx_quality_activity_task_status.value,
    search: { order: 2, el: "select", props: { filterable: true } }
  },
  {
    prop: "activeNode",
    label: "活动节点",
    width: 180,
    enum: zx_quality_activity_approval_nodes.value
    // search: { order: 4, el: "select", props: { filterable: true } }
  },
  {
    prop: "taskExecuteDetail",
    label: "任务执行详情",
    width: 180
  },
  {
    prop: "file",
    label: "附件",
    width: 180,
    render: ({ row }) => {
      return <LinkFile url={row.file} />;
    }
  },

  {
    prop: "name",
    label: "任务名称",
    width: 180
  },
  {
    prop: "approver",
    label: "审批人",
    width: 180
  },
  {
    prop: "approverWorkNo",
    label: "审批人工号",
    width: 180
  },

  {
    prop: "responsibleStaffName",
    label: "责任人",
    width: 180
  },
  {
    prop: "responsibleWorkNo",
    label: "责任人工号",
    width: 180
  },

  {
    prop: "endDate",
    label: "截止日期",
    width: 150,
    search: {
      el: "date-picker",
      props: {
        type: "daterange"
      },
      order: 7
    },
    render: ({ row }) => {
      return dayjs(row.endDate).format("YYYY-MM-DD");
    }
  },

  {
    prop: "createBy",
    label: "创建人",
    width: 180,
    search: {
      el: "input",
      order: 3
    }
  },

  {
    prop: "createAt",
    label: "创建时间",
    width: 180,

    render: ({ row }) => {
      return dayjs(row.createAt).format("YYYY-MM-DD HH:mm:ss");
    }
  }
]);

const getDeptListAll = async () => {
  const { data } = await getDeptAll();
  deptListAll.value = data;
};

const getTableList = (params: any) => {
  const { pageNum, pageSize, createAt, ...condition } = params;

  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.applyStartDate = filterDate.value[0];
    condition.applyEndDate = filterDate.value[1];
  }
  if (!isNil(createAt)) {
    if (isArray(createAt)) {
      const [startTime, endTime] = createAt;
      condition.startTime = dayjs(startTime).format("YYYY-MM-DD") + " 00:00:00";
      condition.endTime = dayjs(endTime).format("YYYY-MM-DD") + " 23:59:59";
    } else {
      condition.startTime = createAt;
    }
    delete condition.createAt;
  }
  if (!isNil(condition.viewRange)) {
    if (condition.viewRange === "my") {
      condition.isViewMy = true;
    } else {
      condition.isViewMy = false;
    }
    delete condition.viewRange;
  } else {
    condition.isViewMy = false;
  }
  return getList({
    ...condition,
    pageNum,
    pageSize
  });
};

const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const handlePass = async () => {
  check();
  const row = currentRow.value;
  if (![TaskStatus.PENDING_APPROVAL].includes(row.status as TaskStatus)) {
    return ElNotification({
      title: t("错误操作"),
      message: t("当前任务状态不允许进行审批通过"),
      type: "error",
      duration: 3000
    });
  }
  await useHandleData(pass, row, t(`确认审批通过【${row.name}】`));
  proTable.value?.getTableList();
};

const confirmReject = async (row: ZxQualityActivityTask.Item) => {
  try {
    const { value: comment } = await ElMessageBox.prompt(t("请输入回退原因"), t("确定回退"), {
      confirmButtonText: t("回退"),
      cancelButtonText: t("取消"),
      inputPattern: /.+/,
      inputErrorMessage: t("请输入回退原因")
    });

    await reject({
      ...row,
      comment
    });
    return true;
  } catch (error) {
    console.log(error);
  }
};

const handleReject = async () => {
  check();
  const row = currentRow.value;
  if (![TaskStatus.PENDING_APPROVAL].includes(row.status as TaskStatus)) {
    return ElNotification({
      title: t("错误操作"),
      message: t("当前任务状态不允许进行审批退回"),
      type: "error",
      duration: 3000
    });
  }
  const { value: comment } = await ElMessageBox.prompt(t("请输入回退原因"), t("确定回退"), {
    confirmButtonText: t("回退"),
    cancelButtonText: t("取消"),
    inputPattern: /.+/,
    inputErrorMessage: t("请输入回退原因")
  });
  await useHandleData(
    reject,
    {
      ...row,
      comment
    },
    t(`确认审批退回【${row.name}】`)
  );
  proTable.value?.getTableList();
};

const openModal = async (title: string, row: Partial<ZxQualityActivityPlan.Item> = {}) => {
  check(row?.id);

  const form = cloneDeep(currentRow.value);

  const params = {
    title,
    isView: true,
    disabled: true,
    zx_quality_activity_activity_type: zx_quality_activity_activity_type.value,
    zx_quality_activity_activity_periods: zx_quality_activity_activity_periods.value,
    form,
    // api: submit,
    getTableList: proTable.value?.getTableList
  };
  modalRef.value?.acceptParams(params);
};
</script>
