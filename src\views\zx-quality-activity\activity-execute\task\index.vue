<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'yqj-fa-fx-zz-apply:add'" type="primary" @click="openModal('执行任务')">
          {{ $t("执行任务") }}
        </el-button>
      </template>
    </ProTable>
    <Modal ref="modalRef" />
  </div>
</template>

<script setup lang="tsx" name="zx-quality-activity-activity-execute-task-list">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";

import Modal from "@/views/zx-quality-activity/activity-execute/task/components/Modal.vue";

import { ElButton, ElMessage, ElNotification } from "element-plus";
import { useDict } from "@/hooks/useDict";

import { isEmpty } from "@/utils/is";
import DateRange from "@/views/components/DateRange.vue";
import { ref, reactive, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";

import { getList, remove, executeTask } from "@/api/modules/zx-quality-activity/task";

import { computed } from "vue";
import { cloneDeep, isArray, isNil } from "lodash-es";
import useUserStore from "@/stores/modules/user";
import { getDeptAll } from "@/api/modules/dept";
import { Dept } from "@/typings/dept";
import { PlanStatus } from "@/enums/zx-quality-activity/planStatus";

import dayjs from "dayjs";
import LinkFile from "@/views/components/LinkFile.vue";
import { getMessage, getUploadFileName } from "@/utils";
import { useRouter } from "vue-router";
import { TaskStatus } from "@/enums/zx-quality-activity/taskStatus";

// ProTable 实例
const proTable = ref<ProTableInstance>();

const { check, currentRow } = useCheckSelectId<ZxQualityActivityTask.Item>();

const { t } = useI18n();

// 打开 drawer(新增、查看、编辑)
const modalRef = ref<InstanceType<typeof Modal> | null>(null);

const userStore = useUserStore();

const userInfo = computed(() => userStore.userInfo);

const router = useRouter();

const routePath = computed(() => router.currentRoute.value.path);

const isMy = computed(() => routePath.value.includes("my-list"));

const initParam = reactive({});

const resetCounter = ref(0);
const filterDate = ref([]);

const deptListAll = ref<Dept.Item[]>([]);

const { zx_quality_activity_approval_nodes, zx_quality_activity_task_status } = useDict(
  "zx_quality_activity_approval_nodes",
  "zx_quality_activity_task_status"
);

// 表格配置项
const columns = computed<ColumnProps<ZxQualityActivityTask.Item>[]>(() => [
  { type: "selection", fixed: "left", width: 40 },
  {
    prop: "id",
    label: "ID",
    fixed: "left",
    width: 180,
    render: ({ row }) => {
      return (
        <ElButton link type="primary" onClick={() => openModal("执行任务", row)}>
          {row.id}
        </ElButton>
      );
    }
  },
  {
    prop: "activityNumber",
    label: "活动编号",
    width: 180,
    fixed: "left",
    search: {
      el: "input",
      order: 1
    },
    render: ({ row }) => {
      return (
        <ElButton link type="primary">
          {row.activity.number}
        </ElButton>
      );
    }
  },
  {
    prop: "status",
    label: "状态",
    fixed: "left",
    width: 130,
    enum: zx_quality_activity_task_status.value,
    search: { order: 2, el: "select", props: { filterable: true } }
  },
  {
    prop: "activeNode",
    label: "节点",
    width: 180,
    enum: zx_quality_activity_approval_nodes.value
    // search: { order: 4, el: "select", props: { filterable: true } }
  },
  {
    prop: "name",
    label: "任务名称",
    width: 180
  },
  {
    prop: "approver",
    label: "审批人",
    width: 180
  },
  {
    prop: "approverWorkNo",
    label: "审批人工号",
    width: 180
  },

  {
    prop: "responsibleStaffName",
    label: "责任人",
    width: 180
  },
  {
    prop: "responsibleWorkNo",
    label: "责任人工号",
    width: 180
  },

  {
    prop: "endDate",
    label: "截止日期",
    width: 150,
    search: {
      el: "date-picker",
      props: {
        type: "daterange"
      },
      order: 7
    },
    render: ({ row }) => {
      return dayjs(row.endDate).format("YYYY-MM-DD");
    }
  },
  {
    prop: "taskExecuteDetail",
    label: "任务执行详情",
    width: 180
  },
  {
    prop: "file",
    label: "附件",
    width: 180,
    render: ({ row }) => {
      return <LinkFile url={row.file} />;
    }
  },

  {
    prop: "createBy",
    label: "创建人",
    width: 180,
    search: {
      el: "input",
      order: 3
    }
  },

  {
    prop: "createAt",
    label: "创建时间",
    width: 180,

    render: ({ row }) => {
      return dayjs(row.createAt).format("YYYY-MM-DD HH:mm:ss");
    }
  }
]);

const getDeptListAll = async () => {
  const { data } = await getDeptAll();
  deptListAll.value = data;
};

const getTableList = (params: any) => {
  const { pageNum, pageSize, endDate, ...condition } = params;
  if (!isNil(endDate)) {
    if (isArray(endDate)) {
      const [startTime, endTime] = endDate;
      condition.startTime = dayjs(startTime).format("YYYY-MM-DD") + " 00:00:00";
      condition.endTime = dayjs(endTime).format("YYYY-MM-DD") + " 23:59:59";
    } else {
      condition.startTime = endDate;
    }
    delete condition.endDate;
  }
  // if (!isNil(condition.viewRange)) {
  //   if (condition.viewRange === "my") {
  //     condition.isViewMy = true;
  //   } else {
  //     condition.isViewMy = false;
  //   }
  //   delete condition.viewRange;
  // } else {
  //   condition.isViewMy = false;
  // }
  if (isMy.value) {
    condition.responsibleWorkNo = userInfo.value.jobNum;
  }
  return getList({
    ...condition,
    pageNum,
    pageSize
  });
};

const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const batchDelete = async (list?: YqjFaFxZzApply.Item | YqjFaFxZzApply.Item[]) => {
  const _list = isArray(list) ? list : [list];
  const noAuth = _list.filter(item => item?.applicantWorkNo !== userInfo.value.jobNum).map(v => v?.number);
  if (!isEmpty(noAuth)) {
    return ElMessage.error(t(`无法删除编号`) + noAuth.join());
  }
  const ids = _list.map(item => item?.id);
  // const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(remove, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const openModal = async (title: string, row: Partial<ZxQualityActivityTask.Item> = {}) => {
  check(row?.id);
  let disabled = false;
  const form = cloneDeep(currentRow.value);

  // if (title === "执行任务") {
  //   disabled = false;
  // }

  if (![TaskStatus.PENDING_EXECUTED, TaskStatus.RETURN].includes(form.status as TaskStatus)) {
    return ElNotification({
      title: t("错误操作"),
      message: t("当前任务状态不允许执行"),
      type: "error",
      duration: 3000
    });
  }

  // if (title === "编辑") {
  //   if ([PlanStatus.DRAFT, PlanStatus.RETURN].includes(form.status as PlanStatus)) {
  //     disabled = false;
  //     console.log("可以编辑");
  //   }
  //   if (form.createByWorkNo !== userInfo.value.jobNum) {
  //     disabled = true;
  //   }
  // }

  // if (title === "查看") {
  //   disabled = true;
  // } else if (title === "编辑") {
  //   if (form.applicantWorkNo !== userInfo.value.jobNum) {
  //     disabled = true;
  //   }
  //   if ([YqjApplyStatus.DRAFT, YqjApplyStatus.REJECTED].includes(form.status as YqjApplyStatus)) {
  //     disabled = false;
  //   }
  // }

  const params = {
    title,
    isView: title === "查看",
    disabled,

    form,
    api: executeTask,
    getTableList: proTable.value?.getTableList
  };
  modalRef.value?.acceptParams(params);
};

onMounted(getDeptListAll);

onMounted(() => {
  console.log("onMounted apply-list");
});
</script>
