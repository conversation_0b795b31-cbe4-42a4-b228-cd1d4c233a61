<template>
  <el-dialog v-model="visible" width="580px" :destroy-on-close="true" :title="`${t(title)}`">
    <el-form ref="formRef" label-width="auto" label-suffix=" :" :rules="rules" :model="form" :hide-required-asterisk="isView">
      <el-row>
        <el-col :span="20">
          <el-form-item :label="$t('维护人员')" prop="workName">
            <RemoteSearch :job-num="form.workNo" v-model="form.workName" @extra="workExtra" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="setVisible(false)">{{ $t("取消") }}</el-button>
      <el-button v-show="!isView" type="primary" @click="handleSubmit">{{ $t("确定") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="QualityPersonCertificateMaintainerModal">
import { QualityPersonCertificateMaintainer } from "@/typings/quality-person/quality_person_certificate_maintainer";
import { ElMessage, FormInstance } from "element-plus";
import { ref, reactive, toRefs } from "vue";
import { useI18n } from "vue-i18n";
import RemoteSearch from "@/views/components/RemoteSearch.vue";
import { Staff } from "@/typings/staff";
import { updateUserSearchExtra } from "@/utils";

const { t } = useI18n();
interface IState {
  title: string;
  isView: boolean;
  form: QualityPersonCertificateMaintainer.Item;

  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const rules = {
  workName: [{ required: true, message: t("请填写维护人员"), trigger: "blur" }]
};

const visible = ref(false);

const formRef = ref<FormInstance>();

const state = reactive<IState>({
  isView: false,
  title: "",
  form: {} as QualityPersonCertificateMaintainer.Item
});
const { form, title, isView } = toRefs(state);

const setVisible = (val: boolean) => {
  visible.value = val;
};
// 接收父组件传过来的参数
const acceptParams = (params: IState) => {
  Object.assign(state, params);
  setVisible(true);
};

const workExtra = (staff: Staff.Extra) => {
  updateUserSearchExtra(form, staff, "work", "维护人员没有绑定邮箱,请更换");
};

// 提交数据（新增/编辑）
const handleSubmit = () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await state.api!(form.value);
      ElMessage.success({ message: t(`保存成功`) });
      state.getTableList!();
      setVisible(false);
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
