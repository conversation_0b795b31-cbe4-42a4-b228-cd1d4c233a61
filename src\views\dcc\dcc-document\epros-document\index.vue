<!-- eslint-disable prettier/prettier -->
<!-- eslint-disable prettier/prettier -->
<!-- eslint-disable vue/html-closing-bracket-newline -->
<!-- eslint-disable prettier/prettier -->
<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      @reset="resetForm()"
      :pagination="false"
    >
      <template #tableHeader>
        <div class="action-buttons">
          <el-button type="primary" @click="openSSOLoginModal()">
            {{ $t("SSO登录") }}
          </el-button>
        </div>
      </template>
      <template #operation="scope">
        <el-button type="primary" link @click="downloadFile(scope.row)">预览</el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="dcc-dcc-document-epros-document">
import { getDccEprosList } from "@/api/modules/dcc/dcc_epros_document";
import { dccEprosList } from "@/typings/dcc/dcc_epros_document";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ElMessage, ElMessageBox } from "element-plus";
import { DocumentRemove, DocumentChecked } from "@element-plus/icons-vue";
import { useAdminDict, useDict } from "@/hooks/useDict";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { ref, reactive, computed, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import DateRange from "@/views/components/DateRange.vue";
import { download } from "@/api/modules/common";
import LinkFile from "@/views/components/LinkFile.vue";
import useUserStore from "@/stores/modules/user";
import RemoteSearchJobNum from "@/views/components/RemoteSearchJobNum.vue";
import LinkModal from "@/views/components/LinkModal.vue";
import { SuperRoleId } from "@/enums/status";

const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const initParam = reactive({});
const { t } = useI18n();
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();

const baseSSO_LOGIN_URL = "http://10.12.8.48:8080/login.action?sys=sysoa&userid=";
const SSO_LOGIN_URL = computed(() => baseSSO_LOGIN_URL + userInfo.value.jobNum);

const filterDate = ref([]);
const resetCounter = ref(0);

// 表格配置项
const columns = reactive<ColumnProps<dccEprosList.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "ruleName",
    label: "文件名称",
    width: 500,
    search: { order: 1, el: "input" }
  },
  {
    prop: "ruleNum",
    label: "文件编号",
    width: 240,
    search: { el: "input", order: 2 }
  },

  {
    prop: "drafter",
    label: "创建人",
    width: 120,
    search: { el: "input", order: 3 }
  },
  {
    prop: "pubOrg",
    label: "发布部门",
    width: 120,
    search: { el: "input", order: 4 }
  },
  {
    prop: "pubTime",
    label: "发布时间",
    width: 180
  },
  { prop: "operation", label: "操作", width: 120, fixed: "right" }
]);

const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};

const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const getTableList = async (params: any) => {
  const { data } = await getDccEprosList(userInfo.value.jobNum, -1);

  // 可根据 params 进行前端过滤
  const rawData = JSON.parse(data.data);
  const filteredData = rawData.filter((item: any) => {
    return (
      (!params.ruleName || item.ruleName.includes(params.ruleName)) &&
      (!params.ruleNum || item.ruleNum.includes(params.ruleNum)) &&
      (!params.drafter || item.drafter.includes(params.drafter)) &&
      (!params.pubOrg || item.pubOrg.includes(params.pubOrg))
    );
  });

  return {
    ...data,
    data: filteredData
  };
};

const openSSOLoginModal = () => {
  window.open(SSO_LOGIN_URL.value, "_blank");
};

// 下载文件
const downloadFile = (row: dccEprosList.Item) => {
  const fileUrl = row.url;
  if (fileUrl) {
    window.open(fileUrl, "_blank");
  } else {
    ElMessage.warning("当前行没有可下载的文件地址");
  }
};
</script>

<style scoped>
.document-tabs {
  margin-bottom: 16px;
  background-color: white;
  padding: 10px;
}
.document-tabs ::v-deep .el-tabs__item {
  font-size: 14px; /* 设置字体大小 */
}
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.block-tip {
  margin: 10px 0;
  font-weight: bold;
}

.draggable-modal .el-dialog__header {
  cursor: move;
  user-select: none;
}
</style>
