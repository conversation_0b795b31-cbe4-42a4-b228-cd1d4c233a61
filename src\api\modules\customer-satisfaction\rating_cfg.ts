import { ReqPage, ResPage } from "@/api/interface/index";
import { RatingCfg } from "@/typings/customer-satisfaction/rating_cfg";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/ratingCfg`;

// 列表
export const getRatingCfgList = (params?: ReqPage) => {
  return http.post<ResPage<RatingCfg.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getRatingCfgDetail = (id: number) => {
  return http.post<RatingCfg.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createRatingCfg = (data: RatingCfg.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editRatingCfg = (data: RatingCfg.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteRatingCfg = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportRatingCfg = (params?: RatingCfg.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importRatingCfg = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importRatingCfgTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
