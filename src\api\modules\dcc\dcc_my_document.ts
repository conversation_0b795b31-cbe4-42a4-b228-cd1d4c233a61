import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { dccLocalList } from "@/typings/dcc/dcc_my_document";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/documentMy`;
const workFlowUrl = `${API_PREFIX}/processDcc`;

// 未发布列表
export const getDccLocalDataUnpublished = (params?: ReqPage) => {
  return http.post<ResPage<dccLocalList.Item>>(`${baseUrl}/unapprovedList`, params);
};

// 已发布列表
export const getDccLocalDataPublished = (params?: ReqPage) => {
  return http.post<ResPage<dccLocalList.Item>>(`${baseUrl}/approvedList`, params);
};

// 查询所有列表
export const getDccLocalDataAll = (params?: ReqPage) => {
  return http.post<ResPage<dccLocalList.Item>>(`${baseUrl}/listAll`, params);
};

// 详情
export const getDccLocalListDetail = (id: number) => {
  return http.post<Partial<dccLocalList.Item>>(`${baseUrl}/get/${id}`);
};

// 新增
export const createDccLocalData = (data: dccLocalList.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editDccLocalData = (data: dccLocalList.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 提交
export const submitDccLocalData = (id: number, processName: string) => {
  return http.post(`${baseUrl}/submitToAudit`, { id, processName });
};

// 撤回
export const cancelDccLocalData = (id: number) => {
  return http.post(`${baseUrl}/recall/${id}`);
};

// 版本管理查询
export const getDccLocalListVersion = (id: number) => {
  return http.post<Partial<dccLocalList.VersionParams>>(`${baseUrl}/queryDetail/${id}`);
};

// 更新版本保存
export const updateDccLocalData = (data: dccLocalList.Item) => {
  return http.post(`${baseUrl}/createUpdateVersion`, data);
};

// 版本删除
export const deleteDccLocalVersion = (ids: number[]) => {
  return http.post(`${baseUrl}/delDetail`, { ids });
};

// 删除
export const deleteDccLocalData = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 获取所有的流程
export const getWorkflowList = () => {
  return http.post<dccLocalList.Item>(`${workFlowUrl}/allNameList`);
};
