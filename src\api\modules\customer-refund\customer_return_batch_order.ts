import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerReturnBatchOrder } from "@/typings/customer-refund/customer_return_batch_order";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/customer-refund/statistics";

const baseUrl = `${API_PREFIX}/CustomerReturnBatchOrder`;

// 列表
export const getCustomerReturnBatchOrderList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerReturnBatchOrder.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerReturnBatchOrderDetail = (id: number) => {
  return http.post<CustomerReturnBatchOrder.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerReturnBatchOrder = (data: CustomerReturnBatchOrder.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerReturnBatchOrder = (data: CustomerReturnBatchOrder.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerReturnBatchOrder = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerReturnBatchOrder = (params?: CustomerReturnBatchOrder.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerReturnBatchOrder = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerReturnBatchOrderTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 客退数量统计
export const statsCustomerReturnNumber = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ItemList[]>(`${baseUrl}/statsCustomerReturnNumber`, params);
};

// 退货金额统计
export const statsCustomerReturnCost = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ItemList[]>(`${baseUrl}/statsCustomerReturnCost`, params);
};
