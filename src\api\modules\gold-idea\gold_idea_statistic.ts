import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { Statistics } from "@/typings/gold-idea/statistics";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/proposal`;

// 获取提案个数By工厂
export const statsCountByFactory = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCount[]>(`${baseUrl}/countAnalysisByFactory`, params);
};

// 获取提案个数By部门
export const statsCountByDept = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCount[]>(`${baseUrl}/countAnalysisByDept`, params);
};

// 获取提案个数By状态
export const statsCountByStatus = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCount[]>(`${baseUrl}/countAnalysisByStatus`, params);
};

// 获取提案个数By改善类别
export const statsCountByImprovementType = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCount[]>(`${baseUrl}/countAnalysisByImprovementType`, params);
};

// 获取节约金额By工厂
export const statsSavingByFactory = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCost[]>(`${baseUrl}/savingAnalysisByFactory`, params);
};

// 获取节约金额By部门
export const statsSavingByDept = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCost[]>(`${baseUrl}/savingAnalysisByDept`, params);
};

// 获取节约金额By状态
export const statsSavingByStatus = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCost[]>(`${baseUrl}/savingAnalysisByStatus`, params);
};

// 获取节约金额By改善类别
export const statsSavingByImprovementType = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsCost[]>(`${baseUrl}/savingAnalysisByImprovementType`, params);
};
