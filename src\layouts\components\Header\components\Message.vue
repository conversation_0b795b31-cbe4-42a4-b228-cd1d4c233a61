<template>
  <div class="message">
    <el-popover placement="bottom" :width="350" trigger="click">
      <template #reference>
        <el-badge :value="pendingCount" class="item">
          <i :class="'iconfont icon-xiaoxi'" class="toolBar-icon"></i>
        </el-badge>
      </template>
      <el-tabs v-model="activeName" @tab-click="onTabChange">
        <el-tab-pane :label="`${$t('待处理')}(${pendingCount})`" name="first">
          <div v-if="isLoadingPending" class="message-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>{{ $t("加载中") }}...</span>
          </div>
          <div v-else>
            <div v-if="pendingList.length > 0" class="message-list">
              <div v-for="item in pendingList" :key="item.id" class="message-item" @click="handleTodoClick(item)">
                <img src="@/assets/images/msg01.png" alt="待处理" class="message-icon" />
                <div class="message-content">
                  <span class="message-title">{{ item.title }}</span>
                  <span class="message-date">{{ formatTime(item.createdTime) }}</span>
                </div>
              </div>
            </div>
            <div v-else class="message-empty">
              <img src="@/assets/images/notData.png" alt="notData" />
              <div>{{ $t("暂无待处理") }}</div>
            </div>
            <el-pagination
              v-if="pendingCount > pageSize"
              :current-page="pendingPage"
              :page-size="pageSize"
              :total="pendingCount"
              @current-change="onPendingPageChange"
              small
              layout="prev, pager, next"
              background
              class="message-pagination"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane :label="`${$t('已处理')}(${processedCount})`" name="second">
          <div v-if="isLoadingProcessed" class="message-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>{{ $t("加载中") }}...</span>
          </div>
          <div v-else>
            <div v-if="processedList.length > 0" class="message-list">
              <div v-for="item in processedList" :key="item.id" class="message-item processed" @click="handleTodoClick(item)">
                <img src="@/assets/images/msg04.png" alt="已处理" class="message-icon" />
                <div class="message-content">
                  <span class="message-title">{{ item.title }}</span>
                  <span class="message-date">{{ formatTime(item.createdTime) }}</span>
                </div>
              </div>
            </div>
            <div v-else class="message-empty">
              <img src="@/assets/images/notData.png" alt="notData" />
              <div>{{ $t("暂无已处理") }}</div>
            </div>
            <el-pagination
              v-if="processedCount > pageSize"
              :current-page="processedPage"
              :page-size="pageSize"
              :total="processedCount"
              @current-change="onProcessedPageChange"
              layout="prev, pager, next"
              small
              background
              class="message-pagination"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { getTodoList } from "@/api/modules/todo";
import type { TodoItem } from "@/typings/todo";
import { Loading } from "@element-plus/icons-vue";
import router from "@/routers";

const activeName = ref("first");
const pendingList = ref<TodoItem[]>([]);
const processedList = ref<TodoItem[]>([]);
const pendingCount = ref(0);
const processedCount = ref(0);
const isLoadingPending = ref(false);
const isLoadingProcessed = ref(false);
const pageSize = 6;
const pendingPage = ref(1);
const processedPage = ref(1);
let timer: NodeJS.Timeout | null = null;
let retryCount = 0;
const MAX_RETRY_COUNT = 3;

const fetchList = async (status: number, page = 1) => {
  if (status === 0 && isLoadingPending.value) return;
  if (status === 1 && isLoadingProcessed.value) return;
  try {
    if (status === 0) isLoadingPending.value = true;
    if (status === 1) isLoadingProcessed.value = true;
    const {
      data: {
        list: { list },
        pending,
        processed
      }
    } = await getTodoList({ pageNum: page, pageSize, status });
    if (status === 0) {
      pendingList.value = list || [];
      pendingCount.value = pending ?? 0;
      pendingPage.value = page;
      processedCount.value = processed ?? 0;
    } else {
      processedList.value = list || [];
      processedCount.value = processed ?? 0;
      processedPage.value = page;
    }
    retryCount = 0;
  } catch (error) {
    if (retryCount < MAX_RETRY_COUNT) {
      retryCount++;
      setTimeout(() => {
        fetchList(status, page);
      }, 5000 * retryCount);
    }
  } finally {
    if (status === 0) isLoadingPending.value = false;
    if (status === 1) isLoadingProcessed.value = false;
  }
};

const onPendingPageChange = (page: number) => fetchList(0, page);
const onProcessedPageChange = (page: number) => fetchList(1, page);

const onTabChange = (tab: any) => {
  if (tab.paneName === "first") {
    fetchList(0, 1);
  } else if (tab.paneName === "second") {
    fetchList(1, 1);
  }
};

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const handleTodoClick = async (item: TodoItem) => {
  try {
    if (item.url) {
      if (item.url.startsWith("http")) {
        window.open(item.url, "_blank");
      } else {
        router.push(item.url);
      }
    }
  } catch (error) {
    console.error("处理待办点击失败:", error);
  }
};

const startTimer = () => {
  fetchList(0, 1);
  timer = setInterval(
    () => {
      if (!document.hidden) {
        fetchList(0, pendingPage.value);
      }
    },
    5 * 60 * 1000
  );
};

const stopTimer = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
};

const handleVisibilityChange = () => {
  if (!document.hidden) {
    fetchList(0, pendingPage.value);
  }
};

onMounted(() => {
  fetchList(0, 1);
  startTimer();
  document.addEventListener("visibilitychange", handleVisibilityChange);
});

onUnmounted(() => {
  stopTimer();
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});
</script>

<style scoped lang="scss">
.message-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 550px;
  color: var(--el-text-color-secondary);
  .el-icon {
    margin-bottom: 10px;
    font-size: 24px;
  }
}
.message-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 550px;
  line-height: 45px;
}
.message-list {
  display: flex;
  flex-direction: column;
  max-height: 550px;
  overflow-y: auto;
  .message-item {
    display: flex;
    align-items: center;
    padding: 15px 5px 15px 0;
    cursor: pointer;
    border-bottom: 1px solid var(--el-border-color-light);
    transition: background-color 0.2s;
    &:hover {
      background-color: var(--el-fill-color-light);
    }
    &:last-child {
      border: none;
    }
    .message-icon {
      width: 20px;
      height: 20px;
      margin: 0 10px 0 5px;
    }
    &.processed .message-icon,
    &.processed i.iconfont {
      font-size: 20px;
      color: #bcbcbc;
    }
    .message-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      min-width: 0;
      .message-title {
        margin-bottom: 5px;
        overflow: hidden;
        font-size: 12px;
        line-height: 1.4;
        color: var(--el-text-color-primary);
        text-overflow: ellipsis;
      }
      .message-date {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
.message-pagination {
  display: flex;
  justify-content: flex-end;
  margin: 8px 0 0;
}

// 自定义滚动条样式
.message-list::-webkit-scrollbar {
  width: 4px;
}
.message-list::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 2px;
}
.message-list::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 2px;
}
.message-list::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}
</style>
