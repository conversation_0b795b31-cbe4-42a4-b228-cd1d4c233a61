<template>
  <div class="table-box flex flex-col">
    <!-- 搜索栏 -->
    <div class="search-box">
      <el-form :model="searchForm" inline>
        <el-form-item label="检验日期">
          <div class="date-range-container">
            <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              placeholder="开始日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 140px"
            />
            <span class="date-separator">-</span>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 140px"
            />
          </div>
        </el-form-item>
        <el-form-item label="工厂/部门">
          <el-select
            v-model="searchForm.plant"
            placeholder="请选择工厂/部门"
            style="width: 150px"
            clearable
            @change="onPlantChange"
          >
            <el-option v-for="item in factoryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料类别">
          <el-select
            v-model="searchForm.materialType[0]"
            placeholder="请选择物料类别"
            style="width: 200px"
            clearable
            @change="val => onMaterialTypeChange(val ? [val] : [])"
          >
            <el-option v-for="item in materialTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select
            v-model="searchForm.supplierName[0]"
            placeholder="请选择供应商"
            style="width: 200px"
            clearable
            @change="val => (searchForm.supplierName = val ? [val] : [])"
          >
            <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物料紧急度">
          <el-select v-model="searchForm.urgency" placeholder="请选择紧急度" style="width: 150px" clearable>
            <el-option v-for="item in iqc_material_level" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!--         <el-form-item label="物料名称">
          <el-input v-model="searchForm.materialName" placeholder="请输入物料名称" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="物料编码">
          <el-input v-model="searchForm.partNo" placeholder="请输入物料编码" style="width: 150px" clearable />
        </el-form-item> -->
        <!--         <el-form-item label="检验单号">
          <el-input v-model="searchForm.orderNo" placeholder="请输入检验单号" style="width: 150px" clearable />
        </el-form-item> -->
        <el-form-item label="检验方式">
          <el-select v-model="searchForm.checkMethod" placeholder="请选择检验方式" style="width: 150px" clearable>
            <el-option v-for="item in iqc_inspection_type" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 图表展示区域 -->
    <el-tabs v-model="activeName" type="card" class="document-tabs">
      <el-tab-pane name="material-stats">
        <template #label>
          <el-icon style="margin-right: 8px"><Histogram /></el-icon>
          <span>物料类别统计</span>
        </template>

        <!-- 时间单位选择和物料类别筛选 -->
        <div class="w-full flex justify-between items-center px-10 mb-4">
          <div class="flex items-center">
            <span class="mr-2">物料类别:</span>
            <el-select
              v-model="materialStatsFilter"
              placeholder="请选择物料类别"
              multiple
              style="width: 300px"
              collapse-tags
              collapse-tags-tooltip
              @change="onMaterialStatsFilterChange"
            >
              <template #header>
                <el-button text @click="selectAllMaterialStatsFilter">全选</el-button>
                <el-button text @click="clearAllMaterialStatsFilter">清空</el-button>
              </template>
              <el-option v-for="item in materialTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <el-radio-group v-model="statsType" @change="handleStatsTypeChange">
            <el-radio :label="1">周</el-radio>
            <el-radio :label="2">月</el-radio>
            <el-radio :label="3">季度</el-radio>
            <el-radio :label="4">年</el-radio>
          </el-radio-group>
        </div>

        <!-- 按物料大类统计的图表 -->
        <div class="charts-container">
          <div class="chart-card">
            <BatchQualifyRateByMaterial :chart-data="batchQualifyRateByMaterialData" :search-params="searchParams" />
          </div>
          <div class="chart-card">
            <DppmByMaterial :chart-data="dppmByMaterialData" :search-params="searchParams" />
          </div>
          <div class="chart-card">
            <TimelinessRateByMaterial :chart-data="timelinessRateByMaterialData" :search-params="searchParams" />
          </div>
          <div class="chart-card">
            <BatchFailRateByMaterial :chart-data="batchFailRateByMaterialData" :search-params="searchParams" />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane name="supplier-stats">
        <template #label>
          <el-icon style="margin-right: 8px"><User /></el-icon>
          <span>供应商统计</span>
        </template>

        <!-- 时间单位选择和供应商筛选 -->
        <div class="w-full flex justify-between items-center px-10 mb-4">
          <div class="flex items-center">
            <span class="mr-2">供应商:</span>
            <el-select
              v-model="supplierStatsFilter"
              placeholder="请选择供应商"
              multiple
              style="width: 300px"
              collapse-tags
              collapse-tags-tooltip
              @change="onSupplierStatsFilterChange"
            >
              <template #header>
                <el-button text @click="selectAllSupplierStatsFilter">全选</el-button>
                <el-button text @click="clearAllSupplierStatsFilter">清空</el-button>
              </template>
              <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <el-radio-group v-model="statsType" @change="handleStatsTypeChange">
            <el-radio :label="1">周</el-radio>
            <el-radio :label="2">月</el-radio>
            <el-radio :label="3">季度</el-radio>
            <el-radio :label="4">年</el-radio>
          </el-radio-group>
        </div>

        <!-- 按供应商统计的图表 -->
        <div class="charts-container">
          <div class="chart-card">
            <BatchQualifyRateBySupplier :chart-data="batchQualifyRateBySupplierData" :search-params="searchParams" />
          </div>
          <div class="chart-card">
            <DppmBySupplier :chart-data="dppmBySupplierData" :search-params="searchParams" />
          </div>
          <div class="chart-card">
            <TimelinessRateBySupplier :chart-data="timelinessRateBySupplierData" :search-params="searchParams" />
          </div>
          <div class="chart-card">
            <BatchFailRateBySupplier :chart-data="batchFailRateBySupplierData" :search-params="searchParams" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="tsx" name="iqc-data-analysis">
import { ref, reactive, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { Histogram, User } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
dayjs.extend(quarterOfYear);
import {
  statsBatchQualifyRate,
  statsBySupplierBatchQualifyRate,
  statsDppmRate,
  statsDppmBySupplierRate,
  statsTimelinessRate,
  statsTimelinessBySupplierRate,
  statsBatchFailRate,
  statsBatchFailBySupplierRate,
  getSupplierData
} from "@/api/modules/iqc/data_analysis";
import { DataAnalysis } from "@/typings/iqc/data_analysis";
import { useAdminDict, useDict } from "@/hooks/useDict";
import BatchQualifyRateByMaterial from "./components/BatchQualifyRateByMaterial.vue";
import DppmByMaterial from "./components/DppmByMaterial.vue";
import TimelinessRateByMaterial from "./components/TimelinessRateByMaterial.vue";
import BatchFailRateByMaterial from "./components/BatchFailRateByMaterial.vue";
import BatchQualifyRateBySupplier from "./components/BatchQualifyRateBySupplier.vue";
import DppmBySupplier from "./components/DppmBySupplier.vue";
import TimelinessRateBySupplier from "./components/TimelinessRateBySupplier.vue";
import BatchFailRateBySupplier from "./components/BatchFailRateBySupplier.vue";

const { t } = useI18n();
const { factory } = useAdminDict("factory");

// 字典项配置
const { iqc_material_level, iqc_inspection_type, iqc_inspection_result, iqc_inspection_status } = useDict(
  "iqc_material_level",
  "iqc_inspection_type",
  "iqc_inspection_result",
  "iqc_inspection_status"
);
// 当前激活的标签页
const activeName = ref("material-stats");

// 统计类型：0=日,1=周,2=月,3=季度,4=年
const statsType = ref<number>(2); // 默认月

// 搜索表单
const searchForm = reactive({
  startDate: dayjs().subtract(6, "month").format("YYYY-MM-DD"),
  endDate: dayjs().format("YYYY-MM-DD"),
  plant: "深圳", // 默认深圳
  materialType: [] as string[],
  supplierName: [] as string[],
  urgency: "",
  materialName: "",
  partNo: "",
  orderNo: "",
  checkMethod: ""
});

// 搜索参数（用于传递给图表组件）
const searchParams = ref<DataAnalysis.IQueryParams>({} as DataAnalysis.IQueryParams);

// 下拉选项
const factoryOptions = computed(() => factory.value.map(item => ({ label: item.label, value: item.value })));
const materialTypeOptions = ref<{ label: string; value: string }[]>([]);
const supplierOptions = ref<{ label: string; value: string }[]>([]);

// 图表内部筛选项
const materialStatsFilter = ref<string[]>([]);
const supplierStatsFilter = ref<string[]>([]);

// 图表数据 - 按物料大类
const batchQualifyRateByMaterialData = ref<DataAnalysis.BatchQualifyRateByMaterialGroup[]>([]);
const dppmByMaterialData = ref<DataAnalysis.DppmByMaterialGroup[]>([]);
const timelinessRateByMaterialData = ref<DataAnalysis.TimelinessRateByMaterialGroup[]>([]);
const batchFailRateByMaterialData = ref<DataAnalysis.BatchFailRateByMaterial[]>([]);

// 图表数据 - 按供应商
const batchQualifyRateBySupplierData = ref<DataAnalysis.BatchQualifyRateBySupplierGroup[]>([]);
const dppmBySupplierData = ref<DataAnalysis.DppmBySupplierGroup[]>([]);
const timelinessRateBySupplierData = ref<DataAnalysis.TimelinessRateBySupplierGroup[]>([]);
const batchFailRateBySupplierData = ref<DataAnalysis.BatchFailRateBySupplier[]>([]);

// 日期转换函数
const convertDateByStatsType = (date: string, statsType: number) => {
  const dayObj = dayjs(date);

  switch (statsType) {
    case 1: // 周
      const weekNumber = dayObj.week().toString().padStart(2, "0");
      return `${dayObj.year()}-${weekNumber}`;
    case 2: // 月
      return dayObj.format("YYYY-MM");
    case 3: // 季度
      return `${dayObj.year()}-Q${dayjs(date).quarter()}`;
    case 4: // 年
      return dayObj.format("YYYY");
    default: // 日
      return dayObj.format("YYYY-MM-DD");
  }
};

// 工厂变更处理
const onPlantChange = async () => {
  if (!searchForm.plant) return;

  try {
    const { data } = await getSupplierData({
      condition: { plant: searchForm.plant },
      pageNum: 1,
      pageSize: 1000
    });

    // 更新物料类别选项
    const materialTypes = [...new Set(data.list.map(item => item.materialType))];
    materialTypeOptions.value = materialTypes.map(type => ({ label: type, value: type }));

    // 默认不选择物料类别（为空）
    searchForm.materialType = [];
    materialStatsFilter.value = materialTypes; // 图表内部筛选默认选择所有

    await loadAllSuppliers();
  } catch (error) {
    console.error("获取物料类别失败:", error);
    //ElMessage.error("获取物料类别失败");
  }
};

// 物料类别变更处理
const onMaterialTypeChange = async (materialTypes: string[]) => {
  if (!searchForm.plant) return;

  try {
    // 获取供应商数据
    const { data } = await getSupplierData({
      condition: {
        plant: searchForm.plant,
        materialType: materialTypes.length > 0 ? materialTypes.join(",") : undefined
      },
      pageNum: 1,
      pageSize: 1000
    });

    if (!data?.list) {
      throw new Error("获取供应商数据失败");
    }

    // 更新供应商选项（去重）
    const suppliers = [...new Set(data.list.map(item => item.supplierName))];
    supplierOptions.value = suppliers.map(supplier => ({ label: supplier, value: supplier }));

    // 默认不选择供应商（为空）
    searchForm.supplierName = [];
  } catch (error) {
    console.error("获取供应商失败:", error);
    //ElMessage.error("获取供应商失败");
    supplierOptions.value = [];
    searchForm.supplierName = [];
  }
};

// 加载所有供应商（当物料类别为空时）
const loadAllSuppliers = async () => {
  if (!searchForm.plant) return;

  try {
    const { data } = await getSupplierData({
      condition: { plant: searchForm.plant },
      pageNum: 1,
      pageSize: 1000
    });

    if (!data?.list) {
      throw new Error("获取供应商数据失败");
    }

    // 更新供应商选项（去重）
    const suppliers = [...new Set(data.list.map(item => item.supplierName))];
    supplierOptions.value = suppliers.map(supplier => ({ label: supplier, value: supplier }));

    // 默认不选择供应商（为空）
    searchForm.supplierName = [];
    supplierStatsFilter.value = suppliers; // 图表内部筛选默认选择所有
  } catch (error) {
    console.error("获取供应商失败:", error);
    //ElMessage.error("获取供应商失败");
    supplierOptions.value = [];
    searchForm.supplierName = [];
  }
};

// 统计类型变更处理
const handleStatsTypeChange = () => {
  if (searchParams.value.plant) {
    handleSearch();
  }
};

// 查询处理
const handleSearch = async () => {
  if (!searchForm.startDate || !searchForm.endDate || !searchForm.plant) {
    ElMessage.warning("请完善搜索条件（开始日期、结束日期、工厂/部门为必填项）");
    return;
  }

  // 根据统计类型转换日期格式
  const convertedStartDate = convertDateByStatsType(searchForm.startDate, statsType.value);
  const convertedEndDate = convertDateByStatsType(searchForm.endDate, statsType.value);

  // 构建查询参数
  const baseParams: DataAnalysis.IQueryParams = {
    statsType: statsType.value,
    statsDateStart: convertedStartDate,
    statsDateEnd: convertedEndDate,
    plant: searchForm.plant,
    materialType: searchForm.materialType.filter(Boolean), // 过滤掉null/undefined值
    supplierName: searchForm.supplierName,
    urgency: searchForm.urgency || undefined,
    materialName: searchForm.materialName || undefined,
    partNo: searchForm.partNo || undefined,
    orderNo: searchForm.orderNo || undefined,
    checkMethod: searchForm.checkMethod || undefined
  };

  // 初始化图表内部筛选项
  // 如果顶部搜索栏为空，则图表内部筛选项使用所有可用选项
  materialStatsFilter.value =
    searchForm.materialType.length > 0 ? searchForm.materialType : materialTypeOptions.value.map(item => item.value);
  supplierStatsFilter.value =
    searchForm.supplierName.length > 0 ? searchForm.supplierName : supplierOptions.value.map(item => item.value);

  searchParams.value = baseParams;
  await loadChartData();
};

// 重置处理
const handleReset = () => {
  searchForm.startDate = dayjs().subtract(6, "month").format("YYYY-MM-DD");
  searchForm.endDate = dayjs().format("YYYY-MM-DD");
  searchForm.materialType = [];
  searchForm.supplierName = [];
  searchForm.urgency = "";
  searchForm.materialName = "";
  searchForm.partNo = "";
  searchForm.orderNo = "";
  searchForm.checkMethod = "";
  statsType.value = 2;

  // 重置图表内部筛选项
  materialStatsFilter.value = [];
  supplierStatsFilter.value = [];

  // 保留工厂选择和选项数据
  if (searchForm.plant) {
    onPlantChange();
  }

  // 清空图表数据
  clearAllChartData();
  searchParams.value = {} as DataAnalysis.IQueryParams;
};

// 清空所有图表数据
const clearAllChartData = () => {
  batchQualifyRateByMaterialData.value = [];
  dppmByMaterialData.value = [];
  timelinessRateByMaterialData.value = [];
  batchFailRateByMaterialData.value = [];
  batchQualifyRateBySupplierData.value = [];
  dppmBySupplierData.value = [];
  timelinessRateBySupplierData.value = [];
  batchFailRateBySupplierData.value = [];
};

// 物料类别筛选处理函数
const onMaterialStatsFilterChange = () => {
  if (searchParams.value.plant) {
    // 更新查询参数中的物料类别
    searchParams.value.materialType = materialStatsFilter.value;
    loadChartData();
  }
};

// 供应商筛选处理函数
const onSupplierStatsFilterChange = () => {
  if (searchParams.value.plant) {
    // 更新查询参数中的供应商
    searchParams.value.supplierName = supplierStatsFilter.value;
    loadChartData();
  }
};

// 全选物料类别筛选
const selectAllMaterialStatsFilter = () => {
  materialStatsFilter.value = materialTypeOptions.value.map(item => item.value);
  onMaterialStatsFilterChange();
};

// 清空物料类别筛选
const clearAllMaterialStatsFilter = () => {
  materialStatsFilter.value = [];
  onMaterialStatsFilterChange();
};

// 全选供应商筛选
const selectAllSupplierStatsFilter = () => {
  supplierStatsFilter.value = supplierOptions.value.map(item => item.value);
  onSupplierStatsFilterChange();
};

// 清空供应商筛选
const clearAllSupplierStatsFilter = () => {
  supplierStatsFilter.value = [];
  onSupplierStatsFilterChange();
};

// 加载图表数据
const loadChartData = async () => {
  try {
    const [
      batchQualifyRateByMaterial,
      batchQualifyRateBySupplier,
      dppmByMaterial,
      dppmBySupplier,
      timelinessRateByMaterial,
      timelinessRateBySupplier,
      batchFailRateByMaterial,
      batchFailRateBySupplier
    ] = await Promise.all([
      statsBatchQualifyRate(searchParams.value),
      statsBySupplierBatchQualifyRate(searchParams.value),
      statsDppmRate(searchParams.value),
      statsDppmBySupplierRate(searchParams.value),
      statsTimelinessRate(searchParams.value),
      statsTimelinessBySupplierRate(searchParams.value),
      statsBatchFailRate(searchParams.value),
      statsBatchFailBySupplierRate(searchParams.value)
    ]);

    batchQualifyRateByMaterialData.value = batchQualifyRateByMaterial.data || [];
    batchQualifyRateBySupplierData.value = batchQualifyRateBySupplier.data || [];
    dppmByMaterialData.value = dppmByMaterial.data || [];
    dppmBySupplierData.value = dppmBySupplier.data || [];
    timelinessRateByMaterialData.value = timelinessRateByMaterial.data || [];
    timelinessRateBySupplierData.value = timelinessRateBySupplier.data || [];
    // 确保数据按不良率降序排序并只取前5条
    batchFailRateByMaterialData.value =
      batchFailRateByMaterial.data?.sort((a, b) => b.defectRate - a.defectRate).slice(0, 5) || [];
    batchFailRateBySupplierData.value =
      batchFailRateBySupplier.data?.sort((a, b) => b.defectRate - a.defectRate).slice(0, 5) || [];
  } catch (error) {
    console.error("加载图表数据失败:", error);
    ElMessage.error("加载图表数据失败");
  }
};

// 初始化
onMounted(async () => {
  // 初始化工厂选择
  await onPlantChange();
});
</script>

<style lang="scss" scoped>
.search-box {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-box .el-form {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.search-box .el-form-item {
  margin-bottom: 15px;
  margin-right: 20px;
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-separator {
  color: #606266;
  font-weight: 500;
  user-select: none;
}

.document-tabs {
  margin-bottom: 16px;
  background-color: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.document-tabs ::v-deep .el-tabs__item {
  font-size: 14px;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 0 20px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 400px;
}

@media (max-width: 1200px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
}
</style>
