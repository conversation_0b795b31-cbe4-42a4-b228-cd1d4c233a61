# HET2 项目配置文档

## 项目概述

**项目名称**: HET2 (和而泰二期)  
**项目版本**: 1.2.0  
**技术栈**: Vue3.3 + TypeScript + Vite4 + Pinia + Element-Plus  
**开发环境**: Node.js >= 16.0.0

## 项目架构

### 技术栈详情

| 技术         | 版本   | 用途       |
| ------------ | ------ | ---------- |
| Vue          | ^3.3.7 | 前端框架   |
| TypeScript   | ^5.2.2 | 类型系统   |
| Vite         | ^4.4.9 | 构建工具   |
| Element Plus | ^2.3.4 | UI组件库   |
| Pinia        | ^2.1.7 | 状态管理   |
| Vue Router   | ^4.2.5 | 路由管理   |
| Axios        | ^1.5.1 | HTTP客户端 |
| ECharts      | ^5.4.3 | 图表库     |
| Tailwind CSS | ^3.3.5 | CSS框架    |

### 核心依赖说明

#### 业务相关

- **@element-plus/icons-vue**: Element Plus 图标库
- **echarts**: 数据可视化图表
- **echarts-liquidfill**: 水球图组件
- **@vue-flow/core**: 流程图组件
- **@wangeditor/editor**: 富文本编辑器
- **exceljs**: Excel文件处理
- **html2canvas**: 页面截图
- **jspdf**: PDF生成

#### 工具库

- **dayjs**: 日期处理
- **lodash-es**: 工具函数库
- **crypto-js**: 加密解密
- **md5**: MD5加密
- **qs**: 查询字符串处理
- **mitt**: 事件总线

#### 开发工具

- **husky**: Git钩子
- **lint-staged**: 暂存文件检查
- **prettier**: 代码格式化
- **eslint**: 代码检查
- **stylelint**: 样式检查

## 项目结构

```
het2/
├── build/                    # 构建配置
│   ├── getEnv.ts            # 环境变量处理
│   ├── plugins.ts           # Vite插件配置
│   └── proxy.ts             # 代理配置
├── public/                   # 静态资源
├── src/
│   ├── api/                 # API接口管理
│   ├── assets/              # 静态资源文件
│   ├── components/          # 全局组件
│   ├── config/              # 全局配置
│   ├── directives/          # 全局指令
│   ├── enums/               # 枚举定义
│   ├── hooks/               # 自定义Hooks
│   ├── languages/           # 国际化
│   ├── layouts/             # 布局组件
│   ├── routers/             # 路由配置
│   ├── stores/              # Pinia状态管理
│   ├── styles/              # 全局样式
│   ├── typings/             # TypeScript类型定义
│   ├── utils/               # 工具函数
│   ├── views/               # 页面组件
│   ├── App.vue              # 根组件
│   └── main.ts              # 入口文件
├── .env                     # 环境变量
├── .env.development         # 开发环境变量
├── .env.production          # 生产环境变量
├── .env.test                # 测试环境变量
├── package.json             # 项目依赖
├── vite.config.ts           # Vite配置
├── tsconfig.json            # TypeScript配置
└── tailwind.config.js       # Tailwind配置
```

## 系统环境要求

### 操作系统支持

- **Windows**: Windows 10/11 (64位)
- **macOS**: macOS 10.15+ (Catalina及以上版本)
- **Linux**: Ubuntu 18.04+, CentOS 7+, 或其他主流Linux发行版

### 硬件要求

#### 最低配置

- **CPU**: 双核 2.0GHz 或更高
- **内存**: 4GB RAM
- **存储**: 2GB 可用磁盘空间
- **网络**: 稳定的互联网连接

#### 推荐配置

- **CPU**: 四核 2.5GHz 或更高
- **内存**: 8GB RAM 或更高
- **存储**: 5GB 可用磁盘空间 (SSD推荐)
- **网络**: 宽带互联网连接

### 软件环境要求

#### 必需软件

- **Node.js**: >= 16.0.0 (推荐使用 LTS 版本)
- **包管理器**: pnpm >= 7.0.0 (推荐) 或 npm >= 8.0.0
- **Git**: >= 2.20.0 (用于版本控制)

#### 开发工具 (推荐)

- **IDE**: Visual Studio Code (推荐) 或 WebStorm
- **浏览器**: Chrome >= 90, Firefox >= 88, Safari >= 14, Edge >= 90

#### VS Code 推荐插件

- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier - Code formatter
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens

### 数据库要求 (如适用)

- **MySQL**: >= 5.7 或 >= 8.0
- **PostgreSQL**: >= 12.0
- **Redis**: >= 6.0 (用于缓存)

### 服务器环境 (生产部署)

#### Web服务器

- **Nginx**: >= 1.18 (推荐)

#### 应用服务器

- **服务器1**: ***********,
- **服务器2**: ***********,
- **端口**: 22
- **账号**: root
- **密码**: het_qms2023
- **Node.js**: >= 16.0.0 (用于SSR或API服务)
- **PM2**: >= 5.0.0 (Node.js进程管理器)
- **Java**: >= 8 (如果后端使用Java)
  - **Spring Boot**: >= 2.5.0

#### 数据库服务器

- **服务器2**: ***********,
- **端口**: 3306
- **账号**: root
- **密码**: Het@12345

- **MySQL**: >= 5.7 或 >= 8.0
  - **配置要求**:
    - 内存: >= 4GB (推荐8GB+)
    - 存储: SSD推荐，至少50GB可用空间
    - 连接数: max_connections >= 500
    - 字符集: utf8mb4
- **Redis**: >= 6.0 (缓存/会话存储)
  - **配置要求**:
    - 内存: >= 2GB (推荐4GB+)
    - 持久化: AOF + RDB
    - 最大内存策略: allkeys-lru

#### 邮件服务器

- **SMTP服务器配置**:
- **地址**: smtphz.qiye.163.com
- **端口**: 994
- **账号**: <EMAIL>
- **密码**: Wyyx2023
- **认证**: 支持SMTP AUTH
- **加密**: 支持SSL/TLS

### 环境检查命令

在开始开发前，请使用以下命令检查您的环境是否满足要求：

```bash
# 检查 Node.js 版本
node --version

# 检查 npm 版本
npm --version

# 检查 pnpm 版本 (如果使用)
pnpm --version

# 检查 Git 版本
git --version
```

### 环境安装指南

#### 安装 Node.js

1. 访问 [Node.js 官网](https://nodejs.org/)
2. 下载并安装 LTS 版本
3. 验证安装：`node --version`

#### 安装 pnpm (推荐)

```bash
# 使用 npm 安装 pnpm
npm install -g pnpm

# 或使用官方安装脚本
curl -fsSL https://get.pnpm.io/install.sh | sh -

# Windows 用户可使用
iwr https://get.pnpm.io/install.ps1 -useb | iex
```

#### 安装 Git

- **Windows**: 下载 [Git for Windows](https://git-scm.com/download/win)
- **macOS**: 使用 Homebrew `brew install git` 或下载官方安装包
- **Linux**: 使用包管理器 `sudo apt install git` (Ubuntu) 或 `sudo yum install git` (CentOS)

#### VS Code 安装与配置

1. 下载并安装 [Visual Studio Code](https://code.visualstudio.com/)
2. 安装推荐的插件列表
3. 配置用户设置以获得最佳开发体验

## 环境配置

### 开发环境要求

- Node.js >= 16.0.0
- pnpm (推荐) 或 npm
- 现代浏览器 (Chrome, Firefox, Safari, Edge)

### 环境变量配置

项目支持多环境配置，通过不同的 `.env` 文件管理：

- `.env` - 通用环境变量
- `.env.development` - 开发环境
- `.env.test` - 测试环境
- `.env.production` - 生产环境

主要环境变量：

```bash
VITE_PORT=3000                    # 开发服务器端口
VITE_OPEN=true                    # 是否自动打开浏览器
VITE_PUBLIC_PATH=/                # 公共路径
VITE_DROP_CONSOLE=false           # 是否移除console
VITE_PROXY=[["/api","http://localhost:8080"]]  # 代理配置
```

## 构建配置

### Vite 配置说明

**路径别名**:

```typescript
resolve: {
  alias: {
    "@": resolve(__dirname, "./src"),
    "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js"
  }
}
```

**开发服务器**:

```typescript
server: {
  host: "0.0.0.0",
  port: viteEnv.VITE_PORT,
  open: viteEnv.VITE_OPEN,
  cors: true,
  proxy: createProxy(viteEnv.VITE_PROXY)
}
```

**构建优化**:

```typescript
build: {
  outDir: "dist",
  minify: "esbuild",
  sourcemap: false,
  reportCompressedSize: false,
  chunkSizeWarningLimit: 2000
}
```

### 插件配置

项目使用的主要Vite插件：

- `@vitejs/plugin-vue` - Vue支持
- `@vitejs/plugin-vue-jsx` - JSX支持
- `vite-plugin-eslint` - ESLint集成
- `vite-plugin-svg-icons` - SVG图标
- `vite-plugin-pwa` - PWA支持
- `vite-plugin-compression` - 压缩
- `unplugin-vue-setup-extend-plus` - setup语法扩展

## 代码规范

### ESLint 配置

- 基于 `@typescript-eslint` 规则
- 集成 Vue3 专用规则
- 支持 Prettier 格式化

### Prettier 配置

- 统一代码格式化标准
- 支持多种文件类型
- 与ESLint无冲突集成

### Stylelint 配置

- SCSS/CSS代码规范
- 支持Vue单文件组件
- 属性排序规范

### Git 提交规范

- 使用 `husky` + `lint-staged` 预提交检查
- `commitlint` 提交信息规范
- `cz-git` 交互式提交

提交类型：

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建/工具相关

## 脚本命令

### 开发相关

```bash
pnpm dev          # 启动开发服务器
pnpm serve        # 启动开发服务器(别名)
pnpm preview      # 预览构建结果
```

### 构建相关

```bash
pnpm build:dev    # 开发环境构建
pnpm build:test   # 测试环境构建
pnpm build:pro    # 生产环境构建
```

### 代码检查

```bash
pnpm lint:eslint    # ESLint检查
pnpm lint:prettier  # Prettier格式化
pnpm lint:stylelint # Stylelint检查
pnpm type:check     # TypeScript类型检查
```

### 版本管理

```bash
pnpm release      # 自动版本发布
pnpm commit       # 规范化提交
```

## 浏览器支持

### 开发环境

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

### 生产环境

- 现代浏览器 (支持 ES Module)
- 不支持 IE 浏览器
- 移动端浏览器支持

## 部署配置

### 应用发布路径配置

#### 开发环境路径

```bash
# 本地开发服务器
http://localhost:3000/

# 开发环境API地址
http://dev-api.het.com/api/

# 开发环境静态资源路径
http://localhost:3000/assets/
```

#### 测试环境路径

```bash
# 测试环境应用地址
http://test.het.com/

# 测试环境API地址
http://test-api.het.com/api/

# 测试环境静态资源路径
http://test.het.com/assets/

# 测试环境部署路径
/var/www/het-test/
```

#### 生产环境路径

```bash
# 生产环境应用地址
https://het.com/

# 生产环境API地址
https://api.het.com/api/

# 生产环境静态资源路径
https://cdn.het.com/assets/

# 生产环境部署路径
/var/www/het-prod/
```

#### 服务器目录结构

```
/var/www/het-prod/
├── current/                 # 当前版本软链接
├── releases/               # 历史版本目录
│   ├── 20250128-001/      # 版本时间戳目录
│   ├── 20250128-002/
│   └── ...
├── shared/                 # 共享文件目录
│   ├── logs/              # 日志文件
│   ├── uploads/           # 上传文件
│   └── config/            # 配置文件
└── backup/                # 备份目录
```

#### Nginx 配置示例

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name het.com www.het.com;

    # SSL 配置
    ssl_certificate /etc/ssl/certs/het.com.crt;
    ssl_certificate_key /etc/ssl/private/het.com.key;

    # 应用根目录
    root /var/www/het-prod/current;
    index index.html;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # API 代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

#### Docker 部署配置

```yaml
# docker-compose.yml
version: "3.8"
services:
  het-frontend:
    image: het-frontend:latest
    container_name: het-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
      - ./logs:/var/log/nginx
    restart: unless-stopped

  het-backend:
    image: het-backend:latest
    container_name: het-backend
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://user:pass@db:3306/het
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    container_name: het-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=het
      - MYSQL_USER=hetuser
      - MYSQL_PASSWORD=hetpassword
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    container_name: het-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 构建产物

```
dist/
├── assets/
│   ├── css/         # 样式文件
│   ├── js/          # JavaScript文件
│   └── [ext]/       # 其他资源文件
├── index.html       # 入口HTML
└── ...              # 其他静态资源
```

### 部署注意事项

1. 确保服务器支持 History 路由模式
2. 配置正确的 `publicPath`
3. 设置合适的缓存策略
4. 配置 gzip/brotli 压缩
5. 配置 SSL 证书和 HTTPS 重定向
6. 设置适当的安全头
7. 配置日志轮转和监控

## 开发指南

### 快速开始

```bash
# 1. 克隆项目
git clone [repository-url]

# 2. 安装依赖
pnpm install

# 3. 启动开发服务器
pnpm dev

# 4. 打开浏览器访问 http://localhost:3000
```

### 开发规范

1. 使用 TypeScript 进行类型约束
2. 遵循 Vue3 Composition API 规范
3. 组件命名采用 PascalCase
4. 文件命名采用 kebab-case
5. 使用 ESLint + Prettier 保持代码风格一致

### 常见问题

1. **端口冲突**: 修改 `.env.development` 中的 `VITE_PORT`
2. **代理失败**: 检查 `VITE_PROXY` 配置
3. **类型错误**: 运行 `pnpm type:check` 检查类型
4. **构建失败**: 检查依赖版本兼容性

## 更新日志

详见 [CHANGELOG.md](./CHANGELOG.md)

## 许可证

MIT License

---

**文档版本**: 1.0  
**更新时间**: 2025-01-28  
**维护人员**: 开发团队
