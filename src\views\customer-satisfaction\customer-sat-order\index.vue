<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <template #tableHeader="{ selectedListIds }">
        <el-button
          v-auth="'customer-satisfaction-customer-sat-order:export'"
          type="primary"
          @click="downloadFile(selectedListIds as number[])"
          >{{ $t("导出") }}
        </el-button>
        <el-button
          v-auth="'customer-satisfaction-customer-sat-order:add'"
          type="primary"
          @click="openCustomerSatOrderModal('新增')"
          >{{ $t("新增") }}
        </el-button>
        <el-button
          v-auth="'customer-satisfaction-customer-sat-order:edit'"
          type="primary"
          @click="openCustomerSatOrderModal('编辑')"
          >{{ $t("编辑") }}
        </el-button>
        <el-button type="primary" @click="openPreview()">{{ $t("预览") }}</el-button>
        <el-button
          v-auth="'customer-satisfaction-customer-sat-order:delete'"
          type="danger"
          @click="batchDelete(selectedListIds as number[])"
          >{{ $t("删除") }}
        </el-button>
        <el-checkbox v-model="my" :label="$t('只看我的')" style="margin-left: 10px" />
      </template>
    </ProTable>
    <CustomerSatOrderModal ref="CustomerSatOrderModalRef" />
    <CustomerSatOrderViewModal ref="CustomerSatOrderViewModalRef" />
  </div>
</template>

<script setup lang="tsx" name="PostTable">
import {
  getCustomerSatOrderList,
  createCustomerSatOrder,
  editCustomerSatOrder,
  deleteCustomerSatOrder,
  exportCustomerSatOrder
} from "@/api/modules/customer-satisfaction/customer_sat_order";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import CustomerSatOrderModal from "./components/CustomerSatOrderModal.vue";
import { download } from "@/api/modules/common";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAdminDict } from "@/hooks/useDict";
import { CustomerSatOrder } from "@/typings/customer-satisfaction/customer_sat_order";
import { isEmpty } from "@/utils/is";
import DateRange from "../../components/DateRange.vue";
import { ref, reactive, onMounted, computed, watch } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import { OrderStatus, IsExpired, RatingType, LangType } from "@/enums/statusCustomerSatisfaction";
import { getLangTemplateList } from "@/utils/langTemplate";
import { getCustomerList, customerList } from "@/utils/customer";
import { getBuList, buList } from "@/utils/bu";
import CustomerSatOrderViewModal from "./components/CustomerSatOrderViewModal.vue";
import LinkModal from "../../components/LinkModal.vue";
import Link from "../../components/Link.vue";

const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();
const { t } = useI18n();
const my = ref(localStorage.getItem("my") === "true" ? true : false);

const CustomerSatOrderModalRef = ref<InstanceType<typeof CustomerSatOrderModal> | null>(null);
const CustomerSatOrderViewModalRef = ref<InstanceType<typeof CustomerSatOrderViewModal> | null>(null);
let queryParams = reactive<CustomerSatOrder.IQueryParams>({} as CustomerSatOrder.IQueryParams);

const initParam = reactive({});
const { factory } = useAdminDict("factory");

const customerPlants = computed(() => {
  return customerList.value.map(item => {
    return {
      label: item.customerPlant,
      value: item.customerPlant
    };
  });
});

const columns = reactive<ColumnProps<CustomerSatOrder.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "orderNo",
    label: "单据编号",
    width: 180,
    search: { order: 2, el: "input", props: { filterable: true } },
    render({ row }) {
      return <LinkModal name={row.orderNo} row={row} onOpenDialog={openPreview} />;
    }
  },
  {
    prop: "orderStatus",
    label: "状态",
    tag: true,
    enum: OrderStatus,
    fieldNames: { label: "label", value: "label" },
    width: 120
  },
  { prop: "isExpired", label: "是否逾期", width: 100, enum: IsExpired, tag: true },
  {
    prop: "hetPlant",
    label: "和而泰工厂",
    width: 120,
    enum: factory,
    fieldNames: { label: "label", value: "label" },
    search: { order: 4, el: "select", props: { filterable: true } }
  },
  {
    prop: "hetBU",
    label: "BU",
    width: 120,
    enum: buList,
    search: { el: "select", order: 5, props: { filterable: true } },
    fieldNames: { label: "bu", value: "bu" }
  },
  {
    prop: "customerName",
    label: "客户名",
    width: 120,
    search: {
      el: "select",
      order: 2,
      props: { filterable: true }
      // render: props => {
      //   return (
      //     <el-select v-model={props.customerName} placeholder="请选择客户名" onChange={tt}>
      //       {customerList.value.map(item => (
      //         <el-option label={item.customerName} value={item.customerName} />
      //       ))}
      //     </el-select>
      //   );
      // }
    },
    enum: customerList,
    fieldNames: { label: "customerName", value: "customerName" }
  },
  {
    prop: "customerPlant",
    label: "客户工厂",
    width: 120,
    search: { el: "select", order: 3, props: { filterable: true } },
    enum: customerPlants
  },
  {
    prop: "title",
    label: "外部链接",
    width: 100,
    render({ row }) {
      if (["已发布", "已完成", "待反馈"].includes(row.orderStatus)) {
        const url = window.location.origin + "/#/customer-satisfaction/score?id=" + row.uuid;
        return <Link url={url} name="查看" />;
      }
      return "";
    }
  },
  { prop: "customerStaffName", label: "客户联系人", width: 120 },
  { prop: "customerStaffEmail", label: "客户联系邮箱", width: 180 },
  { prop: "salesName", label: "和而泰销售", width: 120 },
  { prop: "salesEmail", label: "销售邮箱", width: 180 },
  { prop: "cqeName", label: "和而泰联系人", width: 120 },
  { prop: "cqeEmail", label: "和而泰联系邮箱", width: 180 },
  {
    prop: "ratingType",
    label: "打分类型",
    width: 120,
    enum: RatingType,
    search: { order: 6, el: "select", props: { filterable: true } }
  },
  {
    prop: "langType",
    label: "语言类型",
    width: 120,
    enum: LangType,
    search: { order: 6, el: "select", props: { filterable: true } }
  },
  { prop: "remarks", label: "备注", width: 120 },
  { prop: "hetFeedback", label: "和而泰反馈", width: 120 },
  {
    prop: "title",
    label: "打分模板",
    width: 160,
    render({ row }) {
      return <LinkModal name={row.title} row={row} onOpenDialog={openPreview} />;
    }
  },
  {
    prop: "surveyTime",
    label: "季度",
    width: 120,
    search: {
      el: "date-picker",
      order: 1,
      render: () => {
        return <DateRange type="quarter" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  { prop: "createBy", label: "创建者", width: 120, search: { el: "input", order: 10 } },
  { prop: "createAt", label: "创建时间", width: 165 },
  { prop: "updateBy", label: "更新者", width: 120 },
  { prop: "updateAt", label: "更新时间", width: 165 }
]);

const getTableList = (params: any) => {
  const { pageNum, pageSize, ...condition } = params;
  if (my.value) {
    condition.onlyShowMine = my.value ? "是" : "否";
  }
  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }

  queryParams = reactive(condition);
  return getCustomerSatOrderList({
    condition,
    pageNum,
    pageSize
  });
};

const resetCounter = ref(0);
const filterDate = ref([]);
const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};
const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const batchDelete = async (id?: number | number[]) => {
  const ids = Array.isArray(id) ? id : [id];
  if (isEmpty(ids)) {
    return ElMessage.error(t(`请选择要删除的数据`));
  }
  await useHandleData(deleteCustomerSatOrder, ids as any, t(`确认删除`));
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

// 导出
const downloadFile = async (ids?: number[]) => {
  const params = { ...queryParams, ...(!isEmpty(ids) ? { ids } : {}) };
  ElMessageBox.confirm(t("确认导出数据?"), t("提示"), { type: "warning" }).then(() =>
    download(exportCustomerSatOrder(params as any))
  );
};

const openCustomerSatOrderModal = (title: string, row: Partial<CustomerSatOrder.Item> = {}) => {
  if (title !== "新增") {
    check();
  }
  const form = title === "新增" ? {} : { ...currentRow.value };
  const params = {
    title,
    isView: title === "查看",
    customerList,
    form,
    api: title === "新增" ? createCustomerSatOrder : title === "编辑" ? editCustomerSatOrder : undefined,
    updateApi: editCustomerSatOrder,
    getTableList: proTable.value?.getTableList
  };
  CustomerSatOrderModalRef.value?.acceptParams(params as any);
};

const openPreview = (row: Partial<CustomerSatOrder.Item> = {}) => {
  if (isEmpty(row)) {
    check();
  }
  const id = isEmpty(row) ? currentRow.value.id : row.id;
  const modalRef = CustomerSatOrderViewModalRef.value;
  if (modalRef) modalRef.acceptParams(id);
};

watch(my, newValue => {
  localStorage.setItem("my", JSON.stringify(newValue));
  proTable.value?.getTableList();
});

onMounted(async () => {
  getLangTemplateList();
  getCustomerList();
  await getBuList();
});
</script>
