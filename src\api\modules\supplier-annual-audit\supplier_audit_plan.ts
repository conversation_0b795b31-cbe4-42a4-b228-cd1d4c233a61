import { ReqPage, ResPage } from "@/api/interface/index";
import { SupplierAuditPlan } from "@/typings/supplier-annual-audit/supplier_audit_plan";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Transfer } from "@/typings/transfer";

const baseUrl = `${API_PREFIX}/supplierAuditPlan`;

// 列表
export const getSupplierAuditPlanList = (params?: ReqPage) => {
  return http.post<ResPage<SupplierAuditPlan.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getSupplierAuditPlanDetail = (id: number) => {
  return http.post<SupplierAuditPlan.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createSupplierAuditPlan = (data: SupplierAuditPlan.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editSupplierAuditPlan = (data: SupplierAuditPlan.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteSupplierAuditPlan = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportSupplierAuditPlan = (params?: SupplierAuditPlan.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importSupplierAuditPlan = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importSupplierAuditPlanTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 审批
export const submitToAudit = (id: number) => {
  return http.post<SupplierAuditPlan.Item>(`${baseUrl}/submitToAudit/${id}`);
};

// 取消
export const cancelSupplierAuditPlan = (id: number) => {
  return http.post<SupplierAuditPlan.Item>(`${baseUrl}/cancel/${id}`);
};

// 退回
export const backSupplierAuditPlan = (params: { id: number; rejectReason: string }) => {
  return http.post<SupplierAuditPlan.Item>(`${baseUrl}/reject`, params);
};

// 延期
export const postponeSupplierAuditPlan = (params: { id: number; planAuditDate: string }) => {
  return http.post<SupplierAuditPlan.Item>(`${baseUrl}/postpone`, params);
};

// 审批通过
export const auditSupplierAuditPlan = (id: number) => {
  return http.post<SupplierAuditPlan.Item>(`${baseUrl}/audit/${id}`);
};

//转办
export const reassignAuditor = (data: Transfer.Item) => {
  return http.post(`${baseUrl}/reassignAuditor`, data);
};

// 审批列表
export const getSupplierAuditPlanApproveList = (params?: ReqPage) => {
  return http.post<ResPage<SupplierAuditPlan.Item>>(`${baseUrl}/listToBeAudited`, params);
};
