import { Staff } from "@/typings/staff";

// 请求响应参数（不包含data）
export interface Result {
  code: string;
  msg: string;
  success: boolean;
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  data: T;
}

// 分页响应参数
export interface ResPage<T> {
  list: T[];
  pageNum: number;
  pageSize: number;
  total: number;
}

// 分页请求参数
export interface ReqPage {
  pageNum: number;
  pageSize: number;
  condition?: any;
}

// 文件上传模块
export namespace Upload {
  export interface ResFileUrl {
    id: number;
    path: string;
    url: string;
    realName: string;
  }
}
export interface StatisticsFour {
  statsType: number;
  startMonth: string;
  endMonth: string;
  plant?: string;
  plantList?: string[];
}

export interface StatisticsReport {
  startDate: string;
  endDate: string;
  statsType?: string;
  supplierType?: string;
  supplierName?: string;
  sqeNo?: string;
  materialName?: string;
  materialTeam?: string;
  plant?: string;
}

// 登录模块
export namespace Login {
  export interface ReqLoginForm {
    username: string;
    password: string;
  }

  export interface ReqRegisterForm {
    name: string;
    jobNum: string;
    password: string;
    confirmPassword: string;
    roleId?: number;
    remark?: string;
  }
  export interface ResLogin {
    access_token: string;
    user: Staff.Item;
  }
  export interface ResAuthButtons {
    [key: string]: string[];
  }
}

// 用户管理模块
export namespace User {
  export interface ReqUserParams extends ReqPage {
    username: string;
    gender: number;
    idCard: string;
    email: string;
    address: string;
    createdTime: string[];
    status: number;
  }
  export interface ResUserList {
    id: string;
    username: string;
    gender: number;
    user: { detail: { age: number } };
    idCard: string;
    email: string;
    address: string;
    createdTime: string;
    status: number;
    avatar: string;
    photo: any[];
    children?: ResUserList[];
  }
  export interface ResStatus {
    userLabel: string;
    userValue: number;
  }
  export interface ResGender {
    genderLabel: string;
    genderValue: number;
  }
  export interface ResDepartment {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
  export interface ResRole {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
}
