import { ReqPage, ResPage, StatisticsFour } from "@/api/interface/index";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Summary } from "@/typings/oba-dip/summary";
import { Statistics } from "@/typings/oba-dip/statistics";
import { ObaDip } from "@/typings/oba-dip/oba_dip";

const baseUrl = `${API_PREFIX}/obaDIP`;

//日报
export const getStatsObaDipDairyReport = (params?: Summary.IQueryParams) => {
  return http.post<ResPage<Summary.Item>>(`${baseUrl}/statsObaDipDairyReport`, params);
};

//导出统计
export const exportQualityCostOrderSummary = (params?: ObaDip.IQueryParams) => {
  return http.post(`${baseUrl}/exportObaDipDairyReport`, params);
};
// 四类质量
export const statsFourClassesDistribution = (params?: StatisticsFour) => {
  return http.post<Statistics.FourData[]>(`${baseUrl}/statsFourClassesDistribution`, params);
};

// OBA DIP抽样合格率
export const statsObaDipLAR = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsObaDipLAR>(`${baseUrl}/statsObaDipLAR`, params);
};

// OBA DIP抽样PPM
export const statsObaDipPPM = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsObaDipLAR>(`${baseUrl}/statsObaDipPPM`, params);
};

// OBA DIP批退PPM
export const statsObaDipLotReject = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnPPM[]>(`${baseUrl}/statsObaDipLotReject`, params);
};

//导出OBA DIP批退PPM
export const exportObaDipLotRejectData = (params?: Statistics.IQueryParams) => {
  return http.post(`${baseUrl}/exportObaDipLotRejectData`, params);
};

// OBA DIP外观+功能不良DPPM
export const statsObaDipNgDPPM = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.statsObaDipNgDPPM>(`${baseUrl}/statsObaDipNgDPPM`, params);
};

// OBA-DIP PPM回顾
export const statsObaDipReview = (params?: Statistics.IQueryParamsReview) => {
  return http.post<Statistics.statsObaDipReview>(`${baseUrl}/statsObaDipReview`, params);
};
