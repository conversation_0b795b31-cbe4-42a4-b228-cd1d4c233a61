<!-- eslint-disable vue/html-closing-bracket-newline -->
<!-- eslint-disable prettier/prettier -->
<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :init-param="initParam" @reset="resetForm()">
      <template #tableHeader>
        <div class="action-buttons">
          <el-button type="primary" @click="openPlmDocumentVersionManageModal()">
            {{ $t("查看明细") }}
          </el-button>
        </div>
      </template>
    </ProTable>
    <PlmDocumentVersionManageModal ref="PlmDocumentVersionManageModalRef" />
  </div>
</template>

<script setup lang="tsx" name="dcc-dcc-document-plm-document">
import { getDccPlmList } from "@/api/modules/dcc/dcc_plm_document";
import PlmDocumentVersionManageModal from "@/views/dcc/dcc-document/plm-document/components/PlmDocumentVersionManageModal.vue";
import { dccPlmList } from "@/typings/dcc/dcc_plm_document";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { ElMessage, ElMessageBox } from "element-plus";
import { DocumentRemove, DocumentChecked } from "@element-plus/icons-vue";
import { useAdminDict, useDict } from "@/hooks/useDict";
import { isEmpty, isEmptyObj } from "@/utils/is";
import { ref, reactive, computed, onMounted } from "vue";
import { useCheckSelectId } from "@/hooks/useCheckSelectId";
import { useI18n } from "vue-i18n";
import DateRange from "@/views/components/DateRange.vue";
import { download } from "@/api/modules/common";
import LinkFile from "@/views/components/LinkFile.vue";
import useUserStore from "@/stores/modules/user";
import RemoteSearchJobNum from "@/views/components/RemoteSearchJobNum.vue";
import LinkModal from "@/views/components/LinkModal.vue";
import { SuperRoleId } from "@/enums/status";

const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const initParam = reactive({});
const { t } = useI18n();
const proTable = ref<ProTableInstance>();
const { check, currentRow } = useCheckSelectId();

const filterDate = ref([]);
const resetCounter = ref(0);

// 表格配置项
const columns = reactive<ColumnProps<dccPlmList.Item>[]>([
  { type: "selection", fixed: "left", width: 40 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "docmentNo",
    label: "文件编号",
    width: 180,
    search: { order: 2, el: "input" }
  },
  {
    prop: "fileName",
    label: "文件名称",
    width: 120,
    search: { el: "input", order: 1 }
  },
  {
    prop: "lifeCyclePhase",
    label: "生命周期阶段",
    width: 120
  },
  {
    prop: "version",
    label: "版本",
    width: 120
  },
  {
    prop: "documentType",
    label: "文件类型",
    width: 120,
    search: { el: "input", order: 6 }
  },
  {
    prop: "versionIssueTime",
    label: "版本发放日期",
    width: 180
  },
  {
    prop: "docmentClassification",
    label: "文档分类",
    width: 120,
    search: { el: "input", order: 5 }
  },
  { prop: "createBy", label: "创建者", width: 120 },
  {
    prop: "createAt",
    label: "创建时间",
    width: 180,
    search: {
      order: 4,
      el: "date-picker",
      render: () => {
        return <DateRange type="date" onDateRangeValue={handleSelectedDates} resetSignal={resetCounter.value}></DateRange>;
      }
    }
  },
  { prop: "updateBy", label: "更新者", width: 120 },
  { prop: "updateAt", label: "更新时间", width: 165 }
]);

const handleSelectedDates = (date: []) => {
  filterDate.value = date;
};

const resetForm = () => {
  resetCounter.value++;
  filterDate.value = [];
};

const getTableList = async (params: any) => {
  const { pageNum, pageSize, ...condition } = params;
  if (!isEmpty(filterDate.value) && filterDate.value.length > 0) {
    condition.startDate = filterDate.value[0];
    condition.endDate = filterDate.value[1];
  }
  return getDccPlmList({
    condition,
    pageNum,
    pageSize
  });
};

const PlmDocumentVersionManageModalRef = ref<InstanceType<typeof PlmDocumentVersionManageModal> | null>(null);

const openPlmDocumentVersionManageModal = (row: Partial<dccPlmList.Item> = {}) => {
  if (isEmptyObj(row)) {
    check();
  }
  const form = !isEmptyObj(row) ? row : { ...currentRow.value };
  const params = {
    itemNumber: form.docmentNo
  };
  console.log("params:", params);
  PlmDocumentVersionManageModalRef.value?.acceptParams(params);
};
</script>

<style scoped>
.document-tabs {
  padding: 10px;
  margin-bottom: 16px;
  background-color: white;
}
.document-tabs ::v-deep .el-tabs__item {
  font-size: 14px; /* 设置字体大小 */
}
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}
.block-tip {
  margin: 10px 0;
  font-weight: bold;
}
.draggable-modal .el-dialog__header {
  cursor: move;
  user-select: none;
}
</style>
