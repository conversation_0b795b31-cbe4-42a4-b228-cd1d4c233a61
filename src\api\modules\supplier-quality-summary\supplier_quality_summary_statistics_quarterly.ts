import { ReqPage, ResPage, StatisticsReport } from "@/api/interface/index";
import { Statistics } from "@/typings/supplier-quality-summary/statistics_quarterly";
import { SupplierInformation } from "@/typings/supplier-quality-summary/supplier_information";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/quarter`;
const baseUrl2 = `${API_PREFIX}/summaryInfo`;

// 获取供应商季度总得分
export const statsTotalScoreData = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.TotalScoreData[]>(`${baseUrl}/statsTotalScoreTrendByQuarter`, params);
};

// 获取供应商客户端投诉数据
export const statsClientComplaintData = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ClientComplaintData[]>(`${baseUrl}/statsClientComplaintTrendByQuarter`, params);
};

// 获取供应商LAR数据
export const statsLarData = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.LarQuarterlyData[]>(`${baseUrl}/statsLarTrendQuarter`, params);
};

// 获取供应商季度品质等级分布数据
export const statsGradeData = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.GradeDistributionData[]>(`${baseUrl}/statsQualityGradeDistributionQuarter`, params);
};

// 获取供应商重大制程品质问题数据
export const statsMajorQualityIssueData = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.MajorQualityIssueData[]>(`${baseUrl}/statsMajorQualityIssueTrendQuarter`, params);
};

// 获取供应商数据列表
export const getSupplierData = (params?: ReqPage) => {
  return http.post<ResPage<SupplierInformation.Item>>(`${baseUrl2}/list`, params);
};
