import { ReqPage, ResPage } from "@/api/interface/index";
import { CustomerDemandCqe } from "@/typings/customer-demand/customer_demand_cqe";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/setCqe`;

// 列表
export const getCustomerDemandCqeList = (params?: ReqPage) => {
  return http.post<ResPage<CustomerDemandCqe.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getCustomerDemandCqeDetail = (id: number) => {
  return http.post<CustomerDemandCqe.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createCustomerDemandCqe = (data: CustomerDemandCqe.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editCustomerDemandCqe = (data: CustomerDemandCqe.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteCustomerDemandCqe = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportCustomerDemandCqe = (params?: CustomerDemandCqe.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importCustomerDemandCqe = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importCustomerDemandCqeTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
