import { ReqPage, ResPage } from "@/api/interface/index";
import { QualityRewardPunishmentRule } from "@/typings/quality-reward-punishment/quality_reward_punishment_rule";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/QualityRewardPunishmentRule`;

// 列表
export const getQualityRewardPunishmentRuleList = (params?: ReqPage) => {
  return http.post<ResPage<QualityRewardPunishmentRule.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getQualityRewardPunishmentRuleDetail = (id: number) => {
  return http.post<QualityRewardPunishmentRule.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createQualityRewardPunishmentRule = (data: QualityRewardPunishmentRule.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editQualityRewardPunishmentRule = (data: QualityRewardPunishmentRule.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteQualityRewardPunishmentRule = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportQualityRewardPunishmentRule = (params?: QualityRewardPunishmentRule.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importQualityRewardPunishmentRule = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importQualityRewardPunishmentRuleTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};
