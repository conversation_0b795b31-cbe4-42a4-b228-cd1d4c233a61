import { ReqPage, ResPage } from "@/api/interface/index";
import { LessonLearnOrder } from "@/typings/lesson-learn/lesson_learn_order";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${API_PREFIX}/lessonLearnOrder`;

// 列表
export const getLessonLearnOrderList = (params?: ReqPage) => {
  return http.post<ResPage<LessonLearnOrder.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getLessonLearnOrderDetail = (id: number) => {
  return http.post<LessonLearnOrder.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createLessonLearnOrder = (data: LessonLearnOrder.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editLessonLearnOrder = (data: LessonLearnOrder.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteLessonLearnOrder = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportLessonLearnOrder = (params?: LessonLearnOrder.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importLessonLearnOrder = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importLessonLearnOrderTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 提交审批
export const submitToAudit = (id: number) => {
  return http.post<LessonLearnOrder.Item>(`${baseUrl}/submit/${id}`);
};

// 一级审批通过
export const primaryPass = (id: number) => {
  return http.post<LessonLearnOrder.Item>(`${baseUrl}/primaryPass/${id}`);
};

// 一级审批退回
export const primaryDismiss = (data: LessonLearnOrder.Item) => {
  return http.post<LessonLearnOrder.Item>(`${baseUrl}/primaryDismiss`, data);
};

// 二级审批通过
export const finalPass = (id: number) => {
  return http.post<LessonLearnOrder.Item>(`${baseUrl}/finalPass/${id}`);
};

// 二级审批退回
export const finalDismiss = (data: LessonLearnOrder.Item) => {
  return http.post<LessonLearnOrder.Item>(`${baseUrl}/finalDismiss`, data);
};
// 审批列表
export const listToBeAudited = (params?: ReqPage) => {
  return http.post<ResPage<LessonLearnOrder.Item>>(`${baseUrl}/listToBeAudited`, params);
};
