<template>
  <el-dialog id="import-het" v-model="dialogVisible" :title="parameter.title" :destroy-on-close="true" width="780px" draggable>
    <el-form
      class="drawer-multiColumn-form"
      label-suffix=" :"
      :rules="rules"
      :validate-on-rule-change="false"
      label-width="120"
      ref="formRef"
      :show-message="isZh"
      :model="form"
    >
      <el-form-item>
        <el-button type="primary" :icon="Download" @click="downloadTemp"> {{ $t("下载模板") }} </el-button>
        &nbsp;&nbsp;&nbsp;&nbsp;
        {{ $t("下载模板后，请勿修改表头，按要求填写数据") }}
      </el-form-item>
      <el-form-item :label="$t('上传文件')" prop="file">
        <el-upload
          v-model:file-list="form.file"
          ref="uploadRef"
          action="#"
          class="upload"
          :drag="true"
          :limit="excelLimit"
          :multiple="false"
          :show-file-list="true"
          :http-request="uploadExcel"
          :before-upload="beforeExcelUpload"
          :on-exceed="handleExceed"
          :on-success="excelUploadSuccess"
          :on-error="excelUploadError"
          :auto-upload="false"
          :accept="parameter.fileType!.join(',')"
        >
          <slot name="empty">
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              {{ $t("将文件拖到此处，或点击上传") }}
            </div>
          </slot>
          <template #tip>
            <slot name="tip">
              <div class="el-upload__tip">{{ $t("请上传 .xls , .xlsx 标准格式文件，文件最大为") }} {{ parameter.fileSize }}M</div>
            </slot>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleUpload">{{ $t("导入") }}</el-button>
        <el-button @click="closeDialog">{{ $t("关闭") }}</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts" name="ImportModal">
import { ref } from "vue";
import { Download } from "@element-plus/icons-vue";
import {
  ElNotification,
  UploadRequestOptions,
  UploadRawFile,
  FormInstance,
  UploadProps,
  genFileId,
  ElMessageBox
} from "element-plus";
import { useI18n } from "vue-i18n";
import { download } from "@/api/modules/common";
import { useLanguageCode } from "@/hooks/useLanguageCode";
import * as XLSX from "xlsx";
const { isZh } = useLanguageCode();

export interface ExcelParameterProps {
  title: string; // 标题
  fileSize?: number; // 上传文件的大小
  fileType?: File.ExcelMimeType[]; // 上传文件的类型
  tempApi?: (params: any) => Promise<any>; // 下载模板的Api
  importApi?: (params: any) => Promise<any>; // 批量导入的Api
  getTableList?: () => void; // 获取表格数据的Api
  onImportData?: (data: any[]) => void; // 导入数据回调函数
}
const formRef = ref<FormInstance>();
const form = ref({
  plant: "",
  startDate: "",
  endDate: "",
  file: []
});
// 是否覆盖数据
const isCover = ref(false);

// 最大文件上传数
const excelLimit = ref(1);
// dialog状态
const dialogVisible = ref(false);
const { t } = useI18n();
// 父组件传过来的参数
const parameter = ref<ExcelParameterProps>({
  title: "",
  fileSize: 100,
  fileType: ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
});

// 上传组件的引用
const uploadRef = ref<InstanceType<(typeof import("element-plus"))["ElUpload"]> | null>(null);

// 接收父组件参数
const acceptParams = (params: ExcelParameterProps) => {
  parameter.value = { ...parameter.value, ...params };
  form.value.file = [];
  uploadRef.value?.clearFiles();
  dialogVisible.value = true;
};

// Excel 导入模板下载
const downloadTemp = async () => {
  if (!parameter.value.tempApi) return;
  download(parameter.value.tempApi, true);
};

// 文件上传
const uploadExcel = async (param: UploadRequestOptions) => {
  try {
    const reader = new FileReader();
    reader.onload = e => {
      const data = new Uint8Array(e.target?.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: "array" });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // 跳过表头，从第二行开始读取数据
      const excelData = jsonData
        .slice(1)
        .map((row: any, index: number) => {
          return {
            index: index + 1,
            responsiblePersonName: row[1] || "",
            responsiblePersonNo: row[0] || "",
            responsibleDept: row[3] || "",
            issueLevel: row[4] || "",
            issueCategoryByManagementElem: row[5] || "",
            issueCategoryByProcess: row[6] || "",
            issuePhotoUrl: "",
            dueDate: row[7] || "",
            auditorName: row[9] || "",
            auditorNo: row[10] || "",
            issueDescription: row[8] || "",
            orderId: 0, // 这个值需要从父组件传入
            responsibleDeptId: null,
            responsiblePersonEmail: row[2] || "",
            auditorEmail: row[11] || ""
          };
        })
        .filter(item => item.responsiblePersonName || item.issueDescription); // 过滤空行

      if (parameter.value.onImportData) {
        parameter.value.onImportData(excelData);
      }

      ElNotification({
        title: t("温馨提示"),
        message: `${t("导入成功")}，共导入 ${excelData.length} 条数据`,
        type: "success"
      });

      dialogVisible.value = false;
    };
    reader.readAsArrayBuffer(param.file);
  } catch (error) {
    ElNotification({
      title: t("温馨提示"),
      message: t("文件读取失败，请检查文件格式"),
      type: "error"
    });
  } finally {
    form.value.file = [];
    uploadRef.value?.clearFiles();
  }
};

/**
 * @description 文件上传之前判断
 * @param file 上传的文件
 * */
const beforeExcelUpload = (file: UploadRawFile) => {
  const isExcel = parameter.value.fileType!.includes(file.type as File.ExcelMimeType);
  const fileSize = file.size / 1024 / 1024 < parameter.value.fileSize!;
  if (!isExcel)
    ElNotification({
      title: t("温馨提示"),
      message: t("上传文件只能是 xls / xlsx 格式！"),
      type: "warning"
    });
  if (!fileSize)
    setTimeout(() => {
      ElNotification({
        title: t("温馨提示"),
        message: `${t("上传文件大小不能超过")} ${parameter.value.fileSize}MB！`,
        type: "warning"
      });
    }, 0);
  return isExcel && fileSize;
};

// 文件数超出提示
const handleExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

// 上传错误提示
const excelUploadError = () => {
  // ElNotification({
  //   title: t("温馨提示"),
  //   message: `${t("批量添加")}${parameter.value.title}${t("失败，请您重新上传！")}`,
  //   type: "error"
  // });
};

// 上传成功提示
const excelUploadSuccess = () => {
  // ElNotification({
  //   title: t("温馨提示"),
  //   message: `${t("批量添加")}${parameter.value.title}${t("成功！")}`,
  //   type: "success"
  // });
};

const rules = {
  file: [{ required: true, message: "请选择文件", trigger: "blur" }]
};

// 触发上传
const handleUpload = async () => {
  formRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      ElMessageBox.confirm(t("确定导入数据"), t("提示"), {
        confirmButtonText: t("确认"),
        cancelButtonText: t("取消"),
        dangerouslyUseHTMLString: true,
        type: ""
      }).then(async () => {
        uploadRef.value?.submit();
      });
    } catch (error) {
      console.log(error);
    }
  });
};

const closeDialog = () => {
  dialogVisible.value = false;
};

defineExpose({
  acceptParams
});
</script>

<style>
#import-het .el-radio-group {
  display: block;
}
#import-het .el-radio {
  margin-right: 8px;
}
</style>
