import { ReqPage, ResPage } from "@/api/interface/index";
import { FaSmtProblem } from "@/typings/fa/fa_smt_problem";
import http from "@/api";
import { API_PREFIX } from "@/api/config/servicePort";
import { Statistics } from "@/typings/fa/statistics";

const baseUrl = `${API_PREFIX}/faSMTProblem`;

// 列表
export const getFaSmtProblemList = (params?: ReqPage) => {
  return http.post<ResPage<FaSmtProblem.Item>>(`${baseUrl}/list`, params);
};

// 详情
export const getFaSmtProblemDetail = (id: number) => {
  return http.post<FaSmtProblem.Item>(`${baseUrl}/get/${id}`);
};

// 新增
export const createFaSmtProblem = (data: FaSmtProblem.Item) => {
  return http.post(`${baseUrl}/create`, data);
};

// 修改
export const editFaSmtProblem = (data: FaSmtProblem.Item) => {
  return http.post(`${baseUrl}/modify`, data);
};

// 删除
export const deleteFaSmtProblem = (ids: number[]) => {
  return http.post(`${baseUrl}/del`, { ids });
};

// 导出
export const exportFaSmtProblem = (params?: FaSmtProblem.IQueryParams) => {
  return http.post(`${baseUrl}/exportListData`, params);
};

// 导入
export const importFaSmtProblem = (formData: FormData) => {
  return http.post(`${baseUrl}/uploadExcelData`, formData);
};

//下载模板
export const importFaSmtProblemTpl = () => {
  return http.post(`${baseUrl}/exportTmpl`);
};

// 首件SMT问题表 首件SMT问题分析
export const statsFaSmtProblem = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsFaSmtProblem`, params);
};

export const statsFaReport = (params?: Statistics.IQueryParams) => {
  return http.post<Statistics.ReturnData>(`${baseUrl}/statsFaReport`, params);
};
