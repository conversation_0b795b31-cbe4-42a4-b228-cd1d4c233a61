import { ReqPage, ResPage } from "@/api/interface/index";
import http from "@/api";
import { Language } from "@/typings/language";
import { ADMIN_API_PREFIX } from "@/api/config/servicePort";

const baseUrl = `${ADMIN_API_PREFIX}/language`;

export const getLanguageList = (params?: ReqPage) => {
  return http.get<ResPage<Language.Item>>(`${baseUrl}/code/list`, params);
};

export const getAllLanguageList = (params?: ReqPage) => {
  return http.get<Language.Item[]>(`${baseUrl}/code/listAll`, params);
};

export const getLanguageDetail = (fileId: number) => {
  return http.get<Language.Item>(`${baseUrl}/${fileId}`);
};

export const createLanguage = (data: Language.Item) => {
  return http.post(`${baseUrl}`, data);
};

export const editLanguage = (data: Language.Item) => {
  return http.put(`${baseUrl}`, data);
};

export const deleteLanguage = (data: { ids: number[] }) => {
  return http.delete(`${baseUrl}/code`, data);
};

export const getImportTemplate = () => {
  return http.get(`${baseUrl}/import_template`);
};
